#!/usr/bin/env python3
import os
import sys
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold
import requests
import re

# 设置代理
os.environ["HTTP_PROXY"] = "http://127.0.0.1:33210"
os.environ["HTTPS_PROXY"] = "http://127.0.0.1:33210"

# 设置API密钥
API_KEY = "AIzaSyCyiRd9Gx2nUU_iBb01OOFZ9fBVnYfdZYE"
genai.configure(api_key=API_KEY)

def convert_google_drive_url(url):
    """转换Google Drive共享链接为直接下载链接"""
    # 检查是否是Google Drive链接
    if 'drive.google.com' in url:
        # 尝试提取文件ID
        file_id_match = re.search(r'/d/([a-zA-Z0-9_-]+)', url)
        if file_id_match:
            file_id = file_id_match.group(1)
            # 使用Google Drive查看器URL格式
            return f"https://drive.google.com/file/d/{file_id}/view"
        else:
            # 尝试另一种格式的链接
            file_id_match = re.search(r'id=([a-zA-Z0-9_-]+)', url)
            if file_id_match:
                file_id = file_id_match.group(1)
                return f"https://drive.google.com/file/d/{file_id}/view"
    
    # 如果不是Google Drive链接或无法提取ID，返回原始URL
    return url

def get_mime_type_from_url(url):
    """根据URL猜测MIME类型"""
    if 'youtube.com' in url or 'youtu.be' in url:
        return "video/mp4"
    elif url.endswith('.mp4'):
        return "video/mp4"
    elif url.endswith('.mpeg') or url.endswith('.mpg'):
        return "video/mpeg"
    elif url.endswith('.webm'):
        return "video/webm"
    elif url.endswith('.avi'):
        return "video/x-msvideo"
    elif url.endswith('.mov'):
        return "video/quicktime"
    else:
        # 默认MIME类型
        return "video/mp4"

def analyze_video(question, video_url=None, model="gemini-2.0-flash"):
    """使用Gemini分析视频或回答问题"""
    
    try:
        # 设置安全配置
        safety_settings = {
            HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
        }
        
        # 获取模型实例
        model = genai.GenerativeModel(model_name=model, safety_settings=safety_settings)
        
        if video_url:
            # 处理Google Drive链接
            processed_url = convert_google_drive_url(video_url)
            
            if processed_url != video_url:
                print(f"使用原始链接")
                
            # 获取MIME类型
            mime_type = get_mime_type_from_url(processed_url)
            print(f"使用MIME类型: {mime_type}")
            
            try:
                # 如果提供了视频URL，创建包含视频和问题的请求
                response = model.generate_content([
                    {
                        "file_data": {
                            "file_uri": processed_url,
                            "mime_type": mime_type
                        }
                    },
                    question
                ])
                # 获取回复文本
                return response.text
                
            except Exception as video_error:
                # 如果视频分析失败，尝试纯文本回答
                print(f"视频分析失败: {str(video_error)}")
                print("尝试使用纯文本回答...")
                
                # 将视频URL添加到问题中
                enhanced_question = f"以下是视频链接: {video_url}\n\n{question}\n\n请注意：我无法直接访问视频内容，但我会尽力根据您的问题提供帮助。"
                response = model.generate_content(enhanced_question)
                return response.text
        else:
            # 仅文本问题
            response = model.generate_content(question)
            return response.text
        
    except Exception as e:
        print(f"API调用错误: {str(e)}")
        return f"无法获取回答。错误: {str(e)}"

def main():
    # 解析命令行参数
    video_url = None
    question = ""
    
    if len(sys.argv) > 1:
        # 检查是否有视频URL参数 (--video 或 -v)
        for i, arg in enumerate(sys.argv[1:], 1):
            if arg in ["--video", "-v"] and i < len(sys.argv):
                video_url = sys.argv[i+1]
                # 从参数列表中移除视频URL和它的标志
                sys.argv.remove(arg)
                sys.argv.remove(video_url)
                break
        
        # 剩余参数作为问题
        if len(sys.argv) > 1:
            question = " ".join(sys.argv[1:])
    
    # 如果没有通过命令行提供问题，则从输入中获取
    if not question:
        question = input("请输入您的问题: ")
    
    # 如果没有通过命令行提供视频URL，询问用户是否要分析视频
    if not video_url:
        use_video = input("是否要分析视频? (y/n): ").lower()
        if use_video == 'y':
            video_url = input("请输入视频URL: ")
    
    print("正在发送请求到Gemini API...")
    print("使用代理: HTTP:33210")
    answer = analyze_video(question, video_url)
    print("\n回答:")
    print(answer)

if __name__ == "__main__":
    main()