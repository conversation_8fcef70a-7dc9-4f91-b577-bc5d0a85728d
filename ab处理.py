import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import random
import subprocess
import shutil
import json
import time
import threading
import sys
import math
import numpy as np
# 移除OpenCV导入，代码中未使用

# 全局配置subprocess启动信息
STARTUPINFO = None
if os.name == 'nt':  # Windows系统
    STARTUPINFO = subprocess.STARTUPINFO()
    STARTUPINFO.dwFlags |= subprocess.STARTF_USESHOWWINDOW
    STARTUPINFO.wShowWindow = subprocess.SW_HIDE
    # 创建一个全局的subprocess配置
    DEFAULT_SUBPROCESS_PARAMS = {
        'startupinfo': STARTUPINFO,
        'creationflags': subprocess.CREATE_NO_WINDOW | subprocess.DETACHED_PROCESS,
        'shell': False
    }
else:
    DEFAULT_SUBPROCESS_PARAMS = {}

# 配置ExifTool路径
EXIFTOOL_PATH = "C:\\exiftool-13.30_64\\exiftool.exe"

def get_config_path():
    if getattr(sys, 'frozen', False):  # exe
        base_dir = os.path.dirname(sys.executable)
    else:  # py
        base_dir = os.path.dirname(os.path.abspath(__file__))
    return os.path.join(base_dir, "video_processor_config.json")

class VideoProcessor:
    def __init__(self, log_text):
        self.log_text = log_text  # 保存日志文本框引用，用于输出处理过程信息
        
        # 验证ExifTool路径
        if not os.path.exists(EXIFTOOL_PATH):
            self.log_text.insert(tk.END, f"警告: ExifTool路径不存在: {EXIFTOOL_PATH}\n")
            self.log_text.insert(tk.END, "元数据处理可能会失败，请确保ExifTool已正确安装\n")

    def get_video_info(self, video_path):
        try:
            # 首先检查文件是否存在
            if not os.path.exists(video_path):
                self.log_text.insert(tk.END, f"获取视频信息失败: 文件不存在 {video_path}\n")
                return None
                
            # 检查文件大小
            file_size = os.path.getsize(video_path)
            if file_size == 0:
                self.log_text.insert(tk.END, f"获取视频信息失败: 文件大小为0 {video_path}\n")
                return None
                
            command = [
                'ffprobe', '-v', 'error',
                '-select_streams', 'v:0',
                '-show_entries', 'stream=width,height,duration,r_frame_rate',
                '-of', 'json',
                video_path
            ]
            result = subprocess.run(command, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)
            
            # 检查ffprobe是否成功
            if result.returncode != 0:
                self.log_text.insert(tk.END, f"ffprobe命令失败: {result.stderr}\n")
                return None
                
            data = json.loads(result.stdout)
            
            # 检查JSON数据是否包含streams
            if 'streams' not in data or not data['streams']:
                self.log_text.insert(tk.END, f"获取视频信息失败: 没有找到视频流\n")
                return None
                
            stream = data['streams'][0]
            
            # 检查必要的字段是否存在
            if 'r_frame_rate' not in stream:
                self.log_text.insert(tk.END, f"获取视频信息失败: 没有帧率信息\n")
                return None
                
            fps = eval(stream['r_frame_rate'])
            
            return {
                'width': stream.get('width', 0),
                'height': stream.get('height', 0),
                'fps': fps,
                'duration': float(stream.get('duration', 0))
            }
        except json.JSONDecodeError as e:
            self.log_text.insert(tk.END, f"解析视频信息JSON失败: {str(e)}\n")
            return None
        except Exception as e:
            self.log_text.insert(tk.END, f"获取视频信息失败: {str(e)}\n")
            import traceback
            self.log_text.insert(tk.END, f"详细错误: {traceback.format_exc()}\n")
            return None

    def verify_metadata(self, video_path):
        try:
            cmd = [
                'ffprobe', '-v', 'quiet',
                '-print_format', 'json',
                '-show_format', '-show_streams',
                video_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
            metadata = json.loads(result.stdout)

            # 获取视频元数据
            tags = metadata.get('format', {}).get('tags', {})

            # 准备显示信息
            info = [
                f"设备型号: {tags.get('com.apple.quicktime.model', '未知')}",
                f"iOS版本: {tags.get('com.apple.quicktime.software', '未知')}",
                f"创建时间: {tags.get('creation_time', '未知')}",
                f"地理位置: {tags.get('location', '未知')}",
                f"视频时长: {metadata.get('format', {}).get('duration', '未知')}秒",
                f"编码器: {tags.get('encoder', '未知')}"
            ]

            # 输出到日志
            self.log_text.insert(tk.END, "\n处理后的视频信息:\n" + "\n".join(info) + "\n")
            self.log_text.see(tk.END)

            return True

        except Exception as e:
            self.log_text.insert(tk.END, f"验证元数据失败: {str(e)}\n")
            return False
    def get_total_frames(self, video_path):
        try:
            info = self.get_video_info(video_path)
            if info:
                return int(info['fps'] * info['duration'])
            return 0
        except Exception as e:
            self.log_text.insert(tk.END, f"获取总帧数失败: {str(e)}\n")
            return 0

    def extract_random_frame(self, video_path, output_path):
        try:
            info = self.get_video_info(video_path)  # 使用 get_video_info 来获取视频信息
            if not info:
                return False

            total_frames = int(info['fps'] * info['duration'])  # 基于视频信息计算总帧数
            if total_frames == 0:
                return False

            frame_number = random.randint(0, total_frames - 1)
            command = [
                'ffmpeg', '-y',
                '-i', video_path,
                '-vf', f'select=eq(n\,{frame_number})',
                '-vframes', '1',
                output_path
            ]
            subprocess.run(command, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
            return True
        except Exception as e:
            self.log_text.insert(tk.END, f"提取帧失败: {str(e)}\n")
            return False

    def create_triple_screen_video(self, video_a_path, video_b_paths, output_path, output_fps=None):
        try:
            info_a = self.get_video_info(video_a_path)
            if not info_a:
                return False
                
            # 获取视频B的信息
            info_b1 = self.get_video_info(video_b_paths[0])
            info_b2 = self.get_video_info(video_b_paths[1])
            if not info_b1 or not info_b2:
                return False
                
            # 如果没有指定输出帧率，则使用视频A的帧率
            if output_fps is None:
                output_fps = info_a["fps"]
                
            self.log_text.insert(tk.END, f"三连屏处理：使用帧率 {output_fps} fps 作为输出帧率\n")
            
            # 获取视频总帧数
            frames_a = self.get_total_frames(video_a_path)
            frames_b1 = self.get_total_frames(video_b_paths[0])
            frames_b2 = self.get_total_frames(video_b_paths[1])
            
            # 计算循环次数，确保视频B能覆盖视频A的时长
            loop_count_b1 = max(1, math.ceil(info_a["duration"] / info_b1["duration"]))
            loop_count_b2 = max(1, math.ceil(info_a["duration"] / info_b2["duration"]))
            
            self.log_text.insert(tk.END, f"视频A时长: {info_a['duration']:.2f}秒，总帧数: {frames_a}\n")
            self.log_text.insert(tk.END, f"上方视频B时长: {info_b1['duration']:.2f}秒，总帧数: {frames_b1}，循环 {loop_count_b1} 次\n")
            self.log_text.insert(tk.END, f"下方视频B时长: {info_b2['duration']:.2f}秒，总帧数: {frames_b2}，循环 {loop_count_b2} 次\n")
            
            # 创建临时文件夹用于处理
            temp_dir = os.path.dirname(output_path)
            os.makedirs(temp_dir, exist_ok=True)
            
            # 为上下视频B创建临时文件
            temp_b1 = os.path.join(temp_dir, "temp_b1_extended.mp4")
            temp_b2 = os.path.join(temp_dir, "temp_b2_extended.mp4")
            
            # 尝试使用concat demuxer直接复制视频B到所需时长（更快）
            use_concat_method = True
            command = None
            
            try:
                # 直接使用concat demuxer复制视频B到所需时长
                self.log_text.insert(tk.END, "直接复制视频B到所需时长，加快处理速度...\n")

                # 验证输入视频文件
                for video_path in video_b_paths:
                    if not os.path.exists(video_path):
                        raise Exception(f"视频B文件不存在: {video_path}")
                    if os.path.getsize(video_path) == 0:
                        raise Exception(f"视频B文件大小为0: {video_path}")

                    # 快速验证视频文件是否可读
                    test_cmd = ['ffprobe', '-v', 'error', '-select_streams', 'v:0', '-show_entries', 'stream=duration', '-of', 'csv=p=0', video_path]
                    test_result = subprocess.run(test_cmd, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)
                    if test_result.returncode != 0:
                        raise Exception(f"视频B文件无法读取: {video_path}")

                # 创建上方视频B的concat文件，使用UTF-8编码和规范化路径
                concat_file_b1 = os.path.join(temp_dir, "concat_b1.txt")
                with open(concat_file_b1, 'w', encoding='utf-8', newline='\n') as f:
                    # 使用双反斜杠转义路径，确保Windows兼容性
                    video_path = os.path.abspath(video_b_paths[0])
                    # 转换为正斜杠并确保路径正确转义
                    normalized_path = video_path.replace('\\', '/').replace("'", "\\'")
                    for _ in range(loop_count_b1):
                        f.write(f"file '{normalized_path}'\n")

                # 创建下方视频B的concat文件，使用UTF-8编码和规范化路径
                concat_file_b2 = os.path.join(temp_dir, "concat_b2.txt")
                with open(concat_file_b2, 'w', encoding='utf-8', newline='\n') as f:
                    # 使用双反斜杠转义路径，确保Windows兼容性
                    video_path = os.path.abspath(video_b_paths[1])
                    # 转换为正斜杠并确保路径正确转义
                    normalized_path = video_path.replace('\\', '/').replace("'", "\\'")
                    for _ in range(loop_count_b2):
                        f.write(f"file '{normalized_path}'\n")

                # 使用concat demuxer生成上方视频B的延长版本
                self.log_text.insert(tk.END, f"正在处理上方视频B，循环 {loop_count_b1} 次，调整帧率为 {output_fps} fps...\n")

                # 添加更多的FFmpeg参数来提高兼容性，并调整帧率
                cmd_b1 = [
                    'ffmpeg', '-y',
                    '-f', 'concat',
                    '-safe', '0',
                    '-protocol_whitelist', 'file,pipe',
                    '-i', concat_file_b1,
                    '-c:v', 'libx264',  # 重新编码以调整帧率
                    '-r', str(output_fps),  # 设置输出帧率
                    '-c:a', 'aac',  # 音频编码
                    '-avoid_negative_ts', 'make_zero',
                    '-fflags', '+genpts',
                    temp_b1
                ]

                self.log_text.insert(tk.END, f"执行命令: {' '.join(cmd_b1)}\n")
                result_b1 = subprocess.run(cmd_b1, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)

                if result_b1.returncode != 0:
                    self.log_text.insert(tk.END, f"FFmpeg错误输出: {result_b1.stderr}\n")
                    raise Exception(f"上方视频B处理失败，返回码: {result_b1.returncode}")

                # 使用concat demuxer生成下方视频B的延长版本
                self.log_text.insert(tk.END, f"正在处理下方视频B，循环 {loop_count_b2} 次，调整帧率为 {output_fps} fps...\n")

                cmd_b2 = [
                    'ffmpeg', '-y',
                    '-f', 'concat',
                    '-safe', '0',
                    '-protocol_whitelist', 'file,pipe',
                    '-i', concat_file_b2,
                    '-c:v', 'libx264',  # 重新编码以调整帧率
                    '-r', str(output_fps),  # 设置输出帧率
                    '-c:a', 'aac',  # 音频编码
                    '-avoid_negative_ts', 'make_zero',
                    '-fflags', '+genpts',
                    temp_b2
                ]

                self.log_text.insert(tk.END, f"执行命令: {' '.join(cmd_b2)}\n")
                result_b2 = subprocess.run(cmd_b2, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)

                if result_b2.returncode != 0:
                    self.log_text.insert(tk.END, f"FFmpeg错误输出: {result_b2.stderr}\n")
                    raise Exception(f"下方视频B处理失败，返回码: {result_b2.returncode}")
                
                self.log_text.insert(tk.END, f"视频B帧率调整完成（{output_fps} fps），开始三连屏合成...\n")

                # 构建FFmpeg命令，使用预先延长并调整帧率的视频B
                command = [
                    'ffmpeg', '-y',
                    '-i', video_a_path,
                    '-i', temp_b1,  # 已调整为目标帧率
                    '-i', temp_b2,  # 已调整为目标帧率
                    '-filter_complex',
                    f'[1:v]scale={info_a["width"]}:{info_a["height"]}[top];'
                    f'[2:v]scale={info_a["width"]}:{info_a["height"]}[bottom];'
                    f'[top][0:v][bottom]vstack=inputs=3[v]',
                    '-map', '[v]',
                    '-map', '0:a',
                    '-c:v', 'libx264',
                    '-c:a', 'aac',
                    '-r', str(output_fps),
                    '-shortest',
                    output_path
                ]
            except Exception as e:
                use_concat_method = False
                self.log_text.insert(tk.END, f"预处理视频B失败: {str(e)}，回退到使用loop滤镜...\n")
                # 清理可能创建的临时文件
                for temp_file in [temp_b1, temp_b2]:
                    if os.path.exists(temp_file):
                        try:
                            os.remove(temp_file)
                        except:
                            pass

            # 如果concat方法失败，回退到使用loop滤镜
            if not use_concat_method:
                self.log_text.insert(tk.END, f"使用loop滤镜方法，同时调整上下视频帧率为 {output_fps} fps...\n")
                command = [
                    'ffmpeg', '-y',
                    '-i', video_a_path,
                    '-i', video_b_paths[0],
                    '-i', video_b_paths[1],
                    '-filter_complex',
                    f'[1:v]loop=loop={loop_count_b1}:size={frames_b1}:start=0,fps={output_fps},scale={info_a["width"]}:{info_a["height"]}[top];'
                    f'[2:v]loop=loop={loop_count_b2}:size={frames_b2}:start=0,fps={output_fps},scale={info_a["width"]}:{info_a["height"]}[bottom];'
                    f'[top][0:v][bottom]vstack=inputs=3[v]',
                    '-map', '[v]',
                    '-map', '0:a',
                    '-c:v', 'libx264',
                    '-c:a', 'aac',
                    '-r', str(output_fps),
                    '-shortest',
                    output_path
                ]

            subprocess.run(command, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
            return True
        except Exception as e:
            self.log_text.insert(tk.END, f"创建三连屏视频失败: {str(e)}\n")
            return False

    def process_audio(self, video_a_path, video_b_folder, output_path, audio_folder=None, bg_volume=0.06):
        try:
            self.log_text.insert(tk.END, "开始处理音频...\n")
            temp_folder = os.path.join(os.path.dirname(output_path), "temp_audio")
            os.makedirs(temp_folder, exist_ok=True)

            # 检测硬件加速
            hw_accel, hw_device = self.detect_hardware_acceleration()
            hw_params = []
            if hw_accel:
                hw_params = ['-hwaccel', hw_accel, '-hwaccel_device', hw_device]

            temp_audio_a = os.path.join(temp_folder, "audio_a.wav")
            temp_audio_b = os.path.join(temp_folder, "audio_b.wav")
            temp_audio_mixed = os.path.join(temp_folder, "mixed.wav")

            try:
                # 1. 提取视频A的音频并应用语音变声效果和轻微回声
                # 随机选择变声效果类型（1-3）
                voice_type = random.randint(1, 3)
                
                if voice_type == 1:
                    # 年轻女声效果
                    voice_effects = (
                        'rubberband=pitch=1.15,' +  # 只调整音调，不改变速度（降低音调变化）
                        'equalizer=f=200:t=h:w=100:g=-2,' +  # 降低低频衰减
                        'equalizer=f=1500:t=h:w=100:g=3,' +  # 降低中高频增强
                        'equalizer=f=4000:t=h:w=100:g=3,' +  # 降低高频增强
                        'aecho=in_gain=1:out_gain=0.3:delays=50:decays=0.2'  # 添加轻微回声
                    )
                elif voice_type == 2:
                    # 成熟男声效果
                    voice_effects = (
                        'rubberband=pitch=0.92,' +  # 只调整音调，不改变速度（降低音调变化）
                        'equalizer=f=100:t=h:w=100:g=2,' +  # 降低低频增强
                        'equalizer=f=1000:t=h:w=100:g=0,' +  # 保持中频平衡
                        'equalizer=f=3000:t=h:w=100:g=-1,' +  # 降低高频衰减
                        'aecho=in_gain=1:out_gain=0.3:delays=50:decays=0.2'  # 添加轻微回声
                    )
                else:
                    # 中性声效果
                    voice_effects = (
                        'rubberband=pitch=1.04,' +  # 只调整音调，不改变速度（降低音调变化）
                        'equalizer=f=200:t=h:w=100:g=-1,' +  # 降低低频衰减
                        'equalizer=f=1200:t=h:w=100:g=2,' +  # 降低中频增强
                        'equalizer=f=3500:t=h:w=100:g=2,' +  # 降低高频增强
                        'aecho=in_gain=1:out_gain=0.3:delays=50:decays=0.2'  # 添加轻微回声
                    )

                # 2. 提取并处理主音频
                cmd_extract_a = [
                    'ffmpeg', '-y'
                ] + hw_params + [
                    '-i', video_a_path,
                    '-vn',
                    '-filter_complex',
                    voice_effects + ',volume=2.0',  # 简化音频处理链
                    '-acodec', 'pcm_s16le',
                    '-ar', '48000',  # 保持固定采样率
                    '-ac', '2',
                    temp_audio_a
                ]
                subprocess.run(cmd_extract_a, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)

                # 3. 处理背景音乐
                # 首先检查自定义音频文件夹
                background_audio_path = ""
                if audio_folder and os.path.isdir(audio_folder):
                    # 如果指定了音频文件夹且存在，从中随机选择一个音频文件
                    # 先检查常规音频文件
                    audio_files = [f for f in os.listdir(audio_folder) 
                                   if f.lower().endswith(('.mp3', '.wav', '.aac', '.flac', '.ogg'))]
                    
                    # 检查MP4文件，如果有MP4文件则提取音频
                    mp4_files = [f for f in os.listdir(audio_folder) 
                                if f.lower().endswith('.mp4')]
                    
                    if mp4_files:
                        self.log_text.insert(tk.END, f"从音频文件夹中找到 {len(mp4_files)} 个MP4文件，将提取音频\n")
                        
                        # 随机选择一个MP4文件
                        selected_mp4 = os.path.join(audio_folder, random.choice(mp4_files))
                        
                        # 创建临时音频文件
                        temp_extracted_audio = os.path.join(temp_folder, "extracted_audio.wav")
                        
                        try:
                            # 从MP4提取音频
                            extract_cmd = [
                                'ffmpeg', '-y',
                                '-i', selected_mp4,
                                '-vn',  # 不要视频
                                '-acodec', 'pcm_s16le',
                                '-ar', '48000',
                                '-ac', '2',
                                temp_extracted_audio
                            ]
                            
                            self.log_text.insert(tk.END, f"从MP4文件提取音频: {os.path.basename(selected_mp4)}\n")
                            subprocess.run(extract_cmd, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
                            
                            # 检查提取是否成功
                            if os.path.exists(temp_extracted_audio) and os.path.getsize(temp_extracted_audio) > 0:
                                background_audio_path = temp_extracted_audio
                                self.log_text.insert(tk.END, f"成功从MP4提取音频\n")
                            else:
                                self.log_text.insert(tk.END, f"从MP4提取音频失败，将使用其他音频源\n")
                        except Exception as e:
                            self.log_text.insert(tk.END, f"提取音频出错: {str(e)}\n")
                    
                    # 如果没有MP4文件或提取失败，使用常规音频文件
                    if not background_audio_path and audio_files:
                        self.log_text.insert(tk.END, f"从音频文件夹中找到 {len(audio_files)} 个音频文件\n")
                        selected_audio = os.path.join(audio_folder, random.choice(audio_files))
                        background_audio_path = selected_audio
                        self.log_text.insert(tk.END, f"随机选择背景音乐: {os.path.basename(selected_audio)}\n")
                
                # 如果没有自定义音频或没有找到音频文件，使用视频B中的音频
                if not background_audio_path:
                    video_b_files = [f for f in os.listdir(video_b_folder)
                                   if f.lower().endswith(('.mp4', '.avi', '.mov'))]
                    video_b_path = os.path.join(video_b_folder, random.choice(video_b_files))
                    background_audio_path = video_b_path
                    self.log_text.insert(tk.END, f"使用视频B作为背景音乐: {os.path.basename(video_b_path)}\n")

                # 获取视频时长
                video_a_info = self.get_video_info(video_a_path)
                if not video_a_info:
                    return False

                target_duration = video_a_info['duration']

                # 4. 提取并处理背景音乐
                cmd_extract_b = [
                    'ffmpeg', '-y'
                ] + hw_params + [
                    '-stream_loop', '-1',  # 直接在提取时循环背景音乐
                    '-i', background_audio_path,
                    '-t', str(target_duration),
                    '-vn',
                    '-filter_complex',
                    f'volume={bg_volume}',  # 使用参数控制音量大小
                    '-acodec', 'pcm_s16le',
                    '-ar', '48000',  # 保持固定采样率
                    '-ac', '2',
                    temp_audio_b
                ]
                subprocess.run(cmd_extract_b, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)

                # 5. 混合音频并直接输出到最终文件
                # 随机选择音频比特率（范围：128k-320k）
                audio_bitrates = ['192k', '256k', '320k']
                random_bitrate = random.choice(audio_bitrates)
                self.log_text.insert(tk.END, f"使用随机音频比特率: {random_bitrate}\n")

                cmd_final = [
                    'ffmpeg', '-y'
                ] + hw_params + [
                    '-i', video_a_path,
                    '-i', temp_audio_a,
                    '-i', temp_audio_b,
                    '-map', '0:v',  # 使用原始视频流
                    '-c:v', 'copy',  # 复制视频流，不重新编码
                    '-filter_complex',
                    f'[1:a][2:a]amix=inputs=2:duration=first:weights=1.3 {bg_volume}',  # 使用参数控制混音比例
                    '-ar', '48000',  # 保持固定采样率
                    '-ac', '2',
                    '-b:a', random_bitrate,  # 使用随机比特率
                    output_path
                ]
                subprocess.run(cmd_final, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)

                self.log_text.insert(tk.END, "音频处理完成\n")
                return True

            finally:
                # 清理临时文件
                if os.path.exists(temp_folder):
                    shutil.rmtree(temp_folder)

        except Exception as e:
            self.log_text.insert(tk.END, f"音频处理失败: {str(e)}\n")
            return False
    def modify_metadata(self, input_path, output_path, output_fps=None):
        try:
            # 路径处理
            input_path = os.path.normpath(input_path).replace('\\', '/')
            output_path = os.path.normpath(output_path).replace('\\', '/')
            temp_file = os.path.join(os.path.dirname(output_path), f'temp_{int(time.time())}.mp4')
            temp_file = os.path.normpath(temp_file).replace('\\', '/')
            # 首先清除原有元数据
            self.log_text.insert(tk.END, "开始清除原有元数据...\n")
            if not self.clear_metadata(input_path, temp_file):
                self.log_text.insert(tk.END, "清除原有元数据失败\n")
                return False
            # 获取原始视频信息
            original_info = self.get_video_info(input_path)
            if not original_info:
                return False

            # 第一步：使用 FFmpeg 进行基础格式转换
            self.log_text.insert(tk.END, "重新封装视频...\n")

            # 设备和系统信息
            model = random.choice([
                'iPhone 13 Pro', 'iPhone 13 Pro Max',
                'iPhone 14 Pro', 'iPhone 14 Pro Max',
                'iPhone 15 Pro', 'iPhone 15 Pro Max'
            ])
            ios_version = random.choice([
                '16.6.1', '16.7', '17.0.3', '17.1',
                '17.1.2', '17.2', '17.2.1', '17.3'
            ])

            # 生成时间戳
            creation_time = time.strftime('%Y-%m-%dT%H:%M:%S.000Z',
                                          time.gmtime(time.time() - random.randint(1, 30 * 86400)))

            # 位置信息
            lat, lon = random.choice([
                (39.9042, 116.4074),  # 天安门
                (39.9947, 116.4142),  # 奥林匹克公园
                (39.9087, 116.3975)  # 西城区
            ])
            lat += random.uniform(-0.005, 0.005)
            lon += random.uniform(-0.005, 0.005)

            ffmpeg_cmd = [
                'ffmpeg', '-y',
                '-i', input_path,
                '-c', 'copy',
                '-metadata', 'com.apple.quicktime.make=Apple',
                '-metadata', f'com.apple.quicktime.model={model}',
                '-metadata', f'com.apple.quicktime.software=iOS {ios_version}',
                '-metadata', f'com.apple.quicktime.creationdate={creation_time}',
                '-movflags', '+faststart+use_metadata_tags',
                temp_file
            ]

            result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)
            if result.returncode != 0:
                self.log_text.insert(tk.END, f"FFmpeg处理失败: {result.stderr}\n")
                return False

            # 第二步：修改视频时长
            if not self.modify_video_duration(temp_file, temp_file):
                self.log_text.insert(tk.END, "修改视频时长失败\n")
                return False

            # 第三步：使用ExifTool添加其他元数据
            exif_cmd = [
                EXIFTOOL_PATH,  # 使用全局配置的ExifTool路径
                '-overwrite_original',
                f'-QuickTime:Make=Apple',
                f'-QuickTime:Model={model}',
                f'-QuickTime:Software=iOS {ios_version}',
                f'-QuickTime:CreationDate={creation_time}',
                f'-QuickTime:CreateDate={creation_time}',
                f'-QuickTime:ModifyDate={creation_time}',
                f'-QuickTime:GPSCoordinates={lat:0.6f} {lon:0.6f}',
                f'-QuickTime:GPSLatitude={lat}',
                f'-QuickTime:GPSLongitude={lon}',
                f'-QuickTime:GPSLatitudeRef=N',
                f'-QuickTime:GPSLongitudeRef=E',
                f'-Keys:Model={model}',
                f'-Keys:Software=iOS {ios_version}',
                '-Keys:Make=Apple',
            ]
            
            # 添加帧率元数据（如果提供了output_fps）
            if output_fps is not None:
                exif_cmd.extend([
                    f'-VideoFrameRate={output_fps}',
                    f'-QuickTime:VideoFrameRate={output_fps}'
                ])
                self.log_text.insert(tk.END, f"设置元数据帧率为: {output_fps}fps\n")
                
            # 添加文件路径
            exif_cmd.append(temp_file)

            result = subprocess.run(exif_cmd, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)
            if result.returncode != 0:
                self.log_text.insert(tk.END, f"ExifTool处理失败: {result.stderr}\n")
                return False

            # 移动到最终位置
            if os.path.exists(output_path):
                os.remove(output_path)
            shutil.move(temp_file, output_path)

            self.log_text.insert(tk.END, f"元数据修改完成 (设备: {model}, iOS: {ios_version})\n")
            return True

        except Exception as e:
            self.log_text.insert(tk.END, f"修改元数据出现异常: {str(e)}\n")
            return False
        finally:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass

    def apply_track_reuse(self, video_a_path, video_b_folder, output_path, video_a_fps=60, video_b_fps=60, output_fps=120, interleave_mode="baba", use_a_clips=True, process_options=None):
        try:
            self.log_text.insert(tk.END, "开始轨道复用处理...\n")

            # 规范化路径
            video_a_path = os.path.normpath(video_a_path)
            video_b_folder = os.path.normpath(video_b_folder)
            output_path = os.path.normpath(output_path)

            # 创建临时文件夹
            temp_folder = os.path.normpath(os.path.join(os.path.dirname(output_path), "temp_track"))
            os.makedirs(temp_folder, exist_ok=True)

            try:
                # 验证输入文件和文件夹
                if not os.path.isfile(video_a_path):
                    self.log_text.insert(tk.END, f"视频A文件不存在: {video_a_path}\n")
                    return False

                if os.path.isfile(video_b_folder):
                    video_b_folder = os.path.dirname(video_b_folder)
                    video_b_folder = os.path.normpath(video_b_folder)

                if not os.path.isdir(video_b_folder):
                    self.log_text.insert(tk.END, f"视频B的文件夹不存在: {video_b_folder}\n")
                    return False

                # 获取视频信息
                info_a = self.get_video_info(video_a_path)
                if not info_a:
                    return False

                # 获取视频B文件夹中的所有视频文件
                video_b_files = []
                for f in os.listdir(video_b_folder):
                    if f.lower().endswith(('.mp4', '.avi', '.mov')):
                        full_path = os.path.normpath(os.path.join(video_b_folder, f))
                        if os.path.isfile(full_path):
                            video_b_files.append(full_path)

                if len(video_b_files) < 6:
                    self.log_text.insert(tk.END, "错误: 视频B文件夹中的视频文件数量不足，至少需要6个视频文件\n")
                    self.log_text.insert(tk.END, "程序将立即停止\n")
                    # 强制退出程序
                    import sys
                    sys.exit(1)  # 使用非零退出码表示错误

                # 随机选择6-12个视频文件
                num_videos = random.randint(6, min(12, len(video_b_files)))
                selected_videos = random.sample(video_b_files, num_videos)
                self.log_text.insert(tk.END, f"已随机选择 {len(selected_videos)} 个视频文件\n")

                # 用于跟踪每个视频已使用的时间段
                used_segments = {}  # 格式: {video_path: [(start_time, end_time), ...]}
                
                # 专为避免平台判重的片段提取策略
                b_frames = []
                
                # 获取视频A的时长信息，用于确定需要多少视频B片段
                info_a = self.get_video_info(video_a_path)
                if not info_a:
                    self.log_text.insert(tk.END, "无法获取视频A信息，无法确定所需片段数量\n")
                    return False
                
                a_duration = info_a['duration']
                # 基于视频A时长计算所需的片段数量 - 每3-4秒一个片段，但至少8个片段
                min_clips_needed = max(8, int(a_duration / random.uniform(1.0, 4.0)))
                max_clips_needed = min_clips_needed + int(min_clips_needed * 0.5)  # 增加50%的冗余
                target_clips = random.randint(min_clips_needed, max_clips_needed)
                
                self.log_text.insert(tk.END, f"视频A时长: {a_duration:.2f}秒，计划提取 {target_clips} 个独立片段\n")
                
                # 随机打乱视频文件顺序，确保每次使用不同组合
                random.shuffle(video_b_files)
                
                # 新策略：优先从每个不同的视频中提取片段，确保多样性
                self.log_text.insert(tk.END, "使用基于视频A时长的智能提取策略...\n")
                
                # 计算每个视频B应提取的平均片段数
                max_videos_to_use = min(len(video_b_files), target_clips)
                clips_per_video = max(1, math.ceil(target_clips / max_videos_to_use))
                self.log_text.insert(tk.END, f"计划使用 {max_videos_to_use} 个不同的视频B源，每个视频平均提取 {clips_per_video} 个片段\n")
                
                clips_extracted = 0
                for video_idx, video_path in enumerate(video_b_files):
                    # 如果已经提取足够的片段，就停止
                    if clips_extracted >= target_clips:
                        break
                    
                    # 获取视频信息
                    info = self.get_video_info(video_path)
                    if not info or info['duration'] < 2:
                        continue
                    
                    # 为当前视频B确定需要提取的片段数
                    clips_to_extract = min(clips_per_video, target_clips - clips_extracted)
                    
                    # 如果视频时长足够，尝试从不同位置提取多个片段
                    if info['duration'] > clips_to_extract * 3:  # 确保有足够的时长提取多个片段
                        # 将视频B分成多个不重叠的区段
                        segment_duration = info['duration'] / clips_to_extract
                        
                        for i in range(clips_to_extract):
                            segment_start = i * segment_duration
                            segment_end = (i + 0.8) * segment_duration  # 使用80%的段大小，避免重叠
                            
                            if segment_start >= (info['duration'] - 2):
                                break  # 已经到达视频末尾
                            
                            # 在当前区段内随机选择时间点
                            start_time = random.uniform(segment_start, max(segment_start, segment_end - 2))
                            
                            # 动态调整片段时长 - 随机在0.4-2秒之间，但不超过剩余视频长度
                            clip_duration = min(2.0, max(0.3, random.uniform(0.3, 2.0)))
                            if start_time + clip_duration > info['duration']:
                                clip_duration = max(0.3, info['duration'] - start_time - 0.5)
                            
                            # 尝试找到附近的关键帧
                            try:
                                keyframes_cmd = [
                                    'ffprobe',
                                    '-v', 'error',
                                    '-skip_frame', 'nokey',
                                    '-select_streams', 'v:0',
                                    '-show_entries', 'frame=pts_time',
                                    '-read_intervals', f'%+{max(0, start_time-1)}%+{min(info["duration"], start_time+1)}',
                                    '-of', 'csv=p=0',
                                    video_path
                                ]
                                keyframes_result = subprocess.run(keyframes_cmd, capture_output=True, text=True, timeout=3, **DEFAULT_SUBPROCESS_PARAMS)
                                keyframes = [float(time) for time in keyframes_result.stdout.strip().split('\n') if time.strip()]
                                
                                if keyframes:
                                    # 找到最近的关键帧
                                    closest_keyframe = min(keyframes, key=lambda kf: abs(kf - start_time))
                                    if abs(closest_keyframe - start_time) < 1.0:  # 差异不超过1秒
                                        start_time = closest_keyframe
                            except:
                                # 忽略所有错误
                                pass
                            
                            # 生成唯一文件名
                            timestamp = int(time.time() * 1000000) + video_idx * 100 + i
                            clip_path = os.path.normpath(os.path.join(temp_folder, f"diverse_{timestamp}.mp4"))
                            
                            # 提取片段
                            cmd = [
                                'ffmpeg', '-y',
                                '-ss', f"{start_time:.3f}",
                                '-i', video_path,
                                '-t', f"{clip_duration:.3f}",
                                '-c', 'copy',
                                '-avoid_negative_ts', '1',
                                clip_path
                            ]
                            
                            try:
                                subprocess.run(cmd, capture_output=True, check=True, timeout=10, **DEFAULT_SUBPROCESS_PARAMS)
                                b_frames.append(clip_path)
                                clips_extracted += 1
                                
                                # 每提取5个片段显示一次进度
                                if clips_extracted % 5 == 0 or clips_extracted == target_clips:
                                    self.log_text.insert(tk.END, f"已提取 {clips_extracted}/{target_clips} 个片段\n")
                                else:
                                    self.log_text.insert(tk.END, ".")  # 简单进度指示
                                
                            except Exception as e:
                                # 忽略错误，继续下一个区段
                                continue
                    else:
                        # 如果视频时长不够长，只提取一个片段
                        start_time = random.uniform(0, max(0, info['duration'] - 2))
                        clip_duration = min(2.0, max(1.0, info['duration'] * 0.3))
                        
                        # 生成唯一文件名
                        timestamp = int(time.time() * 1000000) + video_idx
                        clip_path = os.path.normpath(os.path.join(temp_folder, f"diverse_{timestamp}.mp4"))
                        
                        cmd = [
                            'ffmpeg', '-y',
                            '-ss', f"{start_time:.3f}",
                            '-i', video_path,
                            '-t', f"{clip_duration:.3f}",
                            '-c', 'copy',
                            '-avoid_negative_ts', '1',
                            clip_path
                        ]
                        
                        try:
                            subprocess.run(cmd, capture_output=True, check=True, timeout=10, **DEFAULT_SUBPROCESS_PARAMS)
                            b_frames.append(clip_path)
                            clips_extracted += 1
                            self.log_text.insert(tk.END, ".")  # 简单进度指示
                        except Exception as e:
                            # 忽略错误，继续下一个视频
                            continue
                
                # 告知用户结果
                self.log_text.insert(tk.END, f"成功提取了 {len(b_frames)} 个片段，每个来自不同视频\n")

                if not b_frames:
                    # 备用策略：直接使用完整视频
                    self.log_text.insert(tk.END, "无法提取任何片段，启用极端备用策略...\n")
                    
                    for video_path in video_b_files[:6]:  # 最多使用6个视频
                        timestamp = int(time.time() * 1000000) + random.randint(0, 1000000)
                        clip_path = os.path.normpath(os.path.join(temp_folder, f"emergency_{timestamp}.mp4"))
                        
                        # 尝试直接复制视频的一小部分
                        try:
                            # 使用最简单的提取方法
                            start_time = random.uniform(0, max(0, info['duration'] - 3))
                            cmd = [
                                'ffmpeg', '-y',
                                '-ss', f"{start_time:.3f}",
                                '-i', video_path,
                                '-t', '3.0',  # 固定3秒
                                '-c', 'copy',
                                clip_path
                            ]
                            subprocess.run(cmd, capture_output=True, check=True, timeout=5, **DEFAULT_SUBPROCESS_PARAMS)
                            b_frames.append(clip_path)
                            self.log_text.insert(tk.END, f"使用极端备用策略成功提取片段\n")
                            
                            if len(b_frames) >= 4:  # 只要有4个片段就够了
                                break
                        except Exception as e:
                            # 完全忽略错误
                            continue
                
                # 最终检查
                if len(b_frames) < 2:  # 只要有2个以上片段就可以继续
                    self.log_text.insert(tk.END, f"提取的视频B有效片段数量太少 (只有 {len(b_frames)} 个)，无法处理\n")
                    return False
                else:
                    # 确保有足够的片段 - 如果太少，复制现有片段
                    while len(b_frames) < 8:
                        # 复制现有片段，添加到列表末尾
                        original = random.choice(b_frames)
                        copy_idx = len(b_frames)
                        copy_path = os.path.normpath(os.path.join(temp_folder, f"copy_{copy_idx}_{os.path.basename(original)}"))
                        shutil.copy2(original, copy_path)
                        b_frames.append(copy_path)
                    
                    self.log_text.insert(tk.END, f"最终使用 {len(b_frames)} 个片段进行处理\n")

                # 随机打乱视频B片段顺序
                random.shuffle(b_frames)
                self.log_text.insert(tk.END, f"总共提取了 {len(b_frames)} 个视频B片段\n")

                # 添加视频A的随机片段处理
                a_frames = []
                a_duration = info_a['duration']
                
                # 如果启用了取视频A片段，则进行提取
                if use_a_clips:
                    self.log_text.insert(tk.END, "开始处理视频A随机片段...\n")
                    
                    # 随机提取3-6个视频A片段
                    num_a_clips = random.randint(3, 6)
                    a_used_segments = []
                else:
                    # 如果未启用取视频A片段，设置为0
                    num_a_clips = 0
                    a_used_segments = []
                
                for i in range(num_a_clips):
                    # 随机选择片段时长和起始时间
                    a_clip_duration = random.uniform(1.0, 3.0)
                    max_start = max(0, a_duration - a_clip_duration - 1)
                    if max_start <= 1:
                        continue
                        
                    a_start_time = random.uniform(1, max_start)
                    a_end_time = a_start_time + a_clip_duration
                    
                    # 检查是否重叠
                    overlap = False
                    for used_start, used_end in a_used_segments:
                        if not (a_end_time < used_start - 0.5 or a_start_time > used_end + 0.5):
                            overlap = True
                            break
                            
                    if overlap:
                        continue
                        
                    # 生成片段文件名
                    timestamp = int(time.time() * 1000000 + i)
                    random_suffix = ''.join(random.choices('0123456789abcdef', k=8))
                    a_clip_path = os.path.normpath(os.path.join(temp_folder, f"a_clip_{timestamp}_{random_suffix}.mp4"))
                    
                    # 获取关键帧信息
                    use_keyframe = False
                    try:
                        # 尝试获取关键帧位置
                        keyframes_cmd = [
                            'ffprobe',
                            '-v', 'error',
                            '-skip_frame', 'nokey',
                            '-select_streams', 'v:0',
                            '-show_entries', 'frame=pts_time',
                            '-of', 'csv=print_section=0',
                            video_a_path
                        ]
                        keyframes_result = subprocess.run(keyframes_cmd, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)
                        keyframes = [float(time) for time in keyframes_result.stdout.strip().split('\n') if time.strip()]
                        
                        if keyframes:
                            # 找到略小于或等于start_time的最近关键帧
                            suitable_keyframes = [kf for kf in keyframes if kf <= a_start_time]
                            if suitable_keyframes:
                                closest_keyframe = max(suitable_keyframes)
                                # 只有当关键帧相差不大时才使用
                                if a_start_time - closest_keyframe < 1.0:  # 不超过1秒差异
                                    a_start_time = closest_keyframe
                                    use_keyframe = True
                    except Exception as e:
                        # 忽略错误，继续使用原始时间点
                        pass
                    
                    # 检查视频A是否为横屏
                    is_horizontal = False
                    try:
                        # 获取当前视频的信息
                        a_info = self.get_video_info(video_a_path)
                        if a_info and a_info['width'] > a_info['height']:
                            is_horizontal = True
                            self.log_text.insert(tk.END, "备用方法检测到横屏视频A，将进行旋转处理\n")
                        else:
                            # 确保竖屏时is_horizontal一定为False
                            is_horizontal = False
                            self.log_text.insert(tk.END, "备用方法检测到竖屏视频A，不进行旋转\n")
                    except Exception as e:
                        # 出错时默认为竖屏，不旋转
                        is_horizontal = False
                        self.log_text.insert(tk.END, f"备用方法检测视频方向失败: {str(e)}，默认不旋转\n")
                    
                    # 提取并处理视频A片段 - 根据方向决定是否旋转
                    if is_horizontal:
                        # 横屏，需要旋转
                        a_cmd = [
                            'ffmpeg', '-y',
                            '-ss', f"{a_start_time:.3f}",  # 直接定位到起始时间
                            '-i', video_a_path,            # 指定输入文件
                            '-t', f"{a_clip_duration:.3f}",  # 指定持续时间
                            '-vf', 'transpose=2',          # 添加逆时针旋转90度滤镜（2=逆时针）
                            '-c:v', 'libx264',             # 使用x264编码器
                            '-preset', 'ultrafast',        # 使用最快的编码速度
                            '-crf', '1',                  # 保持高质量
                            '-an',                         # 不包含音频
                            '-avoid_negative_ts', '1',
                            a_clip_path                    # 输出路径
                        ]
                    else:
                        # 竖屏，不需要旋转
                        a_cmd = [
                            'ffmpeg', '-y',
                            '-ss', f"{a_start_time:.3f}",  # 直接定位到起始时间
                            '-i', video_a_path,            # 指定输入文件
                            '-t', f"{a_clip_duration:.3f}",  # 指定持续时间
                            '-c:v', 'libx264',             # 使用x264编码器
                            '-preset', 'ultrafast',        # 使用最快的编码速度
                            '-crf', '1',                  # 保持高质量
                            '-an',                         # 不包含音频
                            '-avoid_negative_ts', '1',
                            a_clip_path                    # 输出路径
                        ]
                    
                    try:
                        subprocess.run(a_cmd, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
                        a_frames.append(a_clip_path)
                        a_used_segments.append((a_start_time, a_end_time))
                        self.log_text.insert(tk.END, f"已提取并处理视频A的 {a_clip_duration:.1f} 秒片段\n")
                    except subprocess.CalledProcessError as e:
                        self.log_text.insert(tk.END, f"处理视频A片段失败: {str(e)}\n")
                
                if not a_frames and use_a_clips:
                    self.log_text.insert(tk.END, "无法提取有效的视频A片段，将尝试备用方法\n")
                    
                    # 使用备用提取方法 - 最简单的单次提取
                    try:
                        # 从原视频中间提取一个短片段作为备用
                        a_duration = info_a['duration']
                        if a_duration > 10:  # 只在视频足够长时尝试
                            mid_point = a_duration / 2
                            backup_clip = os.path.normpath(os.path.join(temp_folder, "a_backup.mp4"))
                            
                            # 检查视频A是否为横屏
                            is_horizontal = False
                            try:
                                # 获取当前视频的信息
                                a_info = self.get_video_info(video_a_path)
                                if a_info and a_info['width'] > a_info['height']:
                                    is_horizontal = True
                                    self.log_text.insert(tk.END, "备用方法检测到横屏视频A，将进行旋转处理\n")
                                else:
                                    # 确保竖屏时is_horizontal一定为False
                                    is_horizontal = False
                                    self.log_text.insert(tk.END, "备用方法检测到竖屏视频A，不进行旋转\n")
                            except Exception as e:
                                # 出错时默认为竖屏，不旋转
                                is_horizontal = False
                                self.log_text.insert(tk.END, f"备用方法检测视频方向失败: {str(e)}，默认不旋转\n")
                            
                            # 使用最简单的提取命令（根据视频方向决定是否旋转）
                            if is_horizontal:
                                # 横屏，需要旋转
                                backup_cmd = [
                                    'ffmpeg', '-y',
                                    '-ss', f"{mid_point-2:.3f}",
                                    '-i', video_a_path,
                                    '-t', '4.0',  # 固定4秒
                                    '-vf', 'transpose=2',  # 添加逆时针旋转90度滤镜（2=逆时针）
                                    '-c:v', 'libx264',     # 使用x264编码器
                                    '-preset', 'ultrafast',  # 使用最快的编码速度
                                    '-crf', '1',          # 保持高质量
                                    backup_clip
                                ]
                            else:
                                # 竖屏，不需要旋转
                                backup_cmd = [
                                    'ffmpeg', '-y',
                                    '-ss', f"{mid_point-2:.3f}",
                                    '-i', video_a_path,
                                    '-t', '4.0',  # 固定4秒
                                    '-c:v', 'libx264',     # 使用x264编码器
                                    '-preset', 'ultrafast',  # 使用最快的编码速度
                                    '-crf', '1',          # 保持高质量
                                    backup_clip
                                ]
                            
                            # 只尝试一次，成功则使用，失败则忽略
                            try:
                                subprocess.run(backup_cmd, capture_output=True, check=True, timeout=10, **DEFAULT_SUBPROCESS_PARAMS)
                                a_frames.append(backup_clip)
                                self.log_text.insert(tk.END, "使用备用方法提取了一个视频A片段\n")
                            except:
                                pass
                    except:
                        # 完全忽略所有错误
                        pass

                # 合并所有的片段（视频B和视频A的片段）
                all_frames = []
                # 按一定频率插入视频A片段
                b_idx = 0
                a_idx = 0
                
                # 如果启用了取A片段且有视频A片段，则交替插入
                if use_a_clips and a_frames:
                    # 改进片段交替策略，确保视频A片段有足够的动态效果
                    insert_frequency = max(1, len(b_frames) // len(a_frames))  # 恢复原来的频率计算
                    self.log_text.insert(tk.END, f"每 {insert_frequency} 个B片段插入1个A片段\n")
                    
                    while b_idx < len(b_frames):
                        # 添加几个视频B片段
                        for _ in range(insert_frequency):
                            if b_idx < len(b_frames):
                                all_frames.append(b_frames[b_idx])
                                b_idx += 1
                                
                        # 插入一个视频A片段
                        if a_idx < len(a_frames):
                            # 只添加一次，不重复
                            all_frames.append(a_frames[a_idx])
                            a_idx += 1
                else:
                    # 没有视频A片段或未启用取A片段，只使用视频B片段
                    all_frames = b_frames
                    if not use_a_clips:
                        self.log_text.insert(tk.END, "已禁用视频A片段提取，仅使用视频B片段\n")
                
                # 随机打乱所有片段的顺序，增加视觉多样性
                random.shuffle(all_frames)
                self.log_text.insert(tk.END, f"已随机打乱 {len(all_frames)} 个片段的顺序\n")
                
                # 创建最终的视频B（连接所有片段）
                temp_video_b = os.path.normpath(os.path.join(temp_folder, "temp_video_b.mp4"))
                
                # 创建concat文件
                concat_file = os.path.normpath(os.path.join(temp_folder, "concat.txt"))
                with open(concat_file, 'w', encoding='utf-8') as f:
                    for clip in all_frames:
                        # 将文件路径写入concat文件
                        f.write(f"file '{clip}'\n")
                
                # 使用复制流连接所有片段，确保最高画质
                cmd_create_b = [
                    'ffmpeg', '-y',
                    '-f', 'concat',
                    '-safe', '0',
                    '-i', concat_file,
                    '-c:v', 'copy',  # 使用复制流，不重新编码视频
                    '-c:a', 'copy',  # 也复制音频流
                    '-vsync', 'cfr',  # 使用恒定帧率模式，避免丢帧
                    temp_video_b
                ]
                
                # 执行合并命令
                try:
                    subprocess.run(cmd_create_b, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
                    self.log_text.insert(tk.END, f"已合并 {len(all_frames)} 个片段，包括 {len(b_frames)} 个视频B片段和 {len(a_frames)} 个视频A片段\n")
                    
                    # 验证合并后的视频
                    if os.path.exists(temp_video_b):
                        merged_info = self.get_video_info(temp_video_b)
                        if merged_info:
                            self.log_text.insert(tk.END, f"合并后视频长度: {merged_info['duration']:.2f}秒\n")
                            self.log_text.insert(tk.END, f"包含视频A片段数量: {sum(1 for f in all_frames if f in a_frames)}\n")
                except subprocess.CalledProcessError as e:
                    self.log_text.insert(tk.END, f"合并视频B时出错: {e.stderr.decode() if e.stderr else str(e)}\n")

                # 初始化process_options参数，如果未提供
                if process_options is None:
                    process_options = {}
                    
                # 输出当前叠加视频和图片边框设置，便于调试
                self.log_text.insert(tk.END, f"处理前叠加视频设置: 启用={process_options.get('enable_overlay', False)}, 文件夹={process_options.get('processed_b_folder', '未设置')}, 透明度={process_options.get('overlay_opacity', 0.5)}\n")
                self.log_text.insert(tk.END, f"处理前图片边框设置: 启用={process_options.get('enable_image_border', False)}, 文件夹={process_options.get('image_folder', '未设置')}\n")
                
                # 检查是否只使用原始视频B
                if process_options.get('use_original_b_only', False):
                    self.log_text.insert(tk.END, "只使用原始视频B模式已启用，将直接使用一个原始视频...\n")
                    
                    # 从视频B文件夹中选择第一个视频
                    video_b_files = [f for f in os.listdir(video_b_folder)
                                   if f.lower().endswith(('.mp4', '.avi', '.mov'))]
                    
                    if not video_b_files:
                        self.log_text.insert(tk.END, "错误：视频B文件夹中没有找到视频文件\n")
                        return False
                        
                    # 选择第一个视频B文件
                    video_b_files.sort()  # 排序确保顺序一致
                    selected_b_file = video_b_files[0]
                    original_b_path = os.path.join(video_b_folder, selected_b_file)
                    self.log_text.insert(tk.END, f"已选择第一个视频B文件: {selected_b_file}\n")
                    
                    # 复制到临时文件夹
                    os.makedirs(temp_folder, exist_ok=True)
                    temp_original_b = os.path.join(temp_folder, "original_video_b.mp4")
                    
                    try:
                        shutil.copy2(original_b_path, temp_original_b)
                        self.log_text.insert(tk.END, f"已复制视频B到临时文件夹: {temp_original_b}\n")
                        
                        # 删除原始视频B文件 - 必须删除成功才能继续
                        try:
                            os.remove(original_b_path)
                            self.log_text.insert(tk.END, f"已删除原视频B文件: {selected_b_file}\n")
                        except Exception as e:
                            self.log_text.insert(tk.END, f"删除原视频B文件失败: {str(e)}\n")
                            self.log_text.insert(tk.END, "错误：必须删除原视频B文件才能继续，处理中断\n")
                            return False  # 如果删除失败，中断处理
                        
                        # 使用复制的原始视频B替代temp_video_b
                        temp_video_b = temp_original_b
                    except Exception as e:
                        self.log_text.insert(tk.END, f"复制视频B失败: {str(e)}\n")
                        return False
                
                # 确保保留所有process_options参数传递给_do_track_reuse
                # 执行最终的轨道复用
                return self._do_track_reuse(video_a_path, temp_video_b, output_path, video_a_fps, video_b_fps, output_fps, interleave_mode, process_options)

            finally:
                # 清理临时文件
                if os.path.exists(temp_folder):
                    try:
                        shutil.rmtree(temp_folder)
                    except Exception as e:
                        self.log_text.insert(tk.END, f"清理临时文件失败: {str(e)}\n")

        except Exception as e:
            self.log_text.insert(tk.END, f"轨道复用处理失败: {str(e)}\n")
            import traceback
            self.log_text.insert(tk.END, f"详细错误:\n{traceback.format_exc()}\n")
            return False
    def _do_track_reuse(self, video_a_path, video_b_path, output_path, video_a_fps=60, video_b_fps=60, output_fps=120, interleave_mode="baba", process_options=None):
        """执行实际的轨道复用处理"""
        # 如果没有传入process_options，初始化为空字典
        if process_options is None:
            process_options = {}
        try:
            # 规范化路径
            video_a_path = os.path.normpath(video_a_path)
            video_b_path = os.path.normpath(video_b_path)
            output_path = os.path.normpath(output_path)

            # 检查文件是否存在
            if not os.path.isfile(video_a_path):
                self.log_text.insert(tk.END, f"视频A文件不存在: {video_a_path}\n")
                return False

            # 检查视频B文件是否存在 - 必须存在，否则立即中止程序
            if not os.path.isfile(video_b_path):
                self.log_text.insert(tk.END, f"错误: 视频B文件不存在: {video_b_path}\n")
                self.log_text.insert(tk.END, "程序将立即停止\n")
                # 强制退出程序
                import sys
                sys.exit(1)  # 使用非零退出码表示错误

            # 获取视频信息
            info_a = self.get_video_info(video_a_path)
            info_b = self.get_video_info(video_b_path)
            if not info_a or not info_b:
                return False

            # 创建临时文件夹
            temp_folder = os.path.normpath(os.path.join(os.path.dirname(output_path), "temp_track_final"))
            try:
                if os.path.exists(temp_folder):
                    shutil.rmtree(temp_folder)
                os.makedirs(temp_folder)
            except Exception as e:
                self.log_text.insert(tk.END, f"创建临时文件夹失败: {str(e)}\n")
                return False

            try:
                # 定义临时文件路径
                temp_a = os.path.normpath(os.path.join(temp_folder, "temp_a_60fps.mp4"))
                temp_b = os.path.normpath(os.path.join(temp_folder, "temp_b_60fps.mp4"))
                temp_audio = os.path.normpath(os.path.join(temp_folder, "temp_audio.aac"))
                temp_b_loop = os.path.normpath(os.path.join(temp_folder, "temp_b_loop.mp4"))

               

                # 处理视频A - 强制转换为自定义帧率
                self.log_text.insert(tk.END, f"正在处理视频A - 转换为{video_a_fps}fps...\n")
                
                # 始终对视频A进行速度调整
                do_speed_adjust = True  # 100%应用速度调整
                if do_speed_adjust:
                    # 随机决定是加速还是减速
                    if random.choice([True, False]):
                        # 减速：0.9到0.95之间
                        speed_factor = random.uniform(0.85, 0.9)
                    else:
                        # 加速：1.1到1.15之间
                        speed_factor = random.uniform(1.1, 1.15)
                    
                    # 计算倍速的倒数，用于setpts滤镜（值越小速度越快）
                    setpts_value = 1.0 / speed_factor
                    # 音频速度因子与视频保持一致
                    atempo_value = speed_factor
                    
                    self.log_text.insert(tk.END, f"视频A将{'加速' if speed_factor > 1 else '减速'}处理，速度为正常的{speed_factor:.2f}倍\n")
                else:
                    speed_factor = 1.0
                    self.log_text.insert(tk.END, f"视频A将使用正常速度处理\n")
                    
                try:
                    # 获取视频A的信息
                    info_a = self.get_video_info(video_a_path)
                    if not info_a:
                        self.log_text.insert(tk.END, "无法获取视频A信息\n")
                        return False

                    # 判断视频方向并设置目标分辨率
                    is_vertical = info_a['height'] > info_a['width']
                    if is_vertical:
                        target_width = 1080
                        target_height = 1920
                        # 竖屏使用直接缩放，保持9:16比例
                        base_filter = f'scale={target_width}:{target_height},setdar=9/16,fps={video_a_fps},setsar=1:1,colorspace=bt470bg:iall=bt709:fast=1'
                    else:
                        # 横屏视频改为4:3比例并填充
                        target_width = 1440  # 4:3宽度
                        target_height = 1080
                        # 使用非线性拉伸来适应4:3比例，不裁剪也不添加黑边
                        self.log_text.insert(tk.END, "检测到横屏视频，使用非线性拉伸转换为4:3比例\n")
                        # 直接缩放到目标尺寸，允许变形
                        base_filter = f'scale={target_width}:{target_height},setdar=4/3,fps={video_a_fps},setsar=1:1,colorspace=bt470bg:iall=bt709:fast=1'
                        
                    # 如果需要速度调整，添加setpts滤镜进行速度变化
                    if do_speed_adjust and speed_factor != 1.0:
                        vf_filter = f'{base_filter},setpts={setpts_value}*PTS'
                    else:
                        vf_filter = base_filter

                    # 首先尝试使用硬件加速处理视频A
                    cmd_a = [
                        'ffmpeg', '-y',
                        '-init_hw_device', 'qsv=qsv:MFX_IMPL_hw_any',
                        '-filter_hw_device', 'qsv',
                        '-i', video_a_path,
                        '-vf', vf_filter,
                        '-c:v', 'h264_qsv',
                        '-preset', 'medium',
                        '-global_quality', '1',
                        '-look_ahead', '1',
                        '-pix_fmt', 'yuv420p',
                        '-color_primaries', 'bt709',
                        '-color_trc', 'bt709',
                        '-colorspace', 'bt709',
                        '-r', str(video_a_fps),
                        temp_a
                    ]
                    subprocess.run(cmd_a, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
                    self.log_text.insert(tk.END, "使用硬件加速处理成功\n")
                except subprocess.CalledProcessError as e:
                    self.log_text.insert(tk.END, "硬件加速失败，切换到软件编码...\n")
                    # 如果硬件加速失败，使用软件编码
                    cmd_a = [
                        'ffmpeg', '-y',
                        '-i', video_a_path,
                        '-vf', vf_filter,
                        '-c:v', 'libx264',
                        '-preset', 'medium',
                        '-crf', '1',
                        '-pix_fmt', 'yuv420p',
                        '-r', str(video_a_fps),
                        temp_a
                    ]
                    subprocess.run(cmd_a, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
                    self.log_text.insert(tk.END, "使用软件编码处理成功\n")

                # 从处理后的视频中提取音频
                self.log_text.insert(tk.END, "从处理后的视频中提取音频...\n")
                
                # 构建基础音频提取命令
                audio_filter = []
                
                # 如果需要速度调整，添加atempo滤镜进行音频速度变化
                if do_speed_adjust and speed_factor != 1.0:
                    # 注意：atempo滤镜有效值范围是0.5-2.0，需要处理超出范围的情况
                    if 0.5 <= atempo_value <= 2.0:
                        audio_filter.append(f'atempo={atempo_value}')
                    elif atempo_value < 0.5:
                        # 多次应用来处理小于0.5的值
                        audio_filter.append('atempo=0.5,atempo=0.5,atempo=0.5')  # 0.125x
                    elif atempo_value > 2.0:
                        # 多次应用来处理大于2.0的值
                        audio_filter.append('atempo=2.0,atempo=2.0')  # 4x
                
                # 构建ffmpeg命令
                cmd_audio = [
                    'ffmpeg', '-y',
                    '-i', temp_a,  # 使用处理后的视频
                    '-vn'
                ]
                
                # 添加音频滤镜（如果有）
                if audio_filter:
                    cmd_audio.extend(['-af', ','.join(audio_filter)])
                
                # 添加其他音频参数
                cmd_audio.extend([
                    '-acodec', 'aac',
                    '-ar', '48000', '-ac', '2',
                    '-b:a', '192k',
                    temp_audio
                ])
                subprocess.run(cmd_audio, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)

                # 处理视频B - 根据视频A的方向进行适当调整，确保填满画面
                self.log_text.insert(tk.END, f"正在处理视频B - 调整尺寸并转换为{video_b_fps}fps并匹配视频A的时长...\n")
                
                # 获取视频A的信息用于参考
                info_a = self.get_video_info(temp_a)  # 使用已处理的视频A
                if not info_a:
                    self.log_text.insert(tk.END, "无法获取视频A信息\n")
                    return False
                    
                # 获取视频B的信息
                info_b = self.get_video_info(video_b_path)
                if not info_b:
                    self.log_text.insert(tk.END, "无法获取视频B信息\n")
                    return False
                
                # 确定视频A和B的方向
                is_a_vertical = info_a['height'] > info_a['width']
                is_b_vertical = info_b['height'] > info_b['width']
                self.log_text.insert(tk.END, f"视频A是{'竖屏' if is_a_vertical else '横屏'}, 视频B是{'竖屏' if is_b_vertical else '横屏'}\n")

                # 确保视频B与视频A具有相同的分辨率
                target_width = info_a['width']
                target_height = info_a['height']
                
                # 计算视频A的预期总帧数
                a_expected_frames = int(info_a['duration'] * video_a_fps)  # 基于自定义帧率
                
                self.log_text.insert(tk.END, f"视频A - 时长: {info_a['duration']:.2f}秒, 预期帧数: {a_expected_frames}\n")
                
                # 直接一步处理视频B，避免二次编码导致的画质损失
                try:
                    # 根据视频A和B的方向决定处理方式
                    vf_option = ''
                    
                    # 调试输出process_options内容
                    self.log_text.insert(tk.END, f"图片边框设置: {process_options.get('enable_image_border')}, 图片文件夹: {process_options.get('image_folder', '未设置')}\n")
                    
                    # 优先检查是否启用了图片边框功能
                    if is_a_vertical and process_options.get('enable_image_border') == True and process_options.get('image_folder'):
                        # 视频B只占2/3高度
                        b_height = int(target_height * 2/3)
                        self.log_text.insert(tk.END, f"使用图片边框：视频B高度设为{b_height}像素（屏幕2/3）\n")
                        vf_option = f'scale=-1:{b_height},crop={target_width}:{b_height}:(iw-{target_width})/2:0,fps={video_b_fps},setsar=1:1'
                    elif is_a_vertical != is_b_vertical:
                        # 方向不同，需要特殊处理以填满画面
                        if is_a_vertical:  # A是竖屏，B是横屏
                            # 传统方式：将横屏B裁剪为竖屏（保持画面中心，裁剪两侧）
                            self.log_text.insert(tk.END, "视频B是横屏，将裁剪为竖屏（保持画面中心）\n")
                            vf_option = f'scale=-1:{target_height},crop={target_width}:{target_height}:(iw-{target_width})/2:0,fps={video_b_fps},setsar=1:1'
                        else:  # A是横屏，B是竖屏
                            # 将竖屏B裁剪为横屏（保持画面中心，裁剪上下）
                            self.log_text.insert(tk.END, "视频B是竖屏，将裁剪为横屏（保持画面中心）\n")
                            vf_option = f'scale={target_width}:-1,crop={target_width}:{target_height}:0:(ih-{target_height})/2,fps={video_b_fps},setsar=1:1'
                    else:
                        # 方向相同，使用常规的缩放和裁剪，确保填满
                        self.log_text.insert(tk.END, "视频A和B方向相同，使用填满式缩放\n")
                        vf_option = f'scale={target_width}:{target_height}:force_original_aspect_ratio=increase,crop={target_width}:{target_height},fps={video_b_fps},setsar=1:1'
                    
                    # 检查是否需要使用图片边框
                    if is_a_vertical and process_options.get('enable_image_border', False) and process_options.get('image_folder'):
                        self.log_text.insert(tk.END, "使用图片边框模式处理视频B...\n")
                        image_folder = process_options.get('image_folder')
                        
                        # 检查图片文件夹是否存在
                        if not os.path.exists(image_folder):
                            self.log_text.insert(tk.END, f"图片文件夹不存在: {image_folder}，将使用传统模式\n")
                        else:
                            # 获取所有图片文件
                            image_files = [f for f in os.listdir(image_folder) 
                                         if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.webp'))]
                            
                            if len(image_files) < 1:
                                self.log_text.insert(tk.END, f"图片文件夹中图片数量不足: 只有 {len(image_files)} 张，至少需要1张\n")
                            else:
                                self.log_text.insert(tk.END, f"找到 {len(image_files)} 张图片，将随机选择1张\n")
                                
                                # 随机选择1张图片
                                selected_image = random.choice(image_files)
                                bg_image = os.path.normpath(os.path.join(image_folder, selected_image))
                                
                                self.log_text.insert(tk.END, f"已选择图片：{os.path.basename(bg_image)}\n")
                                
                                # 图片尺寸为1080x1920（竖屏）
                                bg_width = 1080
                                bg_height = 1920
                                
                                try:
                                    # 创建全新的处理流程，不进行裁剪，只做随机缩放
                                    self.log_text.insert(tk.END, "视频B将保持原始宽高比，不裁剪只缩放\n")
                                    
                                    # 使用软件滤镜+硬件编码的混合方法
                                    self.log_text.insert(tk.END, "使用软件滤镜处理+QSV硬件编码模式...\n")
                                    
                                    # 计算屏幕高度的一半作为基础高度
                                    base_height = 1920 // 2  # 基础高度为960像素
                                    
                                    # 生成随机缩放因子，在一半高度基础上随机浮动
                                    # 随机因子在0.8-1.2之间，提供20%上下浮动空间
                                    random_factor = random.uniform(0.8, 1.2)
                                    
                                    # 计算目标高度
                                    target_height = int(base_height * random_factor)
                                    
                                    self.log_text.insert(tk.END, f"视频B将保持原始比例，高度随机设置为屏幕高度的{target_height/1920:.1%}\n")
                                    self.log_text.insert(tk.END, f"目标高度约为{target_height}像素（基准960像素上下浮动20%）\n")
                                    
                                    # 随机决定是否水平翻转视频B（30%几率）- 如果使用原始视频B则不翻转
                                    do_hflip_b = False if process_options.get('use_original_b_only', False) else random.random() < 0.3  # 30%的几率进行水平翻转，使用原始视频B时不翻转
                                    # 随机生成视频B的水平偏移百分比，范围-20%到20%
                                    offset_percent_b = random.uniform(-0.2, 0.2)  # 随机偏移量，负值向左，正值向右
                                    
                                    self.log_text.insert(tk.END, f"视频B {'将' if do_hflip_b else '不'}进行水平镜像\n")
                                    self.log_text.insert(tk.END, f"视频B将进行随机水平偏移{offset_percent_b:.1%}（{'-左' if offset_percent_b < 0 else '右'}）\n")
                                    
                                    # 视频B处理 - 随机镜像，按照原始比例随机缩放到目标高度，有几率随机拉伸
                                    do_stretch_b = random.random() < 0.4  # 40%的几率应用拉伸效果
                                    stretch_factor_w = random.uniform(0.85, 1.15) if do_stretch_b else 1  # 横向拉伸，范围在±15%
                                    stretch_factor_h = random.uniform(0.95, 1.05) if do_stretch_b else 1  # 微小的纵向拉伸，范围在±5%
                                    
                                    # 随机决定是否应用RGB抖动效果
                                    do_pixelate = random.random() < 0.5  # 50%的几率应用RGB抖动效果
                                    pixelate_factor = random.randint(1, 3) if do_pixelate else 0  # 抖动强度，影响RGB偏移量
                                    
                                    # 随机决定是否应用色彩变化效果
                                    do_color_shift = random.random() < 0.5  # 40%的几率应用色彩变化
                                    color_shift_factor = random.randint(1, 3) if do_color_shift else 0  # 色彩变化强度
                                    
                                    # 初始化边框处理状态变量
                                    border_processed = False
                                    
                                    self.log_text.insert(tk.END, f"视频B {'将' if do_stretch_b else '不'}进行微小拉伸（宽度:{stretch_factor_w:.2f}倍，高度:{stretch_factor_h:.2f}倍）\n")
                                    self.log_text.insert(tk.END, f"视频B {'将' if do_pixelate else '不'}应用像素抖动效果（强度:{pixelate_factor}）\n")
                                    self.log_text.insert(tk.END, f"视频B {'将' if do_color_shift else '不'}应用色彩变化效果（强度:{color_shift_factor}）\n")
                                    
                                    # 创建视频B的处理指令
                                    # 首先构建基础视频B滤镜
                                    if do_stretch_b:
                                        base_filter_b = f'{"hflip," if do_hflip_b else ""}scale=iw*{stretch_factor_w}:-1,scale=-1:{int(target_height*stretch_factor_h)}'
                                    else:
                                        base_filter_b = f'{"hflip," if do_hflip_b else ""}scale=-1:{target_height}'
                                    
                                    # 初始化滤镜
                                    pixel_filter = ''
                                    
                                    # 添加RGB抖动效果（如果启用）
                                    if do_pixelate:
                                        # 确保滤镜包含基本缩放
                                        pixel_filter = f',scale=-1:{target_height}'
                                        
                                        # 增强RGB抖动效果，使其明显
                                        shift_amount = random.randint(1, 3)  # RGB分离强度
                                        pixel_filter += f',rgbashift=rh={shift_amount}:bh=-{shift_amount}'
                                    # 如果没有RGB抖动但有色彩变化，添加基本缩放
                                    elif do_color_shift:
                                        pixel_filter = f',scale=-1:{target_height}'
                                    
                                    # 添加色彩变化效果（如果启用）
                                    if do_color_shift:
                                        # 生成随机参数，使色彩变化更加随机无规律
                                        # 生成随机频率和振幅
                                        h_amp = random.uniform(0.15, 0.4)  # 色相振幅
                                        h_freq1 = random.uniform(1, 3)  # 色相频率1
                                        h_freq2 = random.uniform(0.5, 2)  # 色相频率2
                                        s_amp = random.uniform(0.3, 0.6)  # 饱和度振幅
                                        s_freq = random.uniform(0.8, 2.5)  # 饱和度频率
                                        b_amp = random.uniform(0.15, 0.3)  # 亮度振幅
                                        b_freq = random.uniform(1, 3)  # 亮度频率
                                        
                                        # 根据强度选择色彩效果，添加随机性
                                        if color_shift_factor == 1:
                                            # 随机色相变化 - 混合两个不同频率
                                            hue_expr = f'h={h_amp}*sin(2*PI*t*{h_freq1})+{h_amp/3}*cos(2*PI*t*{h_freq2})'
                                            pixel_filter += f',hue={hue_expr}'
                                        elif color_shift_factor == 2:
                                            # 随机色相+饱和度变化
                                            hue_expr = f'h={h_amp}*sin(2*PI*t*{h_freq1})+{h_amp/2.5}*cos(2*PI*t*{h_freq2})'
                                            sat_expr = f's=1+{s_amp}*sin(2*PI*t*{s_freq})'
                                            pixel_filter += f',hue={hue_expr}:{sat_expr}'
                                        else:  # 强度3
                                            # 随机色相+饱和度+亮度变化，同时混合多个频率
                                            hue_expr = f'h={h_amp}*sin(2*PI*t*{h_freq1})+{h_amp/2}*cos(2*PI*t*{h_freq2})'
                                            sat_expr = f's=1+{s_amp}*sin(2*PI*t*{s_freq})'
                                            bright_expr = f'b=1+{b_amp}*sin(2*PI*t*{b_freq})'
                                            pixel_filter += f',hue={hue_expr}:{sat_expr}:{bright_expr}'
                                    
                                    # 组合完整的视频B滤镜
                                    video_b_filter = f'[1:v]{base_filter_b}{pixel_filter},setsar=1[video_b];'
                                    
                                    # 创建完整的滤镜字符串
                                    filter_complex_str = f'[0:v]scale=1080:1920:force_original_aspect_ratio=increase,crop=1080:1920[bg];' + \
                                                        video_b_filter + \
                                                        f'[bg][video_b]overlay=(W-w)/2+{int(1080*offset_percent_b)}:(H-h)/2[v]'
                                        
                                    cmd_combine = [
                                        'ffmpeg', '-y',
                                        # 移除硬件设备初始化，让滤镜使用软件处理
                                        '-loop', '1', '-i', bg_image,  # 背景图片
                                        '-stream_loop', '-1', '-i', video_b_path,  # 视频B
                                        
                                        # 使用标准filter_complex，完全由软件处理
                                        '-filter_complex',
                                        filter_complex_str,
                                        '-map', '[v]',                                 # 映射视频流
                                        '-map', '1:a?',                                # 映射视频B的音频(如果存在)
                                        
                                        # 在滤镜处理完成后，再使用QSV硬件加速编码
                                        '-init_hw_device', 'qsv=qsv:MFX_IMPL_hw_any',  # 初始化QSV，但仅用于编码
                                        '-c:v', 'h264_qsv',                            # QSV硬件编码
                                        '-c:a', 'aac',                                 # 音频编码为AAC
                                        '-preset', 'veryslow',                         # 使用最高质量预设
                                        '-global_quality', '1',                        # QSV全局质量参数（1为最高质量）
                                        '-look_ahead', '1',                            # 启用前瞻缓冲
                                        '-b:v', '50000k',                              # 高码率，确保高质量
                                        '-maxrate', '60000k',                          # 最大比特率
                                        '-bufsize', '60000k',                          # 缓冲大小
                                        '-vsync', 'cfr',                               # 强制恒定帧率
                                        '-r', str(video_b_fps),                        # 使用视频B的帧率
                                        '-t', str(info_a['duration']),                 # 设置时长（使用视频A的时长）
                                        '-frames:v', str(a_expected_frames),           # 设置总帧数
                                        '-pix_fmt', 'yuv420p',                         # 设置像素格式
                                        temp_b_loop                                    # 输出文件
                                    ]
                                    
                                    try:
                                        # 执行命令
                                        subprocess.run(cmd_combine, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
                                        
                                        self.log_text.insert(tk.END, "图片边框处理成功 (单步QSV硬件加速)\n")
                                        # 将temp_b设为temp_b_loop，但不要提前返回
                                        temp_b = temp_b_loop  # 更新视频B的路径
                                        
                                        # 检查是否成功生成了文件
                                        if os.path.exists(temp_b) and os.path.getsize(temp_b) > 0:
                                            # 如果生成了有效的文件，跳过下面的硬件加速尝试
                                            self.log_text.insert(tk.END, "已生成有效的图片边框视频文件，跳过传统处理\n")
                                            # 设置一个标记表明图片边框已经成功处理
                                            border_processed = True
                                        else:
                                            self.log_text.insert(tk.END, "图片边框处理未生成有效文件，将尝试传统处理\n")
                                            border_processed = False
                                    except Exception as e:
                                        self.log_text.insert(tk.END, f"图片边框合成失败: {str(e)}\n")
                                        self.log_text.insert(tk.END, "切换到传统模式处理视频B...\n")
                                        border_processed = False
                                    
                                except Exception as e:
                                    self.log_text.insert(tk.END, f"图片边框处理失败: {str(e)}\n")
                                    self.log_text.insert(tk.END, "切换到传统模式处理视频B...\n")
                                    border_processed = False
                    else:
                        # 如果不使用图片边框，设置标记为False
                        border_processed = False
                    
                    # 如果图片边框处理未成功，则使用传统方式处理
                    if not border_processed:
                        # 首先尝试使用硬件加速
                        self.log_text.insert(tk.END, "使用硬件加速处理视频B...\n")
                        cmd_process = [
                            'ffmpeg', '-y',
                            '-init_hw_device', 'qsv=qsv:MFX_IMPL_hw_any',
                            '-filter_hw_device', 'qsv',
                            '-stream_loop', '-1',  # 无限循环，确保足够长
                            '-i', video_b_path,  # 直接使用原始视频B
                            '-vf', vf_option,
                            '-an',
                            '-c:v', 'h264_qsv',  # 使用QSV硬件编码
                            '-preset', 'medium',
                            '-global_quality', '1',  # QSV质量控制
                            '-look_ahead', '1',  # 启用前瞻缓冲
                            '-vsync', 'cfr',  # 强制恒定帧率
                            '-r', str(video_b_fps),  # 确保输出帧率为自定义
                            '-t', str(info_a['duration']),  # 精确匹配视频A的时长
                            '-frames:v', str(a_expected_frames),  # 精确匹配视频A的帧数
                            '-pix_fmt', 'yuv420p',  # 设置像素格式
                            temp_b_loop
                        ]
                        try:
                            subprocess.run(cmd_process, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
                            self.log_text.insert(tk.END, "使用硬件加速处理成功\n")
                            temp_b = temp_b_loop  # 更新视频B的路径
                        except subprocess.CalledProcessError as e:
                            self.log_text.insert(tk.END, "硬件加速失败，切换到软件编码...\n")
                            # 如果硬件加速失败，使用软件编码
                            cmd_process = [
                                'ffmpeg', '-y',
                                '-stream_loop', '-1',  # 无限循环，确保足够长
                                '-i', video_b_path,  # 直接使用原始视频B
                                '-vf', vf_option,  # 使用与硬件加速相同的视频滤镜选项
                                '-an',
                                '-c:v', 'libx264',  # 使用x264编码器
                                '-preset', 'medium',  # 编码器预设
                                '-crf', '1',  # 质量设置
                                '-vsync', 'cfr',  # 强制恒定帧率
                                '-r', str(video_b_fps),  # 确保输出帧率为自定义
                                '-t', str(info_a['duration']),  # 精确匹配视频A的时长
                                '-frames:v', str(a_expected_frames),  # 精确匹配视频A的帧数
                                '-pix_fmt', 'yuv420p',  # 设置像素格式
                                temp_b_loop
                            ]
                            try:
                                subprocess.run(cmd_process, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
                                self.log_text.insert(tk.END, "使用软件编码处理成功\n")
                                temp_b = temp_b_loop  # 更新视频B的路径
                            except subprocess.CalledProcessError as e:
                                self.log_text.insert(tk.END, f"处理视频B失败: {str(e)}\n")
                                return False
                except Exception as e:
                    self.log_text.insert(tk.END, f"处理视频B失败: {str(e)}\n")
                    import traceback
                    self.log_text.insert(tk.END, f"详细错误:\n{traceback.format_exc()}\n")
                    return False

                # 验证处理后的视频B
                processed_b_info = self.get_video_info(temp_b)
                if not processed_b_info:
                    self.log_text.insert(tk.END, "无法获取处理后的视频B信息\n")
                    return False
                    
                # 处理视频叠加功能
                # 记录process_options中的关键值以便调试
                if process_options:
                    self.log_text.insert(tk.END, f"叠加功能状态: {'启用' if process_options.get('enable_overlay', False) else '禁用'}\n")
                    if 'processed_b_folder' in process_options:
                        folder = process_options.get('processed_b_folder', '')
                        if folder:
                            self.log_text.insert(tk.END, f"叠加视频文件夹: {folder} (存在: {os.path.exists(folder)})\n")
                        else:
                            self.log_text.insert(tk.END, "叠加视频文件夹未设置\n")
                    else:
                        self.log_text.insert(tk.END, "未指定叠加视频文件夹\n")
                
                processed_b_folder = process_options.get('processed_b_folder')
                if process_options and process_options.get('enable_overlay', False) and processed_b_folder and os.path.exists(processed_b_folder):
                    self.log_text.insert(tk.END, "开始进行视频叠加处理...\n")
                    overlay_opacity = process_options.get('overlay_opacity', 0.5)
                    
                    # 查找叠加视频文件夹中的视频文件
                    processed_b_files = [os.path.join(processed_b_folder, f) for f in os.listdir(processed_b_folder) 
                                         if f.lower().endswith(('.mp4', '.mov', '.avi', '.mkv'))]
                    
                    if not processed_b_files:
                        self.log_text.insert(tk.END, f"错误：叠加视频文件夹中没有找到任何视频文件\n")
                        return False
                    else:
                        # 从叠加视频文件夹中随机选择一个视频
                        overlay_video = random.choice(processed_b_files)
                        self.log_text.insert(tk.END, f"选择叠加视频: {os.path.basename(overlay_video)}\n")
                        
                        # 获取主视频和叠加视频的时长信息
                        main_video_info = self.get_video_info(temp_b)
                        overlay_video_info = self.get_video_info(overlay_video)
                        
                        if not main_video_info or not overlay_video_info:
                            self.log_text.insert(tk.END, "无法获取视频信息，跳过叠加处理\n")
                        else:
                            main_duration = main_video_info['duration']
                            overlay_duration = overlay_video_info['duration']
                            
                            self.log_text.insert(tk.END, f"主视频时长: {main_duration:.2f}秒, 叠加视频时长: {overlay_duration:.2f}秒\n")
                            self.log_text.insert(tk.END, f"主视频分辨率: {main_video_info['width']}x{main_video_info['height']}, 叠加视频将缩放以完全填充画面\n")
                            
                            # 创建临时输出文件
                            overlay_output = os.path.join(os.path.dirname(temp_b), f"overlay_{os.path.basename(temp_b)}")
                            
                            # 如果叠加视频较短，需要循环播放
                            if overlay_duration < main_duration:
                                self.log_text.insert(tk.END, f"叠加视频较短，将使用循环叠加模式\n")
                                loop_count = math.ceil(main_duration / overlay_duration)
                                self.log_text.insert(tk.END, f"循环次数: {loop_count}\n")
                                self.log_text.insert(tk.END, f"透明度值: {overlay_opacity}\n")
                                
                                # 使用QSV硬件加速的叠加方式
                                cmd_overlay = [
                                    'ffmpeg', '-y',
                                    '-init_hw_device', 'qsv=qsv:MFX_IMPL_hw_any',
                                    '-filter_hw_device', 'qsv',
                                    '-i', temp_b,  # 主视频
                                    '-stream_loop', str(loop_count),  # 循环播放叠加视频
                                    '-i', overlay_video,  # 叠加视频
                                    '-filter_complex', 
                                    f'[1:v]scale={main_video_info["width"]}:{main_video_info["height"]}[scaled];[0:v][scaled]blend=all_opacity={overlay_opacity}:all_mode=normal:enable=\'between(t,0,{main_duration})\'',
                                    '-c:v', 'h264_qsv',  # 使用QSV硬件编码
                                    '-preset', 'veryslow',
                                    '-global_quality', '1',  # QSV质量参数
                                    '-pix_fmt', 'nv12',  # QSV需要nv12格式
                                    '-r', str(video_b_fps),  # 使用视频B的帧率
                                    '-t', str(main_duration),  # 限制输出时长为主视频时长
                                    overlay_output
                                ]
                            else:
                                # 标准叠加命令，无需循环，使用QSV硬件加速
                                self.log_text.insert(tk.END, f"叠加视频足够长，使用标准叠加模式（QSV硬件加速）\n")
                                self.log_text.insert(tk.END, f"透明度值: {overlay_opacity}\n")
                                cmd_overlay = [
                                    'ffmpeg', '-y',
                                    '-init_hw_device', 'qsv=qsv:MFX_IMPL_hw_any',
                                    '-filter_hw_device', 'qsv',
                                    '-i', temp_b,  # 主视频
                                    '-i', overlay_video,  # 叠加视频
                                    '-filter_complex', f'[1:v]scale={main_video_info["width"]}:{main_video_info["height"]}[scaled];[0:v][scaled]blend=all_opacity={overlay_opacity}:all_mode=normal:enable=\'between(t,0,{main_duration})\'',
                                    '-c:v', 'h264_qsv',  # 使用QSV硬件编码
                                    '-preset', 'veryslow',
                                    '-global_quality', '1',  # QSV质量参数
                                    '-pix_fmt', 'nv12',  # QSV需要nv12格式
                                    '-r', str(video_b_fps),  # 使用视频B的帧率
                                    overlay_output
                                ]
                            
                            try:
                                self.log_text.insert(tk.END, f"开始QSV硬件加速叠加处理，透明度: {overlay_opacity:.2f}\n")
                                subprocess.run(cmd_overlay, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
                                
                                # 如果成功，将叠加后的视频设置为当前处理视频
                                if os.path.exists(overlay_output):
                                    temp_b = overlay_output
                                    self.log_text.insert(tk.END, "视频叠加处理完成（QSV加速）\n")
                                else:
                                    self.log_text.insert(tk.END, "警告：叠加处理后的视频文件不存在，使用原始处理视频继续\n")
                            except subprocess.CalledProcessError as e:
                                self.log_text.insert(tk.END, f"QSV硬件加速叠加处理失败: {e.stderr.decode() if e.stderr else str(e)}\n")
                                self.log_text.insert(tk.END, "继续使用原始处理视频\n")

                # 确保temp_b指向有效的处理后视频
                if not os.path.exists(temp_b):
                    self.log_text.insert(tk.END, f"错误：处理后的视频B文件不存在: {temp_b}\n")
                    return False

                # 计算实际帧数
                b_actual_frames = int(processed_b_info['duration'] * processed_b_info['fps'])
                
                # 详细输出验证信息
                self.log_text.insert(tk.END, f"\n处理后视频B - 时长: {processed_b_info['duration']:.2f}秒, "
                                            f"帧率: {processed_b_info['fps']:.2f}fps, "
                                            f"实际帧数: {b_actual_frames}\n")
                
                # 精确验证时长和帧数
                duration_diff = abs(info_a['duration'] - processed_b_info['duration'])
                frames_diff = abs(a_expected_frames - b_actual_frames)
                
                if duration_diff > 0.05:  # 允许0.05秒的误差
                    self.log_text.insert(tk.END, f"警告：视频时长不匹配，差异: {duration_diff:.3f}秒\n")
                else:
                    self.log_text.insert(tk.END, f"视频时长匹配，差异仅: {duration_diff:.3f}秒\n")
                    
                if frames_diff > 3:  # 允许最多3帧的差异
                    self.log_text.insert(tk.END, f"警告：视频帧数不匹配，差异: {frames_diff}帧\n")
                else:
                    self.log_text.insert(tk.END, f"视频帧数匹配，差异仅: {frames_diff}帧\n")

                # 注意：不要覆盖叠加处理后的视频路径
                # 如果之前没有进行过叠加处理，才使用轨道循环处理的结果
                if "overlay_" not in temp_b:
                    temp_b = temp_b_loop
                    self.log_text.insert(tk.END, "使用轨道循环处理后的视频B作为最终输入\n")
                else:
                    self.log_text.insert(tk.END, f"使用叠加处理后的视频B作为最终输入: {os.path.basename(temp_b)}\n")

                # 生成随机比特率 (30000, 40000)
                bitrate = random.randint(30000, 40000)
                self.log_text.insert(tk.END, f"设置最终视频比特率: {bitrate}k\n")

                # 交错顺序控制
                if interleave_mode == "baba":
                    interleave_filter = f'[0:v]setpts=PTS-STARTPTS,fps={video_b_fps}[v0];[1:v]setpts=PTS-STARTPTS,fps={video_a_fps}[v1];[v0][v1]interleave=nb_inputs=2[v]'
                else:
                    interleave_filter = f'[1:v]setpts=PTS-STARTPTS,fps={video_a_fps}[v0];[0:v]setpts=PTS-STARTPTS,fps={video_b_fps}[v1];[v0][v1]interleave=nb_inputs=2[v]'

                # 最终合成 - 使用interleave滤镜交错合成自定义输出帧率
                self.log_text.insert(tk.END, f"正在进行{output_fps}fps合成...\n")
                self.log_text.insert(tk.END, f"使用视频B路径: {os.path.basename(temp_b)}\n")
                # 首先尝试使用硬件加速
                cmd_final = [
                    'ffmpeg', '-y',
                    '-init_hw_device', 'qsv=qsv:MFX_IMPL_hw_any',
                    '-filter_hw_device', 'qsv',
                    '-i', temp_b,  # 使用当前处理后的视频B（可能经过叠加处理）
                    '-i', temp_a,  # 视频A
                    '-i', temp_audio,  # 音频
                    '-filter_complex',
                    interleave_filter,
                    '-map', '[v]',
                    '-map', '2:a',
                    '-c:v', 'h264_qsv',  # 使用QSV硬件编码
                    '-preset', 'veryslow',  # 使用更慢的预设提高质量
                    '-b:v', f'{bitrate}k',  # 使用随机生成的视频比特率
                    '-minrate', f'{bitrate}k',  # 最小比特率与目标一致(CBR模式)
                    '-maxrate', f'{bitrate}k',  # 最大比特率与目标一致(CBR模式)
                    '-bufsize', f'{bitrate}k',  # 缓冲区大小与比特率相同，确保严格CBR
                    '-look_ahead', '1',
                    '-c:a', 'aac',
                    '-b:a', '192k',
                    '-ar', '48000',
                    '-pix_fmt', 'yuv420p',
                    '-r', str(output_fps),  # 输出自定义帧率
                    '-fps_mode', 'cfr',  # 使用恒定帧率模式
                    '-t', str(info_a['duration']),  # 使用视频A的时长作为最终输出时长
                    output_path
                ]
                try:
                    subprocess.run(cmd_final, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
                    self.log_text.insert(tk.END, "使用硬件加速合成成功\n")
                except subprocess.CalledProcessError as e:
                    self.log_text.insert(tk.END, "硬件加速失败，切换到软件编码...\n")
                    # 如果硬件加速失败，使用软件编码
                    cmd_final = [
                        'ffmpeg', '-y',
                        '-i', temp_b,  # 使用当前处理后的视频B（可能经过叠加处理）
                        '-i', temp_a,  # 视频A
                        '-i', temp_audio,  # 音频
                        '-filter_complex',
                        interleave_filter,
                        '-map', '[v]',
                        '-map', '2:a',
                        '-c:v', 'libx264',  # 使用软件编码
                        '-preset', 'slow',  # 使用更慢的预设提高质量
                        '-b:v', f'{bitrate}k',  # 先指定目标比特率
                        '-minrate', f'{bitrate}k',  # 最小比特率(CBR模式)
                        '-maxrate', f'{bitrate}k',  # 最大比特率(CBR模式)
                        '-bufsize', f'{bitrate}k',  # 缓冲区大小，严格CBR
                        '-c:a', 'aac',
                        '-b:a', '192k',
                        '-ar', '48000',
                        '-pix_fmt', 'yuv420p',
                        '-r', str(output_fps),  # 输出自定义帧率
                        '-fps_mode', 'cfr',  # 使用恒定帧率模式
                        '-t', str(info_a['duration']),  # 使用视频A的时长作为最终输出时长
                        output_path
                    ]
                    try:
                        subprocess.run(cmd_final, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
                        self.log_text.insert(tk.END, "使用软件编码合成成功\n")
                    except subprocess.CalledProcessError as e:
                        self.log_text.insert(tk.END, f"视频处理失败: {e.stderr.decode() if e.stderr else str(e)}\n")
                        return False

                # 验证最终输出
                final_info = self.get_video_info(output_path)
                if final_info:
                    self.log_text.insert(tk.END, f"\n最终视频信息:\n")
                    self.log_text.insert(tk.END, f"帧率: {final_info['fps']}fps\n")
                    self.log_text.insert(tk.END, f"时长: {final_info['duration']:.3f}秒\n")
                    expected_frames = int(info_a['duration'] * output_fps)  # 期望的总帧数
                    actual_frames = int(final_info['duration'] * final_info['fps'])
                    self.log_text.insert(tk.END, f"总帧数: {actual_frames} (期望: {expected_frames})\n")

                self.log_text.insert(tk.END, "轨道复用处理完成\n")
                return True

            finally:
                # 清理临时文件夹
                if os.path.exists(temp_folder):
                    try:
                        shutil.rmtree(temp_folder)
                    except Exception as e:
                        self.log_text.insert(tk.END, f"清理临时文件失败: {str(e)}\n")

        except Exception as e:
            self.log_text.insert(tk.END, f"轨道复用处理失败: {str(e)}\n")
            # 添加详细的错误信息
            import traceback
            self.log_text.insert(tk.END, f"详细错误:\n{traceback.format_exc()}\n")
            return False    
    def modify_video_duration(self, input_path, output_path):
        try:
            self.log_text.insert(tk.END, "正在读取视频文件...\n")
            with open(input_path, 'rb') as f:
                data = bytearray(f.read())

            target_duration = 10.0  # 目标时长10秒
            atoms_modified = set()

            def find_atom(start_offset, end_offset, atom_name):
                current = start_offset
                while current < end_offset - 8:
                    try:
                        size = int.from_bytes(data[current:current + 4], 'big')
                        if size < 8:  # 无效的原子大小
                            current += 4
                            continue
                        if data[current + 4:current + 8] == atom_name:
                            return current
                        current += size
                    except:
                        current += 4
                return -1

            # 首先找到 moov 原子
            moov_offset = find_atom(0, len(data), b'moov')
            if moov_offset == -1:
                self.log_text.insert(tk.END, "未找到 moov 原子\n")
                return False

            moov_size = int.from_bytes(data[moov_offset:moov_offset + 4], 'big')
            moov_end = moov_offset + moov_size

            # 1. 修改 mvhd（电影头原子）
            mvhd_offset = find_atom(moov_offset + 8, moov_end, b'mvhd')
            if mvhd_offset != -1:
                version = data[mvhd_offset + 8]
                if version == 0:  # 32位版本
                    timescale = int.from_bytes(data[mvhd_offset + 20:mvhd_offset + 24], 'big')
                    if timescale == 0:
                        timescale = 90000
                        data[mvhd_offset + 20:mvhd_offset + 24] = timescale.to_bytes(4, 'big')
                    new_duration = int(target_duration * timescale)
                    data[mvhd_offset + 24:mvhd_offset + 28] = new_duration.to_bytes(4, 'big')
                else:  # 64位版本
                    timescale = int.from_bytes(data[mvhd_offset + 28:mvhd_offset + 32], 'big')
                    if timescale == 0:
                        timescale = 90000
                        data[mvhd_offset + 28:mvhd_offset + 32] = timescale.to_bytes(4, 'big')
                    new_duration = int(target_duration * timescale)
                    data[mvhd_offset + 32:mvhd_offset + 40] = new_duration.to_bytes(8, 'big')
                atoms_modified.add('mvhd')

            # 2. 遍历所有轨道，修改 trak 原子
            current_offset = moov_offset + 8
            while current_offset < moov_end - 8:
                size = int.from_bytes(data[current_offset:current_offset + 4], 'big')
                if size < 8:
                    break

                if data[current_offset + 4:current_offset + 8] == b'trak':
                    trak_end = current_offset + size

                    # 2.1 修改 tkhd（轨道头原子）
                    tkhd_offset = find_atom(current_offset + 8, trak_end, b'tkhd')
                    if tkhd_offset != -1:
                        version = data[tkhd_offset + 8]
                        if version == 0:
                            data[tkhd_offset + 28:tkhd_offset + 32] = int(target_duration * 90000).to_bytes(4, 'big')
                        else:
                            data[tkhd_offset + 36:tkhd_offset + 44] = int(target_duration * 90000).to_bytes(8, 'big')
                        atoms_modified.add('tkhd')

                    # 2.2 查找并修改 mdia/mdhd（媒体头原子）
                    mdia_offset = find_atom(current_offset + 8, trak_end, b'mdia')
                    if mdia_offset != -1:
                        mdia_size = int.from_bytes(data[mdia_offset:mdia_offset + 4], 'big')
                        mdhd_offset = find_atom(mdia_offset + 8, mdia_offset + mdia_size, b'mdhd')
                        if mdhd_offset != -1:
                            version = data[mdhd_offset + 8]
                            if version == 0:
                                media_timescale = int.from_bytes(data[mdhd_offset + 20:mdhd_offset + 24], 'big')
                                new_duration = int(target_duration * media_timescale)
                                data[mdhd_offset + 24:mdhd_offset + 28] = new_duration.to_bytes(4, 'big')
                            else:
                                media_timescale = int.from_bytes(data[mdhd_offset + 28:mdhd_offset + 32], 'big')
                                new_duration = int(target_duration * media_timescale)
                                data[mdhd_offset + 32:mdhd_offset + 40] = new_duration.to_bytes(8, 'big')
                            atoms_modified.add('mdhd')

                current_offset += size

            # 3. 修改 meta 原子（如果存在）
            meta_offset = find_atom(0, len(data), b'meta')
            if meta_offset != -1:
                meta_size = int.from_bytes(data[meta_offset:meta_offset + 4], 'big')
                hdlr_offset = find_atom(meta_offset + 8, meta_offset + meta_size, b'hdlr')
                if hdlr_offset != -1:
                    # 修改元数据中的时长信息
                    meta_str = f"{target_duration:.3f}".encode('utf-8')
                    for i in range(hdlr_offset, meta_offset + meta_size - len(meta_str)):
                        if data[i:i + 8] == b'duration':
                            # 在找到 duration 字段后搜索数字并替换
                            for j in range(i + 8, i + 32):
                                if chr(data[j]).isdigit():
                                    data[j:j + len(meta_str)] = meta_str
                                    break
                    atoms_modified.add('meta')

            if not atoms_modified:
                self.log_text.insert(tk.END, "未找到任何可修改的时长相关原子\n")
                return False

            self.log_text.insert(tk.END, f"已修改的原子: {', '.join(atoms_modified)}\n")

            # 保存修改
            with open(output_path, 'wb') as f:
                f.write(data)

            return True

        except Exception as e:
            self.log_text.insert(tk.END, f"修改时长失败: {str(e)}\n")
            import traceback
            self.log_text.insert(tk.END, f"错误详情:\n{traceback.format_exc()}\n")
            return False

    def clear_metadata(self, input_path, output_path):
        try:
            self.log_text.insert(tk.END, "开始清除视频元数据...\n")

            # 获取原始视频信息
            info = self.get_video_info(input_path)
            if not info:
                self.log_text.insert(tk.END, "获取视频信息失败\n")
                return False

            # 使用FFmpeg清除元数据
            cmd = [
                'ffmpeg', '-y',
                '-i', input_path,
                '-map_metadata', '-1',  # 移除所有元数据
                '-codec', 'copy',  # 复制编解码器（不重新编码）
                '-movflags', '+faststart',  # 优化网络播放
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)
            if result.returncode != 0:
                self.log_text.insert(tk.END, f"清除元数据失败: {result.stderr}\n")
                return False

            # 使用ExifTool清除残余元数据
            exif_cmd = [
                EXIFTOOL_PATH,  # 使用全局配置的ExifTool路径
                '-overwrite_original',
                '-all=',  # 清除所有标签
                '-TagsFromFile', '@',  # 从原文件读取
                # 仅保留基本信息
                '-VideoFrameRate',
                '-Duration',
                '-FileName',
                output_path
            ]

            result = subprocess.run(exif_cmd, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)
            if result.returncode != 0:
                self.log_text.insert(tk.END, f"ExifTool处理失败: {result.stderr}\n")
                return False

            self.log_text.insert(tk.END, "元数据清除完成\n")
            return True

        except Exception as e:
            self.log_text.insert(tk.END, f"清除元数据时出错: {str(e)}\n")
            return False
    def _verify_duration(self, file_path, expected_duration):
        """验证修改后的视频时长"""
        info = self.get_video_info(file_path)
        return info and abs(info['duration'] - expected_duration) < 1.0

    def process_single_video(self, video_a_path, video_b_folder, frame_interval, output_folder, temp_folder,
                             process_options):
        """
        处理单个视频的统一流程
        """
        try:
            os.makedirs(temp_folder, exist_ok=True)
            current_file = video_a_path
            
            # 生成输出文件名（保留原始文件名）
            output_file = os.path.basename(video_a_path)
            final_output = os.path.join(output_folder, output_file)
            
            # 统一规范化所有路径
            video_a_path = os.path.normpath(video_a_path)
            video_b_folder = os.path.normpath(video_b_folder)
            output_folder = os.path.normpath(output_folder)
            temp_folder = os.path.normpath(temp_folder)
            final_output = os.path.normpath(final_output)
            
            # 打印当前启用的所有功能
            self.log_text.insert(tk.END, "当前启用的功能:\n")
            for key, value in process_options.items():
                if key.startswith('enable_') and value:
                    self.log_text.insert(tk.END, f"- {key.replace('enable_', '')}: 已启用\n")
            
            # 检查并打印叠加视频设置
            if process_options.get('enable_overlay'):
                self.log_text.insert(tk.END, f"叠加视频设置: 启用=是, 文件夹={process_options.get('processed_b_folder', '未设置')}, 透明度={process_options.get('overlay_opacity', 0.5):.2f}\n")

      

            # 调试输出所有处理选项
            self.log_text.insert(tk.END, "当前处理选项详情:\n")
            self.log_text.insert(tk.END, f"轨道融合: {process_options.get('enable_track_reuse', False)}\n")
            self.log_text.insert(tk.END, f"视频叠加: {process_options.get('enable_overlay', False)}\n")
            self.log_text.insert(tk.END, f"叠加视频文件夹: {process_options.get('processed_b_folder', '未设置')}\n")
            self.log_text.insert(tk.END, f"叠加透明度: {process_options.get('overlay_opacity', 0.5)}\n")
            
            # 4. 轨道融合处理
            if process_options['enable_track_reuse']:
                self.log_text.insert(tk.END, "开始轨道融合处理...\n")
                temp_track_reuse = os.path.normpath(os.path.join(temp_folder, 'track_reuse_temp.mp4'))
                
                # 输出图片边框设置以便调试
                self.log_text.insert(tk.END, f"传递到轨道融合的图片边框设置: 启用={process_options.get('enable_image_border', False)}, 文件夹={process_options.get('image_folder', '未设置')}\n")
                
                # 传递所有参数，包括完整的process_options
                # 输出叠加视频相关的设置以便调试
                self.log_text.insert(tk.END, f"叠加视频设置: 启用={process_options.get('enable_overlay', False)}, 文件夹={process_options.get('processed_b_folder', '未设置')}, 透明度={process_options.get('overlay_opacity', 0.5):.2f}\n")

                if not self.apply_track_reuse(current_file, video_b_folder, temp_track_reuse,
                                          process_options['video_a_fps'],
                                          process_options['video_b_fps'],
                                          process_options['output_fps'],
                                          process_options['interleave_mode'],
                                          process_options.get('enable_use_a_clips', True),
                                          process_options):  # 传递完整的process_options
                    self.log_text.insert(tk.END, "轨道融合处理失败\n")
                    return False
                
                # 验证生成的文件是否存在
                if not os.path.exists(temp_track_reuse):
                    self.log_text.insert(tk.END, f"错误：轨道融合处理后的文件不存在: {temp_track_reuse}\n")
                    return False
                
                current_file = temp_track_reuse

            # 4. 三连屏处理
            if process_options['enable_triple']:
                self.log_text.insert(tk.END, "开始三连屏处理...\n")
                # 使用三连屏视频B文件夹（如果设置了），否则使用普通视频B文件夹
                triple_b_folder = process_options.get('triple_b_folder', '')
                if triple_b_folder and os.path.exists(triple_b_folder):
                    self.log_text.insert(tk.END, f"使用三连屏专用视频B文件夹: {triple_b_folder}\n")
                    video_b_files = [f for f in os.listdir(triple_b_folder)
                                    if f.endswith(('.mp4', '.avi', '.mov'))]
                    selected_b_videos = random.sample(video_b_files, 2)
                    b_paths = [os.path.normpath(os.path.join(triple_b_folder, v)) for v in selected_b_videos]
                else:
                    self.log_text.insert(tk.END, f"使用普通视频B文件夹: {video_b_folder}\n")
                    video_b_files = [f for f in os.listdir(video_b_folder)
                                    if f.endswith(('.mp4', '.avi', '.mov'))]
                    selected_b_videos = random.sample(video_b_files, 2)
                    b_paths = [os.path.normpath(os.path.join(video_b_folder, v)) for v in selected_b_videos]
                temp_triple = os.path.normpath(os.path.join(temp_folder, 'triple_temp.mp4'))
                
                # 使用处理后的视频帧率（即output_fps）
                output_fps = process_options.get('output_fps', 120)
                self.log_text.insert(tk.END, f"三连屏处理使用输出帧率: {output_fps} fps\n")
                
                if not self.create_triple_screen_video(current_file, b_paths, temp_triple, output_fps):
                    self.log_text.insert(tk.END, "三连屏处理失败\n")
                    return False
                
                # 确认生成的文件存在
                if not os.path.exists(temp_triple):
                    self.log_text.insert(tk.END, f"错误：三连屏处理后的文件不存在: {temp_triple}\n")
                    return False
                
                current_file = temp_triple

            # 5. 音频处理
            if process_options['enable_audio']:
                temp_audio = os.path.normpath(os.path.join(temp_folder, 'audio_temp.mp4'))
                # 传递音频文件夹和音量参数
                audio_folder = process_options.get('audio_folder', None)
                if audio_folder:
                    audio_folder = os.path.normpath(audio_folder)
                bg_volume = process_options.get('bg_volume', 0.06)
                if not self.process_audio(current_file, video_b_folder, temp_audio, audio_folder, bg_volume):
                    self.log_text.insert(tk.END, "音频处理失败\n")
                    return False
                
                # 验证生成的文件是否存在
                if not os.path.exists(temp_audio):
                    self.log_text.insert(tk.END, f"错误：音频处理后的文件不存在: {temp_audio}\n")
                    return False
                
                current_file = temp_audio

            # 6. 模板视频合并
            if process_options.get('enable_template', False) and process_options.get('template_folder'):
                self.log_text.insert(tk.END, "开始与模板视频合并...\n")
                temp_combined = os.path.normpath(os.path.join(temp_folder, 'combined_temp.mp4'))
                template_folder = os.path.normpath(process_options['template_folder'])
                
                if not os.path.exists(template_folder) or not os.path.isdir(template_folder):
                    self.log_text.insert(tk.END, f"模板视频文件夹不存在: {template_folder}\n")
                else:
                    # 执行模板视频合并，传递UI设置的输出帧率
                    output_fps = process_options['output_fps']
                    # 使用current_file作为输入源，而不是final_output
                    self.log_text.insert(tk.END, f"使用当前处理文件作为模板合并输入: {current_file}\n")
                    if not self.combine_with_template(current_file, template_folder, temp_combined, output_fps):
                        self.log_text.insert(tk.END, "模板视频合并失败\n")
                    else:
                        # 合并成功，替换最终输出
                        if os.path.exists(final_output):
                            os.remove(final_output)
                        shutil.move(temp_combined, final_output)
                        # 重要：更新当前处理文件为模板合并后的文件
                        current_file = final_output
                        self.log_text.insert(tk.END, "模板视频合并完成\n")
            
                         # 7. 元数据处理
            if process_options['enable_metadata']:
                # 确保使用正确的源文件
                # 如果没有进行模板合并，使用当前文件作为源文件
                if not process_options['enable_template'] or not os.path.exists(final_output):
                    source_file = current_file
                    self.log_text.insert(tk.END, f"未启用模板合并，使用当前处理文件作为元数据处理源: {source_file}\n")
                else:
                    source_file = final_output
                
                temp_metadata = os.path.join(temp_folder, 'metadata_temp.mp4')
                
                # 检查源文件是否存在
                if not os.path.exists(source_file):
                    self.log_text.insert(tk.END, f"错误: 元数据处理的源文件不存在: {source_file}\n")
                    return False
                    
                # 检查源文件大小
                if os.path.getsize(source_file) == 0:
                    self.log_text.insert(tk.END, f"错误: 元数据处理的源文件大小为0: {source_file}\n")
                    return False
                
                self.log_text.insert(tk.END, f"开始元数据处理，源文件: {source_file} ({os.path.getsize(source_file)} 字节)\n")
                
                if not self.modify_metadata(source_file, temp_metadata, process_options['output_fps']):
                    self.log_text.insert(tk.END, "元数据修改失败\n")
                    return False
                
                # 检查临时文件是否创建成功
                if not os.path.exists(temp_metadata) or os.path.getsize(temp_metadata) == 0:
                    self.log_text.insert(tk.END, f"错误: 元数据处理后的临时文件不存在或为空: {temp_metadata}\n")
                    return False
                
                # 替换最终输出
                if os.path.exists(final_output):
                    os.remove(final_output)
                shutil.move(temp_metadata, final_output)
                # 更新当前处理文件为元数据处理后的文件
                current_file = final_output
                self.log_text.insert(tk.END, f"元数据处理完成，文件大小: {os.path.getsize(final_output)} 字节\n")
            
            # 验证处理结果
            if process_options['enable_metadata'] and not self.verify_metadata(final_output):
                self.log_text.insert(tk.END, "元数据验证失败\n")
                return False

            # 确保最终视频输出到指定的输出文件夹
            # 如果当前处理文件不在最终输出位置，则复制到最终输出位置
            if current_file != final_output and os.path.exists(current_file):
                self.log_text.insert(tk.END, f"将最终处理结果从 {current_file} 复制到 {final_output}\n")
                if os.path.exists(final_output):
                    os.remove(final_output)
                shutil.copy2(current_file, final_output)
                
            if not os.path.exists(final_output):
                self.log_text.insert(tk.END, "警告：最终输出文件不存在，视频处理可能失败\n")
                return False
                
            self.log_text.insert(tk.END, f"处理完成，已保存到: {final_output}\n")
            return True

        except Exception as e:
            self.log_text.insert(tk.END, f"处理失败: {str(e)}\n")
            import traceback
            self.log_text.insert(tk.END, f"详细错误: {traceback.format_exc()}\n")
            return False
        finally:
            # 确保最终输出存在（即使发生错误也尝试保存）
            try:
                if 'current_file' in locals() and 'final_output' in locals():
                    if current_file != final_output and os.path.exists(current_file) and not os.path.exists(final_output):
                        self.log_text.insert(tk.END, "尝试在错误恢复中保存最终输出...\n")
                        shutil.copy2(current_file, final_output)
            except Exception as e:
                self.log_text.insert(tk.END, f"错误恢复保存失败: {str(e)}\n")
                
            # 清理临时文件
            if os.path.exists(temp_folder):
                try:
                    shutil.rmtree(temp_folder)
                except Exception as e:
                    self.log_text.insert(tk.END, f"清理临时文件失败: {str(e)}\n")

   

  
    def detect_hardware_acceleration(self):
        """检测可用的硬件加速选项"""
        try:
            # 检查可用的硬件加速
            hwaccel_cmd = ['ffmpeg', '-hide_banner', '-hwaccels']
            encoders_cmd = ['ffmpeg', '-hide_banner', '-encoders']
            
            hwaccel_result = subprocess.run(hwaccel_cmd, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)
            encoders_result = subprocess.run(encoders_cmd, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)
            
            hw_accel = None
            hw_device = None
            
            # 优先检查 Intel QSV
            if ('qsv' in hwaccel_result.stdout.lower() or 
                'h264_qsv' in encoders_result.stdout.lower()):
                # Intel 显卡支持 QSV
                hw_accel = 'qsv'
                hw_device = 'qsv'
                self.log_text.insert(tk.END, "使用 Intel Quick Sync 硬件加速\n")
            elif 'cuda' in hwaccel_result.stdout.lower():
                # NVIDIA GPU可用
                hw_accel = 'cuda'
                hw_device = 'cuda'
            elif 'd3d11va' in hwaccel_result.stdout.lower():
                # Windows DirectX 11 加速可用
                hw_accel = 'd3d11va'
                hw_device = 'd3d11va'
            elif 'dxva2' in hwaccel_result.stdout.lower():
                # Windows DirectX 9 加速可用
                hw_accel = 'dxva2'
                hw_device = 'dxva2'
                
            if hw_accel:
                self.log_text.insert(tk.END, f"检测到硬件加速: {hw_accel}\n")
            else:
                self.log_text.insert(tk.END, "未检测到支持的硬件加速，将使用CPU处理\n")
                
            return hw_accel, hw_device
            
        except Exception as e:
            self.log_text.insert(tk.END, f"硬件加速检测失败: {str(e)}\n")
            return None, None

    def trim_intro_outro(self, video_path, intro_duration, outro_start, output_path):
        """
        裁剪视频的片头和片尾
        :param video_path: 输入视频路径
        :param intro_duration: 片头时长(秒)
        :param outro_start: 片尾开始时间(秒)
        :param output_path: 输出视频路径
        :return: 是否成功
        """
        try:
            # 获取视频信息
            info = self.get_video_info(video_path)
            if not info:
                self.log_text.insert(tk.END, "获取视频信息失败\n")
                return False
                
            total_duration = info['duration']
            self.log_text.insert(tk.END, f"视频总时长: {total_duration:.2f}秒\n")
            
            # 检查参数有效性
            if intro_duration >= total_duration:
                self.log_text.insert(tk.END, "错误：片头时长大于或等于视频总时长\n")
                return False
                
            if outro_start >= total_duration:
                self.log_text.insert(tk.END, "警告：片尾开始时间大于视频总时长，将只裁剪片头\n")
                outro_start = total_duration
                
            if intro_duration >= outro_start:
                self.log_text.insert(tk.END, "错误：片头时长大于或等于片尾开始时间\n")
                return False
                
            # 计算实际需要保留的视频时长
            keep_duration = outro_start - intro_duration
            
            if keep_duration <= 0:
                self.log_text.insert(tk.END, "错误：裁剪后视频长度为0\n")
                return False
                
            self.log_text.insert(tk.END, f"将裁剪片头 {intro_duration:.2f}秒 和 {total_duration - outro_start:.2f}秒 片尾\n")
            self.log_text.insert(tk.END, f"保留视频时长: {keep_duration:.2f}秒\n")
            
            # 使用FFmpeg进行裁剪
            cmd = [
                'ffmpeg', '-y',
                '-ss', str(intro_duration),
                '-i', video_path,
                '-t', str(keep_duration),
                '-c', 'copy',  # 使用流复制，不重新编码
                '-avoid_negative_ts', '1',
                output_path
            ]
            
            subprocess.run(cmd, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
            self.log_text.insert(tk.END, "裁剪片头片尾完成\n")
            return True
            
        except Exception as e:
            self.log_text.insert(tk.END, f"裁剪片头片尾失败: {str(e)}\n")
            import traceback
            self.log_text.insert(tk.END, f"详细错误: {traceback.format_exc()}\n")
            return False

    def modify_template_md5(self, template_video, output_path):
        """
        随机修改模板视频的MD5值，通过添加随机元数据
        :param template_video: 原始模板视频路径
        :param output_path: 输出视频路径
        :return: 是否成功
        """
        try:
            # 首先检查源文件是否存在
            if not os.path.exists(template_video):
                self.log_text.insert(tk.END, f"错误: 模板视频文件不存在: {template_video}\n")
                return False
                
            # 检查源文件大小
            if os.path.getsize(template_video) == 0:
                self.log_text.insert(tk.END, f"错误: 模板视频文件大小为0: {template_video}\n")
                return False
                
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
                
            self.log_text.insert(tk.END, "开始随机修改模板视频MD5值...\n")
            
            # 生成多种随机元数据，确保每次生成的MD5都不同
            random_id = ''.join(random.choices('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=16))
            random_time = int(time.time() * 1000)  # 毫秒级时间戳
            random_uuid = str(random.randint(10000000, 99999999)) + str(random.randint(10000000, 99999999))
            random_value1 = f"md5-{random_id}-{random_time}"
            random_value2 = f"uuid-{random_uuid}"
            random_value3 = f"timestamp-{random_time}-{random.randint(1000, 9999)}"
            
            # 使用FFmpeg添加多个随机元数据
            cmd = [
                'ffmpeg', '-y',
                '-i', template_video,
                '-c', 'copy',
                '-metadata', f"md5_random={random_value1}",
                '-metadata', f"uuid_random={random_value2}",
                '-metadata', f"timestamp={random_value3}",
                '-metadata', f"processor=VideoProcessor-{random_id}",
                '-metadata', f"processed_time={time.strftime('%Y-%m-%d %H:%M:%S')}",
                output_path
            ]
            
            self.log_text.insert(tk.END, f"添加随机元数据: md5_random={random_value1}, uuid_random={random_value2}\n")
            self.log_text.insert(tk.END, f"源文件: {template_video} ({os.path.getsize(template_video)} 字节)\n")
            self.log_text.insert(tk.END, f"目标文件: {output_path}\n")
            
            result = subprocess.run(cmd, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)
            
            # 检查命令是否成功执行
            if result.returncode != 0:
                self.log_text.insert(tk.END, f"MD5修改命令执行失败: {result.stderr}\n")
                return False
            
            # 验证输出文件
            if not os.path.exists(output_path):
                self.log_text.insert(tk.END, "MD5修改失败: 输出文件不存在\n")
                return False
                
            if os.path.getsize(output_path) == 0:
                self.log_text.insert(tk.END, "MD5修改失败: 输出文件大小为0\n")
                return False
                
            # 验证元数据是否成功添加
            verify_cmd = [
                'ffprobe', '-v', 'error',
                '-show_entries', 'format_tags=md5_random,uuid_random',
                '-of', 'json',
                output_path
            ]
            
            verify_result = subprocess.run(verify_cmd, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)
            if verify_result.returncode == 0:
                try:
                    metadata = json.loads(verify_result.stdout)
                    if 'format' in metadata and 'tags' in metadata['format']:
                        self.log_text.insert(tk.END, "元数据验证成功\n")
                    else:
                        self.log_text.insert(tk.END, "警告: 未找到元数据标签\n")
                except:
                    self.log_text.insert(tk.END, "警告: 无法解析元数据验证结果\n")
            
            # 计算修改后文件的MD5值
            import hashlib
            with open(output_path, 'rb') as f:
                file_hash = hashlib.md5()
                while chunk := f.read(8192):
                    file_hash.update(chunk)
            new_md5 = file_hash.hexdigest()
            
            # 计算原始文件的MD5值
            with open(template_video, 'rb') as f:
                file_hash = hashlib.md5()
                while chunk := f.read(8192):
                    file_hash.update(chunk)
            original_md5 = file_hash.hexdigest()
            
            self.log_text.insert(tk.END, f"原始文件MD5值: {original_md5}\n")
            self.log_text.insert(tk.END, f"修改后文件MD5值: {new_md5}\n")
            self.log_text.insert(tk.END, f"MD5修改成功，文件大小: {os.path.getsize(output_path)} 字节\n")
            return True
            
        except Exception as e:
            self.log_text.insert(tk.END, f"修改模板视频MD5值失败: {str(e)}\n")
            import traceback
            self.log_text.insert(tk.END, f"详细错误: {traceback.format_exc()}\n")
            return False

    def combine_with_template(self, processed_video, template_folder, output_path, output_fps=120):
        """
        将处理后的视频与模板视频合并，根据视频方向选择对应的模板
        :param processed_video: 处理后的视频路径
        :param template_folder: 模板视频文件夹（可能包含横屏和竖屏子文件夹）
        :param output_path: 输出视频路径
        :return: 是否成功
        """
        try:
            # 首先检查模板文件夹是否存在
            if not os.path.exists(template_folder):
                self.log_text.insert(tk.END, f"错误: 模板文件夹 '{template_folder}' 不存在\n")
                return False
                
            self.log_text.insert(tk.END, f"开始处理模板合并，模板文件夹: {template_folder}\n")
            
            # 检查处理后的视频文件是否存在
            if not os.path.exists(processed_video):
                self.log_text.insert(tk.END, f"错误: 处理后的视频文件不存在: {processed_video}\n")
                return False
                
            # 检查文件大小
            file_size = os.path.getsize(processed_video)
            if file_size == 0:
                self.log_text.insert(tk.END, f"错误: 处理后的视频文件大小为0: {processed_video}\n")
                return False
                
            self.log_text.insert(tk.END, f"处理后的视频文件大小: {file_size} 字节\n")
                
            # 获取处理后视频信息
            try:
                command = [
                    'ffprobe', '-v', 'error',
                    '-select_streams', 'v:0',
                    '-show_entries', 'stream=width,height,duration,r_frame_rate',
                    '-of', 'json',
                    processed_video
                ]
                result = subprocess.run(command, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)
                
                # 检查ffprobe是否成功
                if result.returncode != 0:
                    self.log_text.insert(tk.END, f"ffprobe命令失败: {result.stderr}\n")
                    return False
                    
                self.log_text.insert(tk.END, f"ffprobe输出: {result.stdout}\n")
                
                data = json.loads(result.stdout)
                
                # 检查JSON数据是否包含streams
                if 'streams' not in data or not data['streams']:
                    self.log_text.insert(tk.END, f"获取视频信息失败: 没有找到视频流\n")
                    return False
                    
                stream = data['streams'][0]
                
                processed_info = {
                    'width': stream.get('width', 0),
                    'height': stream.get('height', 0),
                    'fps': eval(stream['r_frame_rate']) if 'r_frame_rate' in stream else 0,
                    'duration': float(stream.get('duration', 0))
                }
                
                self.log_text.insert(tk.END, f"成功获取处理后视频信息: {processed_info}\n")
                
                if processed_info['width'] == 0 or processed_info['height'] == 0:
                    self.log_text.insert(tk.END, f"错误: 无效的视频分辨率: {processed_info['width']}x{processed_info['height']}\n")
                    return False
            except Exception as e:
                self.log_text.insert(tk.END, f"获取处理后视频信息失败: {str(e)}\n")
                import traceback
                self.log_text.insert(tk.END, f"详细错误: {traceback.format_exc()}\n")
                return False
            
            # 检测视频方向
            is_vertical = processed_info['height'] > processed_info['width']
            orientation = "竖屏" if is_vertical else "横屏"
            self.log_text.insert(tk.END, f"检测到视频A是{orientation}视频 ({processed_info['width']}x{processed_info['height']})\n")
            
            # 列出模板文件夹中的所有内容
            self.log_text.insert(tk.END, f"模板文件夹内容:\n")
            try:
                for item in os.listdir(template_folder):
                    item_path = os.path.join(template_folder, item)
                    if os.path.isdir(item_path):
                        self.log_text.insert(tk.END, f"  - 文件夹: {item}\n")
                    else:
                        self.log_text.insert(tk.END, f"  - 文件: {item}\n")
            except Exception as e:
                self.log_text.insert(tk.END, f"列出文件夹内容时出错: {str(e)}\n")
            
            # 确定使用的模板文件夹
            vertical_folder = os.path.join(template_folder, "竖屏")
            horizontal_folder = os.path.join(template_folder, "横屏")
            
            # 直接搜索整个文件夹结构，找出所有视频文件
            all_videos = {}  # 用于存储每个文件夹中的视频文件 {folder_path: [video_files]}
            
            # 规范化模板文件夹路径
            template_folder = os.path.normpath(template_folder)
            self.log_text.insert(tk.END, f"规范化后的模板文件夹路径: {template_folder}\n")
            
            try:
                for root, dirs, files in os.walk(template_folder):
                    # 规范化root路径
                    root = os.path.normpath(root)
                    
                    # 过滤并处理文件名
                    valid_videos = []
                    for f in files:
                        try:
                            # 检查文件扩展名
                            if f.lower().endswith(('.mp4', '.avi', '.mov')):
                                # 检查完整路径是否有效
                                full_path = os.path.join(root, f)
                                full_path = os.path.normpath(full_path)
                                if os.path.exists(full_path) and os.path.isfile(full_path):
                                    valid_videos.append(f)
                                    self.log_text.insert(tk.END, f"  有效视频文件: {f} (路径: {full_path})\n")
                                else:
                                    self.log_text.insert(tk.END, f"  文件路径无效: {full_path}\n")
                        except Exception as e:
                            self.log_text.insert(tk.END, f"  处理文件 {f} 时出错: {str(e)}\n")
                    
                    if valid_videos:
                        all_videos[root] = valid_videos
                        self.log_text.insert(tk.END, f"在 '{root}' 中找到 {len(valid_videos)} 个有效视频文件\n")
            except Exception as e:
                self.log_text.insert(tk.END, f"遍历文件夹时出错: {str(e)}\n")
                import traceback
                self.log_text.insert(tk.END, f"详细错误: {traceback.format_exc()}\n")
            
            if not all_videos:
                self.log_text.insert(tk.END, f"错误: 在模板文件夹及其子文件夹中没有找到任何视频文件\n")
                return False
            
            # 根据视频方向选择合适的模板文件夹
            selected_template_folder = None
            
            # 规范化路径，以便进行准确比较
            vertical_folder = os.path.normpath(vertical_folder)
            horizontal_folder = os.path.normpath(horizontal_folder)
            
            # 输出调试信息
            self.log_text.insert(tk.END, f"竖屏文件夹路径: {vertical_folder}\n")
            self.log_text.insert(tk.END, f"横屏文件夹路径: {horizontal_folder}\n")
            self.log_text.insert(tk.END, f"可用的模板文件夹: {list(all_videos.keys())}\n")
            
            # 检查是否有对应方向的专用文件夹
            if is_vertical:
                # 竖屏视频，查找竖屏模板
                for folder in all_videos.keys():
                    if os.path.normpath(folder) == vertical_folder:
                        selected_template_folder = folder
                        self.log_text.insert(tk.END, f"使用竖屏专用文件夹: {folder}\n")
                        break
            else:
                # 横屏视频，查找横屏模板
                for folder in all_videos.keys():
                    if os.path.normpath(folder) == horizontal_folder:
                        selected_template_folder = folder
                        self.log_text.insert(tk.END, f"使用横屏专用文件夹: {folder}\n")
                        break
            
            # 如果没有找到对应方向的专用文件夹，使用备选方案
            if not selected_template_folder:
                # 如果没有找到专用文件夹，使用包含视频的任意文件夹
                for folder, videos in all_videos.items():
                    if folder != template_folder:  # 优先使用子文件夹
                        selected_template_folder = folder
                        self.log_text.insert(tk.END, f"未找到{orientation}专用文件夹，使用子文件夹: {folder}\n")
                        break
                
                # 如果没有找到子文件夹，使用主文件夹
                if not selected_template_folder and template_folder in all_videos:
                    selected_template_folder = template_folder
                    self.log_text.insert(tk.END, f"使用主文件夹作为模板文件夹\n")
            
            # 最后检查是否找到了有效的模板文件夹
            if not selected_template_folder or selected_template_folder not in all_videos:
                self.log_text.insert(tk.END, f"错误: 无法确定有效的模板文件夹\n")
                return False
            
            # 获取选定文件夹中的视频文件
            template_files = all_videos[selected_template_folder]
            self.log_text.insert(tk.END, f"从 '{selected_template_folder}' 中选择模板视频，共有 {len(template_files)} 个视频文件\n")
            
            # 随机选择一个模板视频
            try:
                # 确保模板文件列表非空
                if not template_files:
                    self.log_text.insert(tk.END, f"错误: 模板文件列表为空\n")
                    return False
                
                # 随机选择并处理文件名
                template_file = random.choice(template_files)
                template_video = os.path.join(selected_template_folder, template_file)
                template_video = os.path.normpath(template_video)
                self.log_text.insert(tk.END, f"随机选择{orientation}模板视频: {template_file}\n")
                self.log_text.insert(tk.END, f"完整路径: {template_video}\n")
                
                # 检查文件是否存在
                if not os.path.exists(template_video):
                    self.log_text.insert(tk.END, f"错误: 选择的模板视频不存在\n")
                    
                    # 尝试列出目录内容
                    try:
                        parent_dir = os.path.dirname(template_video)
                        self.log_text.insert(tk.END, f"父目录: {parent_dir}\n")
                        dir_content = os.listdir(parent_dir)
                        self.log_text.insert(tk.END, f"目录内容: {dir_content}\n")
                    except Exception as e:
                        self.log_text.insert(tk.END, f"无法列出目录内容: {str(e)}\n")
                        import traceback
                        self.log_text.insert(tk.END, f"详细错误: {traceback.format_exc()}\n")
                    
                    return False
                
                # 确认文件大小
                file_size = os.path.getsize(template_video)
                self.log_text.insert(tk.END, f"文件大小: {file_size} 字节\n")
                
                if file_size == 0:
                    self.log_text.insert(tk.END, f"错误: 文件大小为0\n")
                    return False
                
                # 获取模板视频信息
                try:
                    command = [
                        'ffprobe', '-v', 'error',
                        '-select_streams', 'v:0',
                        '-show_entries', 'stream=width,height,duration,r_frame_rate',
                        '-of', 'json',
                        template_video
                    ]
                    result = subprocess.run(command, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)
                    
                    # 检查ffprobe是否成功
                    if result.returncode != 0:
                        self.log_text.insert(tk.END, f"ffprobe命令失败: {result.stderr}\n")
                        return False
                        
                    self.log_text.insert(tk.END, f"模板视频ffprobe输出: {result.stdout}\n")
                    
                    data = json.loads(result.stdout)
                    
                    # 检查JSON数据是否包含streams
                    if 'streams' not in data or not data['streams']:
                        self.log_text.insert(tk.END, f"获取模板视频信息失败: 没有找到视频流\n")
                        return False
                        
                    stream = data['streams'][0]
                    
                    template_info = {
                        'width': stream.get('width', 0),
                        'height': stream.get('height', 0),
                        'fps': eval(stream['r_frame_rate']) if 'r_frame_rate' in stream else 0,
                        'duration': float(stream.get('duration', 0))
                    }
                    
                    self.log_text.insert(tk.END, f"成功获取模板视频信息: {template_info}\n")
                    
                    if template_info['width'] == 0 or template_info['height'] == 0:
                        self.log_text.insert(tk.END, f"错误: 无效的模板视频分辨率: {template_info['width']}x{template_info['height']}\n")
                        return False
                except Exception as e:
                    self.log_text.insert(tk.END, f"获取模板视频信息失败: {str(e)}\n")
                    import traceback
                    self.log_text.insert(tk.END, f"详细错误: {traceback.format_exc()}\n")
                    return False
            except Exception as e:
                self.log_text.insert(tk.END, f"选择模板视频时出错: {str(e)}\n")
                import traceback
                self.log_text.insert(tk.END, f"详细错误: {traceback.format_exc()}\n")
                return False
                
            # 创建临时文件夹
            temp_dir = os.path.dirname(output_path)
            
            # 修改模板视频MD5值
            timestamp = int(time.time())
            temp_template = os.path.join(temp_dir, f"template_md5_modified_{timestamp}.mp4")
            temp_template = os.path.normpath(temp_template)
            
            # 记录临时文件以便后续清理
            self.temp_files = getattr(self, 'temp_files', [])
            self.temp_files.append(temp_template)
            
            self.log_text.insert(tk.END, f"开始修改模板视频MD5值: {template_video} -> {temp_template}\n")
            if not self.modify_template_md5(template_video, temp_template):
                self.log_text.insert(tk.END, "修改模板视频MD5值失败\n")
                return False
            else:
                self.log_text.insert(tk.END, f"成功修改模板视频MD5值，使用修改后的模板: {temp_template}\n")
                template_video = temp_template
            
            # 使用最简单的方法：先提取SEI头部，然后直接使用demuxer合并
            self.log_text.insert(tk.END, "开始提取模板视频SEI信息并合并...\n")
            
            # 使用时间戳创建唯一的临时文件名
            timestamp = int(time.time())
            
            # 1. 提取模板视频的前几帧，确保包含SEI信息
            sei_header = os.path.join(temp_dir, f"sei_header_{timestamp}.ts")
            sei_header = os.path.normpath(sei_header)
            
            self.log_text.insert(tk.END, f"准备提取SEI头部到: {sei_header}\n")
            
            try:
                # 使用两阶段方法确保精确120fps
                self.log_text.insert(tk.END, "使用两阶段方法确保精确120fps...\n")
                
                # 第一阶段：提取原始帧
                raw_h264 = os.path.join(temp_dir, f"raw_frames_{timestamp}.h264")
                raw_h264 = os.path.normpath(raw_h264)
                
                # 提取原始H.264帧
                extract_cmd = [
                    'ffmpeg', '-y',
                    '-ss', '0',               # 从视频开头
                    '-i', template_video,     # 模板视频
                    '-vframes', '2',          # 严格限制为2帧
                    '-c:v', 'copy',           # 复制模式，不重编码以保留SEI信息
                    '-an',                    # 不要音频
                    '-bsf:v', 'h264_mp4toannexb',  # 比特流过滤器
                    '-f', 'h264',             # 输出为原始H.264
                    raw_h264
                ]
                
                # 执行提取
                self.log_text.insert(tk.END, "提取原始H.264帧...\n")
                subprocess.run(extract_cmd, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
                self.log_text.insert(tk.END, f"原始帧提取完成: {os.path.getsize(raw_h264)} 字节\n")
                
                # 第二阶段：将原始帧封装为指定帧率的TS
                self.log_text.insert(tk.END, f"封装为精确{output_fps}fps的TS格式...\n")
                
                sei_cmd = [
                    'ffmpeg', '-y',
                    '-r', str(output_fps),              # 输入帧率设置为指定帧率
                    '-i', raw_h264,           # 使用原始H.264帧
                    '-c:v', 'copy',           # 复制模式，不重编码
                    '-r', str(output_fps),              # 输出帧率设置为指定帧率
                    '-video_track_timescale', str(output_fps),  # 时基设置为指定帧率
                    '-fflags', '+genpts',     # 重新生成PTS
                    '-muxrate', '10000k',     # 高复用率
                    '-f', 'mpegts',           # 输出为TS格式
                    sei_header
                ]
                
                # 记录临时文件用于后续清理
                self.raw_h264 = raw_h264
                
                # 不需要记录中间临时文件
                
                # 执行转换
                self.log_text.insert(tk.END, "转换为120fps TS格式...\n")
                subprocess.run(sei_cmd, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
                
                # 记录已在前面完成
                self.log_text.insert(tk.END, f"提取2帧并转为120fps完成: {os.path.getsize(sei_header)} 字节\n")
                
                # 这一步已经执行过了，不需要重复执行
                
                # 检查文件是否创建成功
                if not os.path.exists(sei_header) or os.path.getsize(sei_header) == 0:
                    self.log_text.insert(tk.END, f"SEI头部文件未创建或为空\n")
                    return False
                    
                self.log_text.insert(tk.END, f"SEI头部提取成功，文件大小: {os.path.getsize(sei_header)} 字节\n")
            except Exception as e:
                self.log_text.insert(tk.END, f"SEI提取过程出错: {str(e)}\n")
                return False
            
            # 2. 将处理后的视频转换为TS格式
            processed_ts = os.path.join(temp_dir, f"processed_{timestamp}.ts")
            processed_ts = os.path.normpath(processed_ts)
            
            self.log_text.insert(tk.END, f"准备转换视频A为TS格式: {processed_ts}\n")
            
            try:
                convert_cmd = [
                    'ffmpeg', '-y',
                    '-i', processed_video,
                    '-c', 'copy',  # 不重新编码
                    '-bsf:v', 'h264_mp4toannexb',  # 转换为TS兼容格式
                    '-map', '0:v',  # 映射视频流
                    '-map', '0:a?',  # 映射音频流(如果存在)
                    '-f', 'mpegts',  # 输出为TS格式
                    processed_ts
                ]
                
                # 执行命令并捕获输出
                result = subprocess.run(convert_cmd, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)
                if result.returncode != 0:
                    self.log_text.insert(tk.END, f"视频A转换失败，错误信息: {result.stderr}\n")
                    return False
                
                # 检查文件是否创建成功
                if not os.path.exists(processed_ts) or os.path.getsize(processed_ts) == 0:
                    self.log_text.insert(tk.END, f"转换后的视频A文件未创建或为空\n")
                    return False
                    
                self.log_text.insert(tk.END, f"视频A转换为TS格式成功，文件大小: {os.path.getsize(processed_ts)} 字节\n")
            except Exception as e:
                self.log_text.insert(tk.END, f"视频A转换过程出错: {str(e)}\n")
                return False
            
            # 3. 使用concat demuxer合并视频和音频
            self.log_text.insert(tk.END, "使用新方法合并视频和音频...\n")
            
            # 创建临时文件
            temp_mp4 = os.path.join(temp_dir, f"temp_final_{timestamp}.mp4")
            temp_mp4 = os.path.normpath(temp_mp4)
            
            try:
                # 直接合并SEI头部和处理后的视频
                self.log_text.insert(tk.END, "合并SEI头部和处理后视频...\n")
                
                # 使用两步法合并视频和音频
                # 步骤1：先连接两个视频文件
                concat_file = os.path.join(temp_dir, f"concat_{timestamp}.txt")
                concat_file = os.path.normpath(concat_file)
                
                with open(concat_file, 'w', encoding='utf-8') as f:
                    sei_path = sei_header.replace('\\', '/')
                    processed_path = processed_ts.replace('\\', '/')
                    f.write(f"file '{sei_path}'\n")
                    f.write(f"file '{processed_path}'\n")
                
                self.log_text.insert(tk.END, "使用concat demuxer连接视频...\n")
                
                # 连接视频部分
                video_temp = os.path.join(temp_dir, f"video_temp_{timestamp}.mp4")
                video_temp = os.path.normpath(video_temp)
                
                concat_cmd = [
                    'ffmpeg', '-y',
                    '-f', 'concat',
                    '-safe', '0',
                    '-i', concat_file,
                    '-c', 'copy',          # 复制模式
                    '-r', str(output_fps),           # 强制帧率为指定帧率
                    '-video_track_timescale', str(output_fps),  # 使用指定帧率作为时基
                    '-an',                 # 不包含音频
                    video_temp
                ]
                
                # 执行视频连接
                subprocess.run(concat_cmd, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
                self.log_text.insert(tk.END, f"视频连接完成: {os.path.getsize(video_temp)} 字节\n")
                
                # 步骤2：将视频与原始视频的音频合并
                self.log_text.insert(tk.END, "合并视频和原始音频...\n")
                
                mux_cmd = [
                    'ffmpeg', '-y',
                    '-i', video_temp,      # 连接后的视频
                    '-i', processed_video, # 原始处理后视频(用于获取音频)
                    '-c:v', 'copy',        # 复制视频流
                    '-c:a', 'copy',        # 直接复制原始音频
                    '-map', '0:v',         # 使用连接后的视频
                    '-map', '1:a',         # 使用原始视频的音频
                    '-shortest',           # 使用最短流的长度
                    '-r', str(output_fps),           # 强制输出帧率为指定帧率
                    '-video_track_timescale', str(output_fps),  # 使用指定帧率作为时基
                    '-fps_mode', 'cfr',    # 使用恒定帧率
                    '-movflags', '+faststart',  # 优化网络播放
                    temp_mp4
                ]
                
                # 记录临时文件
                self.video_temp = video_temp
                self.concat_file = concat_file
                self.temp_mp4 = temp_mp4
            except Exception as e:
                self.log_text.insert(tk.END, f"创建合并列表文件失败: {str(e)}\n")
                return False
            
            try:
                # 执行视频连接命令
                result = subprocess.run(concat_cmd, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)
                
                if result.returncode != 0:
                    self.log_text.insert(tk.END, f"视频连接失败，错误信息: {result.stderr}\n")
                    return False
                
                # 执行音视频合并命令
                self.log_text.insert(tk.END, "执行音视频合并...\n")
                result = subprocess.run(mux_cmd, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)
                
                if result.returncode != 0:
                    self.log_text.insert(tk.END, f"音视频合并失败，错误信息: {result.stderr}\n")
                    return False
                
                # 验证中间文件
                if not os.path.exists(temp_mp4) or os.path.getsize(temp_mp4) == 0:
                    self.log_text.insert(tk.END, f"中间文件未创建或为空\n")
                    return False
                
                # 最后一步：将中间文件复制到最终输出，并确保帧率为指定帧率
                final_cmd = [
                    'ffmpeg', '-y',
                    '-i', temp_mp4,
                    '-c:v', 'copy',       # 复制视频流
                    '-c:a', 'copy',       # 复制音频流
                    '-r', str(output_fps),          # 强制帧率为指定帧率
                    '-video_track_timescale', str(output_fps),  # 使用指定帧率作为时基
                    '-fps_mode', 'cfr',   # 使用恒定帧率
                    '-movflags', '+faststart',  # 优化网络播放
                    output_path
                ]
                
                self.log_text.insert(tk.END, "执行最终输出...\n")
                result = subprocess.run(final_cmd, capture_output=True, text=True, **DEFAULT_SUBPROCESS_PARAMS)
                
                if result.returncode != 0:
                    self.log_text.insert(tk.END, f"最终输出失败，错误信息: {result.stderr}\n")
                    return False
                
                # 验证最终输出文件
                if not os.path.exists(output_path) or os.path.getsize(output_path) == 0:
                    self.log_text.insert(tk.END, f"最终输出文件未创建或为空\n")
                    return False
                
                # 验证帧率
                try:
                    probe_cmd = [
                        'ffprobe', 
                        '-v', 'error',
                        '-select_streams', 'v:0',
                        '-show_entries', 'stream=r_frame_rate',
                        '-of', 'default=noprint_wrappers=1:nokey=1',
                        output_path
                    ]
                    probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
                    framerate = probe_result.stdout.strip()
                    self.log_text.insert(tk.END, f"最终视频帧率: {framerate} (目标: {output_fps}fps)\n")
                except Exception as e:
                    self.log_text.insert(tk.END, f"帧率验证失败: {str(e)}\n")
                
                self.log_text.insert(tk.END, f"视频合并完成，输出文件大小: {os.path.getsize(output_path)} 字节\n")
                self.log_text.insert(tk.END, "模板片段与视频A合并成功，保留了原始SEI信息，包含音频\n")
            except Exception as e:
                self.log_text.insert(tk.END, f"执行最终合并命令时出错: {str(e)}\n")
                import traceback
                self.log_text.insert(tk.END, f"详细错误: {traceback.format_exc()}\n")
                return False
            
            # 清理临时文件
            try:
                # 获取所有需要清理的临时文件
                temp_files = [raw_h264, sei_header, processed_ts, video_temp, concat_file, temp_mp4]
                
                # 添加MD5修改的临时文件
                if hasattr(self, 'temp_files') and self.temp_files:
                    temp_files.extend(self.temp_files)
                
                # 清理所有临时文件
                for temp_file in temp_files:
                    if temp_file and os.path.exists(temp_file):
                        try:
                            os.remove(temp_file)
                            self.log_text.insert(tk.END, f"清理临时文件: {os.path.basename(temp_file)}\n")
                        except Exception as e:
                            self.log_text.insert(tk.END, f"清理临时文件 {os.path.basename(temp_file)} 失败: {str(e)}\n")
                
                # 清空临时文件列表
                if hasattr(self, 'temp_files'):
                    self.temp_files = []
            except Exception as e:
                self.log_text.insert(tk.END, f"清理临时文件失败: {str(e)}\n")
            
            return True
            
        except Exception as e:
            self.log_text.insert(tk.END, f"合并视频失败: {str(e)}\n")
            import traceback
            self.log_text.insert(tk.END, f"详细错误:\n{traceback.format_exc()}\n")
            return False

class VideoProcessorUI:
    def __init__(self, root):
        self.root = root
        self.root.title("视频处理工具")
        self.root.geometry("1024x768")  # 增加窗口初始大小
        
        # 配置文件路径
        self.config_file = get_config_path()

        # 配置根窗口的网格布局
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)

        # 初始化变量
        self.frame_count = tk.IntVar(value=100)
        self.video_a_folders = []  # 修改为列表存储多个文件夹
        self.video_b_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.acceleration = tk.StringVar(value="cpu")
        self.triple_screen = tk.BooleanVar(value=False)
        self.progress_var = tk.DoubleVar()
        # 添加视频分割相关变量
        self.enable_split = tk.BooleanVar(value=False)
        self.split_min = tk.IntVar(value=30)
        self.split_max = tk.IntVar(value=60)
        # 添加删除原视频选项变量
        self.delete_original = tk.BooleanVar(value=False)
        # 添加片头片尾处理相关变量
        self.enable_trim = tk.BooleanVar(value=False)
        self.intro_duration = tk.DoubleVar(value=2.67)  # 默认2分40秒 = 160秒 = 2.67分钟
        self.outro_start = tk.DoubleVar(value=43.67)    # 默认43分40秒 = 2620秒 = 43.67分钟
        # 处理选项变量
        self.enable_random_frames = tk.BooleanVar(value=True)
        self.enable_ab_layer = tk.BooleanVar(value=True)
        self.enable_triple = tk.BooleanVar(value=False)
        self.enable_track_reuse = tk.BooleanVar(value=False)
        self.enable_audio = tk.BooleanVar(value=True)
        self.enable_metadata = tk.BooleanVar(value=True)
        self.enable_sketch = tk.BooleanVar(value=False)
        self.enable_use_a_clips = tk.BooleanVar(value=True)  # 新增：是否在轨道融合中使用A视频片段
        self.use_original_b_only = tk.BooleanVar(value=False)  # 新增：是否只使用原始视频B而不提取片段
        # 新增帧率和交错方式输入
        self.video_a_fps = tk.IntVar(value=60)
        self.video_b_fps = tk.IntVar(value=60)
        self.output_fps = tk.IntVar(value=120)
        self.interleave_mode = tk.StringVar(value="baba")  # "baba"=b1,a1,b2,a2，"abab"=a1,b1,a2,b2
        # 新增模板视频相关变量
        self.template_folder = tk.StringVar()
        self.enable_template = tk.BooleanVar(value=False)
        # 新增音频文件夹和音量控制
        self.audio_folder = tk.StringVar()
        self.bg_volume = tk.DoubleVar(value=0.06)  # 背景音乐音量，0.06约为-24dB
        # 新增图片边框相关变量
        self.image_folder = tk.StringVar()
        self.enable_image_border = tk.BooleanVar(value=False)  # 是否启用图片边框功能
        
        # 新增叠加视频文件夹和叠加控制变量
        self.processed_b_folder = tk.StringVar()
        self.enable_overlay = tk.BooleanVar(value=False)  # 是否启用视频叠加
        self.overlay_opacity = tk.DoubleVar(value=0.5)  # 叠加透明度，默认0.5
        
        # 新增三连屏视频B文件夹变量
        self.triple_b_folder = tk.StringVar()
        
        # 加载之前保存的设置
        self.load_settings()
        
        self.create_ui()
        # 创建日志处理器实例
        self.progress_bar = None
        self.processor = VideoProcessor(self.log_text)
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 绑定设置变更事件
        self.bind_settings_change()

    def create_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.grid(row=0, column=0, sticky="nsew", padx=15, pady=15)  # 增加边距
        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(3, weight=1)

        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择")
        file_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)  # 增加内边距
        file_frame.grid_columnconfigure(1, weight=1)

        # 视频A文件夹选择
        ttk.Label(file_frame, text="视频A文件夹:", width=12).grid(row=0, column=0, padx=8, pady=8, sticky="w")
        
        # 添加列表框显示选择的文件夹
        self.video_a_listbox = tk.Listbox(file_frame, height=4)
        self.video_a_listbox.grid(row=0, column=1, padx=8, pady=8, sticky="ew")
        
        # 填充之前保存的视频A文件夹列表
        for folder in self.video_a_folders:
            self.video_a_listbox.insert(tk.END, folder)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(file_frame, orient="vertical", command=self.video_a_listbox.yview)
        scrollbar.grid(row=0, column=2, sticky="ns")
        self.video_a_listbox.configure(yscrollcommand=scrollbar.set)
        
        # 添加按钮框架
        button_frame = ttk.Frame(file_frame)
        button_frame.grid(row=0, column=3, padx=8, pady=8)
        
        ttk.Button(button_frame, text="添加文件夹", width=12, command=self.select_video_a_folder).grid(row=0, column=0, padx=4, pady=2)
        ttk.Button(button_frame, text="清除选择", width=12, command=self.clear_video_a_folders).grid(row=1, column=0, padx=4, pady=2)

        # 视频B文件夹选择
        ttk.Label(file_frame, text="视频B文件夹:", width=12).grid(row=1, column=0, padx=8, pady=8, sticky="w")
        ttk.Entry(file_frame, textvariable=self.video_b_folder).grid(row=1, column=1, padx=8, pady=8, sticky="ew")
        ttk.Button(file_frame, text="选择", width=10, command=self.select_video_b_folder).grid(row=1, column=3, padx=8, pady=8)

        # 输出文件夹选择
        ttk.Label(file_frame, text="输出文件夹:", width=12).grid(row=2, column=0, padx=8, pady=8, sticky="w")
        ttk.Entry(file_frame, textvariable=self.output_folder).grid(row=2, column=1, padx=8, pady=8, sticky="ew")
        ttk.Button(file_frame, text="选择", width=10, command=self.select_output_folder).grid(row=2, column=3, padx=8, pady=8)
        
        # 添加模板视频文件夹选择
        ttk.Label(file_frame, text="模板视频文件夹:", width=12).grid(row=3, column=0, padx=8, pady=8, sticky="w")
        ttk.Entry(file_frame, textvariable=self.template_folder).grid(row=3, column=1, padx=8, pady=8, sticky="ew")
        ttk.Button(file_frame, text="选择", width=10, command=self.select_template_folder).grid(row=3, column=3, padx=8, pady=8)
        
        # 添加音频文件夹选择
        ttk.Label(file_frame, text="音频文件夹:", width=12).grid(row=4, column=0, padx=8, pady=8, sticky="w")
        ttk.Entry(file_frame, textvariable=self.audio_folder).grid(row=4, column=1, padx=8, pady=8, sticky="ew")
        ttk.Button(file_frame, text="选择", width=10, command=self.select_audio_folder).grid(row=4, column=3, padx=8, pady=8)
        
        # 添加图片文件夹选择
        ttk.Label(file_frame, text="图片文件夹:", width=12).grid(row=5, column=0, padx=8, pady=8, sticky="w")
        ttk.Entry(file_frame, textvariable=self.image_folder).grid(row=5, column=1, padx=8, pady=8, sticky="ew")
        ttk.Button(file_frame, text="选择", width=10, command=self.select_image_folder).grid(row=5, column=3, padx=8, pady=8)
        
        # 添加叠加视频文件夹选择
        ttk.Label(file_frame, text="叠加视频:", width=12).grid(row=6, column=0, padx=8, pady=8, sticky="w")
        ttk.Entry(file_frame, textvariable=self.processed_b_folder).grid(row=6, column=1, padx=8, pady=8, sticky="ew")
        ttk.Button(file_frame, text="选择", width=10, command=self.select_processed_b_folder).grid(row=6, column=3, padx=8, pady=8)
        
        # 添加三连屏视频B文件夹选择
        ttk.Label(file_frame, text="三连屏视频B:", width=12).grid(row=7, column=0, padx=8, pady=8, sticky="w")
        ttk.Entry(file_frame, textvariable=self.triple_b_folder).grid(row=7, column=1, padx=8, pady=8, sticky="ew")
        ttk.Button(file_frame, text="选择", width=10, command=self.select_triple_b_folder).grid(row=7, column=3, padx=8, pady=8)
        
        # 添加模板文件夹说明
        template_info = tk.Label(file_frame, text="可在模板文件夹下创建'竖屏'和'横屏'子文件夹，程序将根据视频方向自动选择", 
                                fg="blue", font=("Arial", 9))
        template_info.grid(row=8, column=0, columnspan=4, padx=8, pady=2, sticky="w")

        # 添加片头片尾处理选项
        trim_frame = ttk.LabelFrame(main_frame, text="片头片尾处理")
        trim_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        trim_frame.grid_columnconfigure(1, weight=1)

        ttk.Checkbutton(trim_frame, text="启用片头片尾裁剪", width=15,
                        variable=self.enable_trim).grid(row=0, column=0, padx=8, pady=8)
        
        # 片头时长设置
        ttk.Label(trim_frame, text="片头时长(分钟):", width=15).grid(row=0, column=1, padx=8, pady=8)
        ttk.Entry(trim_frame, textvariable=self.intro_duration, width=8).grid(row=0, column=2, padx=8, pady=8)
        
        # 片尾开始时间设置
        ttk.Label(trim_frame, text="片尾开始(分钟):", width=15).grid(row=0, column=3, padx=8, pady=8)
        ttk.Entry(trim_frame, textvariable=self.outro_start, width=8).grid(row=0, column=4, padx=8, pady=8)

        # 视频分割选项
        split_frame = ttk.LabelFrame(main_frame, text="视频分割设置")
        split_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=10)
        split_frame.grid_columnconfigure(1, weight=1)

        ttk.Checkbutton(split_frame, text="启用视频分割", width=15,
                        variable=self.enable_split).grid(row=0, column=0, padx=8, pady=8)
        ttk.Label(split_frame, text="分割时长范围(秒):", width=15).grid(row=0, column=1, padx=8, pady=8)
        ttk.Entry(split_frame, textvariable=self.split_min, width=8).grid(row=0, column=2, padx=8, pady=8)
        ttk.Label(split_frame, text="到").grid(row=0, column=3, padx=8, pady=8)
        ttk.Entry(split_frame, textvariable=self.split_max, width=8).grid(row=0, column=4, padx=8, pady=8)

        # 音频控制区域
        audio_frame = ttk.LabelFrame(main_frame, text="音频控制")
        audio_frame.grid(row=3, column=0, sticky="ew", padx=10, pady=10)
        audio_frame.grid_columnconfigure(1, weight=1)
        
        # 背景音量控制滑块
        ttk.Label(audio_frame, text="背景音量:").grid(row=0, column=0, padx=8, pady=8, sticky="w")
        
        # 创建音量控制滑块
        volume_scale = ttk.Scale(audio_frame, 
                                 from_=0.01, 
                                 to=0.3,
                                 variable=self.bg_volume,
                                 orient=tk.HORIZONTAL,
                                 length=200)
        volume_scale.grid(row=0, column=1, padx=8, pady=8, sticky="ew")
        
        # 添加音量显示标签
        self.volume_label = ttk.Label(audio_frame, text=f"{self.bg_volume.get():.2f}")
        self.volume_label.grid(row=0, column=2, padx=8, pady=8)
        
        # 绑定音量变化事件
        volume_scale.bind("<Motion>", self.update_volume_label)
        
        # 添加音量预设按钮
        ttk.Button(audio_frame, text="小声(-30dB)", width=10, 
                  command=lambda: self.set_volume(0.03)).grid(row=0, column=3, padx=4, pady=8)
        ttk.Button(audio_frame, text="适中(-24dB)", width=10,
                  command=lambda: self.set_volume(0.06)).grid(row=0, column=4, padx=4, pady=8)
        ttk.Button(audio_frame, text="较大(-18dB)", width=10,
                  command=lambda: self.set_volume(0.12)).grid(row=0, column=5, padx=4, pady=8)

        # 处理选项区域
        options_frame = ttk.LabelFrame(main_frame, text="处理选项")
        options_frame.grid(row=4, column=0, sticky="ew", padx=10, pady=10)
        options_frame.grid_columnconfigure(0, weight=1)
        options_frame.grid_columnconfigure(1, weight=1)
        options_frame.grid_columnconfigure(2, weight=1)

        # 处理选项变量已经在__init__中初始化并从配置加载了

        # 添加处理选项复选框 - 使用网格布局，每行3个选项
        ttk.Checkbutton(options_frame, text="三连屏效果", width=15,
                        variable=self.enable_triple).grid(row=1, column=0, padx=8, pady=8)
        ttk.Checkbutton(options_frame, text="轨道融合", width=15,
                        variable=self.enable_track_reuse).grid(row=1, column=1, padx=8, pady=8)
        ttk.Checkbutton(options_frame, text="音频处理", width=15,
                        variable=self.enable_audio).grid(row=1, column=2, padx=8, pady=8)
        ttk.Checkbutton(options_frame, text="元数据修改", width=15,
                        variable=self.enable_metadata).grid(row=2, column=0, padx=8, pady=8)
        ttk.Checkbutton(options_frame, text="取a片段", width=15,
                        variable=self.enable_use_a_clips).grid(row=2, column=1, padx=8, pady=8)
        ttk.Checkbutton(options_frame, text="模板视频合并", width=15,
                        variable=self.enable_template).grid(row=2, column=2, padx=8, pady=8)
        ttk.Checkbutton(options_frame, text="图片边框", width=15,
                        variable=self.enable_image_border).grid(row=3, column=0, padx=8, pady=8)

        # 添加视频叠加选项
        ttk.Checkbutton(options_frame, text="视频叠加", width=15,
                        variable=self.enable_overlay).grid(row=3, column=1, padx=8, pady=8)
                        
        # 添加只使用原始B视频选项
        ttk.Checkbutton(options_frame, text="只用原视频B", width=15,
                        variable=self.use_original_b_only).grid(row=3, column=2, padx=8, pady=8)
                        
        # 添加叠加透明度控制区域
        overlay_frame = ttk.LabelFrame(main_frame, text="视频叠加控制")
        overlay_frame.grid(row=6, column=0, sticky="ew", padx=10, pady=10)
        overlay_frame.grid_columnconfigure(1, weight=1)
        
        # 添加透明度控制滑块
        ttk.Label(overlay_frame, text="叠加透明度:").grid(row=0, column=0, padx=8, pady=8, sticky="w")
        
                        # 创建透明度控制滑块 - 注意值越小越透明
        opacity_scale = ttk.Scale(overlay_frame, 
                                 from_=0.1, 
                                 to=1.0,
                                 variable=self.overlay_opacity,
                                 orient=tk.HORIZONTAL,
                                 length=200)
        opacity_scale.grid(row=0, column=1, padx=8, pady=8, sticky="ew")
        
        # 添加透明度显示标签
        self.opacity_label = ttk.Label(overlay_frame, text=f"{self.overlay_opacity.get():.2f}")
        self.opacity_label.grid(row=0, column=2, padx=8, pady=8)
        
        # 绑定透明度变化事件
        opacity_scale.bind("<Motion>", self.update_opacity_label)
        
                                        # 添加透明度预设按钮
        ttk.Button(overlay_frame, text="低透明(0.3)", width=10, 
                  command=lambda: self.set_opacity(0.3)).grid(row=0, column=3, padx=4, pady=8)
        ttk.Button(overlay_frame, text="中透明(0.5)", width=10,
                  command=lambda: self.set_opacity(0.5)).grid(row=0, column=4, padx=4, pady=8)
        ttk.Button(overlay_frame, text="高透明(0.7)", width=10,
                  command=lambda: self.set_opacity(0.7)).grid(row=0, column=5, padx=4, pady=8)
                  
        # 添加调试信息标签
        ttk.Label(overlay_frame, text="注意：透明度值范围为0.1-1.0，值越小透明度越高").grid(row=1, column=0, columnspan=6, padx=8, pady=4, sticky="w")

        # 参数设置区域
        settings_frame = ttk.LabelFrame(main_frame, text="参数设置")
        settings_frame.grid(row=5, column=0, sticky="ew", padx=10, pady=10)
        settings_frame.grid_columnconfigure(1, weight=1)

        ttk.Label(settings_frame, text="插入帧间隔:", width=12).grid(row=0, column=0, padx=8, pady=8, sticky="w")
        ttk.Entry(settings_frame, textvariable=self.frame_count, width=10).grid(row=0, column=1, padx=8, pady=8, sticky="w")
        # 新增帧率和交错方式输入
        ttk.Label(settings_frame, text="视频A帧率:", width=12).grid(row=1, column=0, padx=8, pady=8, sticky="w")
        ttk.Entry(settings_frame, textvariable=self.video_a_fps, width=10).grid(row=1, column=1, padx=8, pady=8, sticky="w")
        ttk.Label(settings_frame, text="视频B帧率:", width=12).grid(row=2, column=0, padx=8, pady=8, sticky="w")
        ttk.Entry(settings_frame, textvariable=self.video_b_fps, width=10).grid(row=2, column=1, padx=8, pady=8, sticky="w")
        ttk.Label(settings_frame, text="输出帧率:", width=12).grid(row=3, column=0, padx=8, pady=8, sticky="w")
        ttk.Entry(settings_frame, textvariable=self.output_fps, width=10).grid(row=3, column=1, padx=8, pady=8, sticky="w")
        ttk.Label(settings_frame, text="交错方式:", width=12).grid(row=4, column=0, padx=8, pady=8, sticky="w")
        ttk.Combobox(settings_frame, textvariable=self.interleave_mode, values=["baba", "abab"], width=8, state="readonly").grid(row=4, column=1, padx=8, pady=8, sticky="w")

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=7, column=0, sticky="ew", padx=10, pady=10)
        button_frame.grid_columnconfigure(1, weight=1)

        ttk.Button(button_frame, text="开始处理", width=15,
                   command=self.start_processing).grid(row=0, column=0, padx=8, pady=8)
        ttk.Checkbutton(button_frame, text="处理后删除原视频", width=20,
                        variable=self.delete_original).grid(row=0, column=1, padx=8, pady=8, sticky="w")

        # 进度条
        self.progress_bar = ttk.Progressbar(
            main_frame,
            variable=self.progress_var,
            maximum=100,
            mode='determinate'
        )
        self.progress_bar.grid(row=8, column=0, sticky="ew", padx=10, pady=10)

        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="处理日志")
        log_frame.grid(row=9, column=0, sticky="nsew", padx=10, pady=10)
        log_frame.grid_columnconfigure(0, weight=1)
        log_frame.grid_rowconfigure(0, weight=1)

        # 创建带滚动条的文本框
        self.log_text = tk.Text(log_frame, height=12, wrap=tk.WORD)  # 增加文本框高度
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.grid(row=0, column=0, sticky="nsew", padx=(5, 0), pady=5)
        scrollbar.grid(row=0, column=1, sticky="ns", padx=(0, 5), pady=5)

        # 配置主框架的行权重
        main_frame.grid_rowconfigure(9, weight=1)

    def add_log(self, message):
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def select_video_a_folder(self):
        folder = filedialog.askdirectory(title="选择视频A文件夹")
        if folder:
            if folder not in self.video_a_folders:  # 避免重复添加
                self.video_a_folders.append(folder)
                self.video_a_listbox.insert(tk.END, folder)
                self.log_text.insert(tk.END, f"添加视频A文件夹: {folder}\n")
                self.save_settings()  # 添加文件夹后保存设置

    def select_video_b_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.video_b_folder.set(folder)
            self.save_settings()  # 选择文件夹后保存设置

    def select_output_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.output_folder.set(folder)
            self.save_settings()  # 选择输出文件夹后保存设置

    def select_template_folder(self):
        folder = filedialog.askdirectory(title="选择模板视频主文件夹")
        if folder:
            self.template_folder.set(folder)
            folder = os.path.normpath(folder)
            self.log_text.insert(tk.END, f"已选择模板视频主文件夹: {folder}\n")
            
            # 检查是否存在横屏和竖屏子文件夹
            vertical_folder = os.path.join(folder, "竖屏")
            horizontal_folder = os.path.join(folder, "横屏")
            
            # 检查文件夹是否存在
            vertical_exists = os.path.exists(vertical_folder) and os.path.isdir(vertical_folder)
            horizontal_exists = os.path.exists(horizontal_folder) and os.path.isdir(horizontal_folder)
            
            if not vertical_exists:
                self.log_text.insert(tk.END, "提示: 未找到竖屏子文件夹，可以创建 '竖屏' 子文件夹用于竖屏模板\n")
            else:
                self.log_text.insert(tk.END, f"已找到竖屏模板文件夹: {vertical_folder}\n")
                # 检查竖屏文件夹中的视频文件
                try:
                    vertical_videos = [f for f in os.listdir(vertical_folder) 
                                     if f.lower().endswith(('.mp4', '.avi', '.mov'))]
                    self.log_text.insert(tk.END, f"竖屏文件夹中有 {len(vertical_videos)} 个视频文件\n")
                except Exception as e:
                    self.log_text.insert(tk.END, f"读取竖屏文件夹失败: {str(e)}\n")
                
            if not horizontal_exists:
                self.log_text.insert(tk.END, "提示: 未找到横屏子文件夹，可以创建 '横屏' 子文件夹用于横屏模板\n")
            else:
                self.log_text.insert(tk.END, f"已找到横屏模板文件夹: {horizontal_folder}\n")
                # 检查横屏文件夹中的视频文件
                try:
                    horizontal_videos = [f for f in os.listdir(horizontal_folder) 
                                       if f.lower().endswith(('.mp4', '.avi', '.mov'))]
                    self.log_text.insert(tk.END, f"横屏文件夹中有 {len(horizontal_videos)} 个视频文件\n")
                except Exception as e:
                    self.log_text.insert(tk.END, f"读取横屏文件夹失败: {str(e)}\n")
            
            # 检查主文件夹中的视频文件
            try:
                main_videos = [f for f in os.listdir(folder) 
                             if f.lower().endswith(('.mp4', '.avi', '.mov'))]
                self.log_text.insert(tk.END, f"主文件夹中有 {len(main_videos)} 个视频文件\n")
            except Exception as e:
                self.log_text.insert(tk.END, f"读取主文件夹失败: {str(e)}\n")
                
            self.save_settings()  # 选择模板视频文件夹后保存设置

    def start_ab_layer_split(self):
        """处理AB层分离的方法"""
        # 选择视频A文件
        video_a = filedialog.askopenfilename(
            title="选择里层视频(A)",
            filetypes=[("视频文件", "*.mp4 *.avi *.mov")]
        )
        if not video_a:
            return

        # 选择视频B文件
        video_b = filedialog.askopenfilename(
            title="选择表层视频(B)",
            filetypes=[("视频文件", "*.mp4 *.avi *.mov")]
        )
        if not video_b:
            return

        # 选择输出文件位置
        output_file = filedialog.asksaveasfilename(
            defaultextension=".mp4",
            filetypes=[("MP4文件", "*.mp4")]
        )
        if not output_file:
            return

    def split_videos(self, input_folder):
        """使用流复制方式快速分割视频"""
        try:
            video_files = [f for f in os.listdir(input_folder)
                           if f.lower().endswith(('.mp4', '.avi', '.mov'))]

            if not video_files:
                self.add_log(f"在文件夹 {input_folder} 中没有找到可分割的视频文件")
                return True

            min_duration = self.split_min.get()
            max_duration = self.split_max.get()

            if min_duration >= max_duration:
                self.add_log("错误：最小分割时长必须小于最大分割时长")
                return False

            total_videos = len(video_files)
            self.add_log(f"开始处理 {total_videos} 个视频文件...")

            # 获取当前日期
            current_date = time.strftime("%Y%m%d")
            current_index = 1

            for video_file in video_files:
                video_path = os.path.join(input_folder, video_file)
                
                # 获取原始文件的扩展名
                original_extension = os.path.splitext(video_file)[1].lower()
                if not original_extension:
                    original_extension = '.mp4'  # 如果没有扩展名，默认使用.mp4
                
                # 获取视频信息
                video_info = self.processor.get_video_info(video_path)
                if not video_info:
                    self.add_log(f"无法获取视频信息: {video_file}")
                    continue

                total_duration = video_info['duration']
                
                # 检查是否需要分割
                if total_duration >= min_duration and total_duration > max_duration:
                    self.add_log(f"视频 {video_file} 需要分割 (时长: {total_duration:.2f}秒)")
                    
                    # 获取关键帧位置
                    keyframes_cmd = [
                        'ffprobe',
                        '-v', 'error',
                        '-skip_frame', 'nokey',
                        '-select_streams', 'v:0',
                        '-show_entries', 'frame=pts_time',
                        '-of', 'csv=print_section=0',
                        video_path
                    ]
                    
                    try:
                        result = subprocess.run(keyframes_cmd, capture_output=True, text=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
                        keyframes = [float(time) for time in result.stdout.strip().split('\n') if time.strip()]
                    except:
                        keyframes = []
                    
                    # 计算分割点
                    split_points = []
                    current_time = 0
                    remaining_duration = total_duration

                    while remaining_duration > 0:
                        # 如果剩余时长小于2倍最小时长，将其作为最后一段
                        if remaining_duration < min_duration * 2:
                            # 将剩余部分全部作为最后一段
                            if remaining_duration >= min_duration:
                                split_points.append(total_duration)
                            else:
                                # 如果有分割点，调整最后一个分割点
                                if split_points:
                                    split_points[-1] = total_duration
                            break
                        
                        # 正常分割逻辑
                        target_time = current_time + random.randint(min_duration, max_duration)
                        
                        # 找到最近的关键帧
                        if keyframes:
                            suitable_keyframes = [kf for kf in keyframes 
                                               if kf > current_time and kf <= target_time]
                            if suitable_keyframes:
                                split_point = suitable_keyframes[-1]
                            else:
                                split_point = target_time
                        else:
                            split_point = target_time
                        
                        split_points.append(split_point)
                        current_time = split_point
                        remaining_duration = total_duration - current_time
                    
                    # 执行分割
                    for i, split_point in enumerate(split_points):
                        start_time = 0 if i == 0 else split_points[i-1]
                        duration = split_point - start_time
                        
                        # 确保使用正确的扩展名
                        output_filename = f"{current_date}{current_index:02d}{original_extension}"
                        current_index += 1
                        output_path = os.path.join(input_folder, output_filename)
                        
                        # 使用流复制进行快速分割
                        cmd = [
                            'ffmpeg', '-y',
                            '-ss', str(start_time),
                            '-i', video_path,
                            '-t', str(duration),
                            '-c', 'copy',  # 直接复制流，不重新编码
                            '-avoid_negative_ts', '1',
                            '-map_metadata', '-1',  # 移除元数据
                            output_path
                        ]
                        
                        try:
                            subprocess.run(cmd, capture_output=True, check=True, **DEFAULT_SUBPROCESS_PARAMS)
                            self.add_log(f"已创建分段: {output_filename}")
                        except subprocess.CalledProcessError as e:
                            self.add_log(f"分割失败: {str(e)}")
                            continue
                    
                    # 如果分割成功且用户选择了删除原视频，则删除原视频
                    if self.delete_original.get():
                        try:
                            os.remove(video_path)
                            self.add_log(f"已删除原始视频: {video_file}")
                        except Exception as e:
                            self.add_log(f"删除原始视频失败: {str(e)}")
                
                else:
                    # 视频不需要分割，但仍需重命名
                    self.add_log(f"视频 {video_file} 不需要分割 (时长: {total_duration:.2f}秒)，仅重命名")
                    
                    # 生成基于日期的新文件名，确保使用正确的扩展名
                    new_filename = f"{current_date}{current_index:02d}{original_extension}"
                    current_index += 1
                    
                    # 如果文件名已存在，添加序号
                    while os.path.exists(os.path.join(input_folder, new_filename)) and new_filename != video_file:
                        new_filename = f"{current_date}{current_index:02d}{original_extension}"
                        current_index += 1
                    
                    # 如果新文件名与原文件名不同，则重命名
                    if new_filename != video_file:
                        new_path = os.path.join(input_folder, new_filename)
                        try:
                            # 如果目标文件已存在，先删除
                            if os.path.exists(new_path):
                                os.remove(new_path)
                            # 重命名文件
                            os.rename(video_path, new_path)
                            self.add_log(f"已重命名: {video_file} -> {new_filename}")
                        except Exception as e:
                            self.add_log(f"重命名失败: {str(e)}")

            return True

        except Exception as e:
            self.add_log(f"视频处理过程出错: {str(e)}")
            import traceback
            self.add_log(f"详细错误: {traceback.format_exc()}")
            return False
    def start_processing(self):
        if not self.validate_inputs():
            return
            
        # 检查叠加视频文件夹（仅当启用叠加功能时）
        if self.enable_overlay.get():
            if not self.processed_b_folder.get():
                messagebox.showerror("错误", "请选择叠加视频文件夹")
                return
                
            if not os.path.exists(self.processed_b_folder.get()):
                messagebox.showerror("错误", "叠加视频文件夹不存在")
                return
            
            # 检查文件夹中是否有视频文件
            processed_b_files = [f for f in os.listdir(self.processed_b_folder.get()) 
                               if f.lower().endswith(('.mp4', '.mov', '.avi', '.mkv'))]
            if not processed_b_files:
                messagebox.showerror("错误", "叠加视频文件夹中没有找到任何视频文件")
                return
                
        self.log_text.delete(1.0, tk.END)  # 清空日志区域
        self.add_log("开始处理视频...\n")
        self.save_settings()  # 保存当前设置

        # 准备处理选项
        process_options = {
            'enable_triple': self.enable_triple.get(),
            'enable_track_reuse': self.enable_track_reuse.get(),
            'enable_audio': self.enable_audio.get(),
            'audio_folder': self.audio_folder.get() if self.enable_audio.get() else None,
            'bg_volume': self.bg_volume.get(),
            'enable_metadata': self.enable_metadata.get(),
            'enable_trim': self.enable_trim.get(),
            'intro_duration': self.intro_duration.get(),
            'outro_start': self.outro_start.get(),
            'enable_template': self.enable_template.get(),
            'template_folder': self.template_folder.get() if self.enable_template.get() else None,
            'video_a_fps': self.video_a_fps.get(),
            'video_b_fps': self.video_b_fps.get(),
            'output_fps': self.output_fps.get(),
            'interleave_mode': self.interleave_mode.get(),
            'enable_use_a_clips': self.enable_use_a_clips.get(),
            'enable_split': self.enable_split.get(),
            'split_min': self.split_min.get(),
            'split_max': self.split_max.get(),
            'enable_image_border': self.enable_image_border.get(),
            'image_folder': self.image_folder.get() if self.enable_image_border.get() else None,
            'enable_overlay': self.enable_overlay.get(),
            'processed_b_folder': self.processed_b_folder.get() if self.enable_overlay.get() else None,
            'overlay_opacity': self.overlay_opacity.get(),
            'triple_b_folder': self.triple_b_folder.get() if self.enable_triple.get() else None,  # 添加三连屏视频B文件夹
            'use_original_b_only': self.use_original_b_only.get()  # 添加只使用原始视频B的选项
        }

        def process():
            try:
                total_folders = len(self.video_a_folders)
                for folder_index, input_folder in enumerate(self.video_a_folders, 1):
                    # 获取文件夹名称
                    folder_name = os.path.basename(input_folder)
                    
                    self.log_text.insert(tk.END, f"\n开始处理文件夹 ({folder_index}/{total_folders}): {folder_name}\n")
                    
                    # 获取当前文件夹中的所有视频文件
                    video_files = [f for f in os.listdir(input_folder)
                                 if f.lower().endswith(('.mp4', '.avi', '.mov'))]

                    if not video_files:
                        self.log_text.insert(tk.END, f"文件夹 {folder_name} 中未找到视频文件\n")
                        continue

                    total_files = len(video_files)
                    self.log_text.insert(tk.END, f"在 {folder_name} 中找到 {total_files} 个视频文件\n")
                    
                    # 首先处理片头片尾（如果启用）
                    if self.enable_trim.get():
                        self.log_text.insert(tk.END, f"开始对文件夹 {folder_name} 进行片头片尾处理...\n")
                        
                        # 将分钟转换为秒
                        intro_duration_sec = self.intro_duration.get() * 60
                        outro_start_sec = self.outro_start.get() * 60
                        
                        for file_index, video_file in enumerate(video_files, 1):
                            video_path = os.path.join(input_folder, video_file)
                            temp_output = os.path.join(input_folder, f"temp_{video_file}")
                            
                            self.log_text.insert(tk.END, f"处理片头片尾: {file_index}/{total_files} - {video_file}\n")
                            
                            # 处理片头片尾
                            success = self.processor.trim_intro_outro(
                                video_path,
                                intro_duration_sec,
                                outro_start_sec,
                                temp_output
                            )
                            
                            if success:
                                # 如果成功，替换原文件
                                try:
                                    os.remove(video_path)
                                    os.rename(temp_output, video_path)
                                    self.log_text.insert(tk.END, f"已替换原文件: {video_file}\n")
                                except Exception as e:
                                    self.log_text.insert(tk.END, f"替换文件失败: {str(e)}\n")
                            else:
                                self.log_text.insert(tk.END, f"片头片尾处理失败: {video_file}\n")
                                # 清理临时文件
                                if os.path.exists(temp_output):
                                    try:
                                        os.remove(temp_output)
                                    except:
                                        pass
                        
                        # 如果只启用了片头片尾裁剪功能，跳过后续处理
                        only_trim = (not self.enable_split.get() and
                                     not self.enable_triple.get() and 
                                     not self.enable_track_reuse.get() and 
                                     not self.enable_audio.get() and 
                                     not self.enable_metadata.get() and
                                     not self.enable_template.get())
                        
                        if only_trim:
                            self.log_text.insert(tk.END, f"只启用了片头片尾裁剪功能，跳过后续处理步骤\n")
                            continue
                    
                    # 然后进行视频分割（如果启用）
                    if self.enable_split.get():
                        self.log_text.insert(tk.END, f"开始对文件夹 {folder_name} 进行视频分割...\n")
                        if not self.split_videos(input_folder):
                            self.log_text.insert(tk.END, f"文件夹 {folder_name} 视频分割失败\n")
                            continue
                        
                        # 重新获取分割后的视频文件列表
                        video_files = [f for f in os.listdir(input_folder)
                                    if f.lower().endswith(('.mp4', '.avi', '.mov'))]
                        total_files = len(video_files)
                        self.log_text.insert(tk.END, f"分割后共有 {total_files} 个视频文件\n")
                        
                        # 如果只启用了视频分割功能（或视频分割+片头片尾），跳过后续处理
                        only_basic = (not self.enable_triple.get() and 
                                     not self.enable_track_reuse.get() and 
                                     not self.enable_audio.get() and 
                                     not self.enable_metadata.get() and
                                     not self.enable_template.get())
                        
                        if only_basic:
                            if self.enable_trim.get():
                                self.log_text.insert(tk.END, f"只启用了视频分割和片头片尾裁剪功能，跳过后续处理步骤\n")
                            else:
                                self.log_text.insert(tk.END, f"只启用了视频分割功能，跳过后续处理步骤\n")
                            continue
                    
                    # 在输出目录创建对应的子文件夹
                    output_subfolder = os.path.join(self.output_folder.get(), folder_name)
                    os.makedirs(output_subfolder, exist_ok=True)
                    
                    # 处理当前文件夹中的每个视频
                    for file_index, video_file in enumerate(video_files, 1):
                        video_path = os.path.join(input_folder, video_file)
                        self.log_text.insert(tk.END, f"\n处理第 {file_index}/{total_files} 个视频: {video_file}\n")

                        # 创建临时文件夹
                        temp_folder = os.path.join(output_subfolder, f"temp_{int(time.time())}")
                        os.makedirs(temp_folder, exist_ok=True)

                        try:
                            # 处理单个视频
                            success = self.processor.process_single_video(
                                video_path,  # 使用原视频（可能已经处理过片头片尾）
                                self.video_b_folder.get(),
                                int(self.frame_count.get()),
                                output_subfolder,  # 使用输出子文件夹作为输出目录
                                temp_folder,
                                process_options
                            )

                            if success:
                                self.log_text.insert(tk.END, f"视频 {video_file} 处理完成\n")
                                
                                # 如果处理成功且用户选择了删除原视频
                                if self.delete_original.get():
                                    try:
                                        os.remove(video_path)
                                        self.log_text.insert(tk.END, f"已删除原始视频: {video_file}\n")
                                    except Exception as e:
                                        self.log_text.insert(tk.END, f"删除原始视频失败: {str(e)}\n")
                            else:
                                self.log_text.insert(tk.END, f"视频 {video_file} 处理失败\n")

                        except Exception as e:
                            self.log_text.insert(tk.END, f"处理 {video_file} 时出错: {str(e)}\n")
                        finally:
                            # 更新进度条 - 考虑文件夹进度和文件进度
                            total_progress = ((folder_index - 1) * 100 + (file_index / total_files) * 100) / total_folders
                            self.progress_var.set(total_progress)
                            self.root.update_idletasks()

                            # 清理临时文件夹
                            if os.path.exists(temp_folder):
                                try:
                                    shutil.rmtree(temp_folder)
                                except Exception as e:
                                    self.log_text.insert(tk.END, f"清理临时文件失败: {str(e)}\n")

                self.log_text.insert(tk.END, "\n所有文件夹处理完成\n")
                messagebox.showinfo("完成", "所有视频处理完成！")

            except Exception as e:
                self.log_text.insert(tk.END, f"处理过程出现错误: {str(e)}\n")
                messagebox.showerror("错误", f"处理过程出现错误: {str(e)}")
            finally:
                self.progress_var.set(0)
                self.root.update_idletasks()

        # 在新线程中运行处理过程
        threading.Thread(target=process, daemon=True).start()

    def validate_inputs(self):
        if not self.video_a_folders:
            messagebox.showerror("错误", "请至少选择一个视频A文件夹")
            return False
            
        for folder in self.video_a_folders:
            if not os.path.exists(folder):
                messagebox.showerror("错误", f"视频A文件夹不存在: {folder}")
                return False
                
        if not os.path.exists(self.video_b_folder.get()):
            messagebox.showerror("错误", "视频B文件夹不存在")
            return False
            
        if not os.path.exists(self.output_folder.get()):
            try:
                os.makedirs(self.output_folder.get())
            except Exception as e:
                messagebox.showerror("错误", f"创建输出文件夹失败: {str(e)}")
                return False
                
        # 验证模板视频文件夹（仅当启用模板合并时）
        if self.enable_template.get():
            if not self.template_folder.get():
                messagebox.showerror("错误", "请选择模板视频文件夹")
                return False
                
            if not os.path.exists(self.template_folder.get()):
                messagebox.showerror("错误", "模板视频文件夹不存在")
                return False
                
            # 检查模板文件夹及其子文件夹中是否有视频文件
            has_videos = False
            for root, dirs, files in os.walk(self.template_folder.get()):
                video_files = [f for f in files if f.lower().endswith(('.mp4', '.avi', '.mov'))]
                if video_files:
                    has_videos = True
                    self.log_text.insert(tk.END, f"在 '{root}' 中找到 {len(video_files)} 个视频文件\n")
                    break
                    
            if not has_videos:
                messagebox.showerror("错误", "在模板视频文件夹及其子文件夹中没有找到任何视频文件")
                return False
                
        return True

    def process_videos(self):
        try:
            video_files = [f for f in os.listdir(self.video_a_folders[0])
                           if f.endswith(('.mp4', '.avi', '.mov'))]

            if not video_files:
                messagebox.showwarning("警告", "没有找到可处理的视频文件")
                return

            total = len(video_files)
            self.add_log(f"找到 {total} 个视频文件待处理")

            for i, video_file in enumerate(video_files, 1):
                self.add_log(f"正在处理第 {i}/{total} 个视频: {video_file}")

                video_path = os.path.join(self.video_a_folders[0], video_file)
                temp_folder = os.path.join(self.output_folder.get(), "temp")

                success = self.processor.process_single_video(
                    video_path,
                    self.video_b_folder.get(),
                    self.frame_count.get(),
                    self.output_folder.get(),
                    temp_folder,
                    self.triple_screen.get()
                )

                # 如果处理成功且用户选择了删除原视频
                if success and self.delete_original.get():
                    try:
                        os.remove(video_path)
                        self.add_log(f"已删除原视频文件: {video_file}")
                    except Exception as e:
                        self.add_log(f"删除原视频文件失败: {str(e)}")

                progress = (i / total) * 100
                self.progress_var.set(progress)
                self.root.update_idletasks()

            self.add_log("所有视频处理完成")
            messagebox.showinfo("完成", "所有视频处理完成")

        except Exception as e:
            error_msg = f"处理过程出错: {str(e)}"
            self.add_log(error_msg)
            messagebox.showerror("错误", error_msg)
        finally:
            self.progress_var.set(0)
            self.root.update_idletasks()

    def clear_video_a_folders(self):
        self.video_a_folders.clear()
        self.video_a_listbox.delete(0, tk.END)
        self.save_settings()  # 清空文件夹后保存设置

    def load_settings(self):
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                # 加载视频A文件夹
                self.video_a_folders = settings.get('video_a_folders', [])
                
                # 加载其他设置
                self.video_b_folder.set(settings.get('video_b_folder', ''))
                self.output_folder.set(settings.get('output_folder', ''))
                self.frame_count.set(settings.get('frame_count', 100))
                self.acceleration.set(settings.get('acceleration', 'cpu'))
                self.triple_screen.set(settings.get('triple_screen', False))
                
                # 加载视频分割设置
                self.enable_split.set(settings.get('enable_split', False))
                self.split_min.set(settings.get('split_min', 30))
                self.split_max.set(settings.get('split_max', 60))
                
                # 加载删除原视频选项
                self.delete_original.set(settings.get('delete_original', False))
                
                # 加载片头片尾处理相关设置
                self.enable_trim.set(settings.get('enable_trim', False))
                self.intro_duration.set(settings.get('intro_duration', 2.67))
                self.outro_start.set(settings.get('outro_start', 43.67))
                
                # 加载处理选项设置
                self.enable_random_frames.set(settings.get('enable_random_frames', True))
                self.enable_ab_layer.set(settings.get('enable_ab_layer', True))
                self.enable_triple.set(settings.get('enable_triple', False))
                self.enable_track_reuse.set(settings.get('enable_track_reuse', False))
                self.enable_audio.set(settings.get('enable_audio', True))
                self.enable_metadata.set(settings.get('enable_metadata', True))
                self.enable_sketch.set(settings.get('enable_sketch', False))
                self.enable_use_a_clips.set(settings.get('enable_use_a_clips', True))
                
                # 加载帧率设置
                self.video_a_fps.set(settings.get('video_a_fps', 60))
                self.video_b_fps.set(settings.get('video_b_fps', 60))
                self.output_fps.set(settings.get('output_fps', 120))
                self.interleave_mode.set(settings.get('interleave_mode', 'baba'))
                
                # 加载模板视频设置
                self.template_folder.set(settings.get('template_folder', ''))
                self.enable_template.set(settings.get('enable_template', False))
                
                # 加载音频设置
                self.audio_folder.set(settings.get('audio_folder', ''))
                self.bg_volume.set(settings.get('bg_volume', 0.06))
                
                # 加载图片设置
                self.image_folder.set(settings.get('image_folder', ''))
                self.enable_image_border.set(settings.get('enable_image_border', False))
                
                # 加载叠加视频文件夹和叠加设置
                self.processed_b_folder.set(settings.get('processed_b_folder', ''))
                self.enable_overlay.set(settings.get('enable_overlay', False))
                self.overlay_opacity.set(settings.get('overlay_opacity', 0.5))
                
                # 加载三连屏视频B文件夹
                self.triple_b_folder.set(settings.get('triple_b_folder', ''))
                
                # 加载只使用原视频B的设置
                self.use_original_b_only.set(settings.get('use_original_b_only', False))
                
        except Exception as e:
            self.log_text.insert(tk.END, f"加载设置时出错: {str(e)}\n")
            # 出错时使用默认设置

    def save_settings(self):
        """保存当前设置到配置文件"""
        try:
            settings = {
                # 基本设置
                'frame_count': self.frame_count.get(),
                'video_a_folders': self.video_a_folders,
                'video_b_folder': self.video_b_folder.get(),
                'output_folder': self.output_folder.get(),
                'delete_original': self.delete_original.get(),
                
                # 视频分割设置
                'enable_split': self.enable_split.get(),
                'split_min': self.split_min.get(),
                'split_max': self.split_max.get(),
                
                # 片头片尾设置
                'enable_trim': self.enable_trim.get(),
                'intro_duration': self.intro_duration.get(),
                'outro_start': self.outro_start.get(),
                
                # 处理选项
                'enable_random_frames': self.enable_random_frames.get(),
                'enable_ab_layer': self.enable_ab_layer.get(),
                'enable_triple': self.enable_triple.get(),
                'enable_track_reuse': self.enable_track_reuse.get(),
                'enable_audio': self.enable_audio.get(),
                'enable_metadata': self.enable_metadata.get(),
                'enable_sketch': self.enable_sketch.get(),
                'enable_use_a_clips': self.enable_use_a_clips.get(),
                
                # 帧率和交错方式
                'video_a_fps': self.video_a_fps.get(),
                'video_b_fps': self.video_b_fps.get(),
                'output_fps': self.output_fps.get(),
                'interleave_mode': self.interleave_mode.get(),
                
                # 模板视频相关设置
                'template_folder': self.template_folder.get(),
                'enable_template': self.enable_template.get(),
                
                # 音频文件夹和音量设置
                'audio_folder': self.audio_folder.get(),
                'bg_volume': self.bg_volume.get(),
                
                # 图片边框相关设置
                'image_folder': self.image_folder.get(),
                'enable_image_border': self.enable_image_border.get(),
                
                                 # 叠加视频文件夹和叠加设置
                 'processed_b_folder': self.processed_b_folder.get(),
                 'enable_overlay': self.enable_overlay.get(),
                 'overlay_opacity': self.overlay_opacity.get(),
                 
                 # 三连屏视频B文件夹
                 'triple_b_folder': self.triple_b_folder.get(),
                 
                 # 只使用原视频B的设置
                 'use_original_b_only': self.use_original_b_only.get()
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=4)
                
            print("设置已保存")
        except Exception as e:
            print(f"保存设置失败: {str(e)}")
    
    def on_closing(self):
        """窗口关闭时保存设置并退出"""
        self.save_settings()
        self.root.destroy()
    
    def bind_settings_change(self):
        """绑定各种设置变更事件，自动保存设置"""
        # 跟踪视频A文件夹列表框选择变化
        self.video_a_listbox.bind('<<ListboxSelect>>', lambda e: self.save_settings())
        
        # 跟踪输入框变化
        self.video_b_folder.trace_add("write", lambda *args: self.save_settings())
        self.output_folder.trace_add("write", lambda *args: self.save_settings())
        self.template_folder.trace_add("write", lambda *args: self.save_settings())
        self.audio_folder.trace_add("write", lambda *args: self.save_settings())
        self.image_folder.trace_add("write", lambda *args: self.save_settings())
        self.triple_b_folder.trace_add("write", lambda *args: self.save_settings())
        self.frame_count.trace_add("write", lambda *args: self.save_settings())
        self.split_min.trace_add("write", lambda *args: self.save_settings())
        self.split_max.trace_add("write", lambda *args: self.save_settings())
        self.intro_duration.trace_add("write", lambda *args: self.save_settings())
        self.outro_start.trace_add("write", lambda *args: self.save_settings())
        self.video_a_fps.trace_add("write", lambda *args: self.save_settings())
        self.video_b_fps.trace_add("write", lambda *args: self.save_settings())
        self.output_fps.trace_add("write", lambda *args: self.save_settings())
        
        # 跟踪复选框和单选按钮变化
        self.enable_split.trace_add("write", lambda *args: self.save_settings())
        self.delete_original.trace_add("write", lambda *args: self.save_settings())
        self.enable_trim.trace_add("write", lambda *args: self.save_settings())
        self.enable_random_frames.trace_add("write", lambda *args: self.save_settings())
        self.enable_ab_layer.trace_add("write", lambda *args: self.save_settings())
        self.enable_triple.trace_add("write", lambda *args: self.save_settings())
        self.enable_track_reuse.trace_add("write", lambda *args: self.save_settings())
        self.enable_audio.trace_add("write", lambda *args: self.save_settings())
        self.enable_metadata.trace_add("write", lambda *args: self.save_settings())
        self.enable_sketch.trace_add("write", lambda *args: self.save_settings())
        self.enable_use_a_clips.trace_add("write", lambda *args: self.save_settings())
        self.enable_template.trace_add("write", lambda *args: self.save_settings())
        self.enable_image_border.trace_add("write", lambda *args: self.save_settings())
        self.use_original_b_only.trace_add("write", lambda *args: self.save_settings())
        
        # 跟踪下拉框变化
        self.interleave_mode.trace_add("write", lambda *args: self.save_settings())
        
        # 跟踪音量滑块变化
        self.bg_volume.trace_add("write", lambda *args: self.update_volume_label())
    
    def select_audio_folder(self):
        """选择自定义音频文件夹"""
        folder = filedialog.askdirectory(title="选择音频文件夹")
        if folder:
            self.audio_folder.set(folder)
            # 检查文件夹中的音频文件
            audio_files = [f for f in os.listdir(folder) 
                         if f.lower().endswith(('.mp3', '.wav', '.aac', '.flac', '.ogg'))]
            self.log_text.insert(tk.END, f"已选择音频文件夹: {folder}\n")
            self.log_text.insert(tk.END, f"找到 {len(audio_files)} 个音频文件\n")
            self.save_settings()  # 选择音频文件夹后保存设置

    def update_volume_label(self, event=None):
        """更新音量显示标签"""
        volume = self.bg_volume.get()
        # 将音量系数转换为分贝
        db = 20 * math.log10(volume) if volume > 0 else -60
        self.volume_label.config(text=f"{volume:.2f} ({db:.1f}dB)")
        self.save_settings()

    def set_volume(self, value):
        """设置音量预设值"""
        self.bg_volume.set(value)
        self.update_volume_label()
        self.save_settings()

    def select_image_folder(self):
        """选择图片文件夹"""
        folder = filedialog.askdirectory(title="选择图片文件夹")
        if folder:
            self.image_folder.set(folder)
            # 检查文件夹中的图片文件
            image_files = [f for f in os.listdir(folder) 
                         if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.webp'))]
            self.log_text.insert(tk.END, f"已选择图片文件夹: {folder}\n")
            self.log_text.insert(tk.END, f"找到 {len(image_files)} 个图片文件\n")
            
            # 如果图片数量少于1张，提示用户
            if len(image_files) < 1:
                self.log_text.insert(tk.END, "警告：图片文件夹为空，需要至少1张图片\n")
                messagebox.showwarning("警告", "图片文件夹为空，需要至少1张图片")
            else:
                # 随机展示一张图片名称
                sample_image = random.choice(image_files)
                self.log_text.insert(tk.END, f"随机图片示例: {sample_image}\n")
            
            self.save_settings()  # 选择图片文件夹后保存设置
            
    def select_processed_b_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.processed_b_folder.set(folder)
            self.add_log(f"已选择叠加视频文件夹: {folder}\n")
            self.save_settings()  # 选择叠加视频文件夹后保存设置
            
    def select_triple_b_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.triple_b_folder.set(folder)
            self.add_log(f"已选择三连屏视频B文件夹: {folder}\n")
            self.save_settings()  # 选择三连屏视频B文件夹后保存设置

    def update_opacity_label(self, event=None):
        """更新透明度显示标签"""
        opacity = self.overlay_opacity.get()
        self.opacity_label.config(text=f"{opacity:.2f}")
        # 确保透明度值在0-1之间
        if opacity < 0:
            self.overlay_opacity.set(0)
        elif opacity > 1:
            self.overlay_opacity.set(1)
        self.save_settings()

    def set_opacity(self, value):
        """设置透明度预设值"""
        self.overlay_opacity.set(value)
        self.update_opacity_label()
        self.save_settings()

if __name__ == "__main__":
    root = tk.Tk()
    app = VideoProcessorUI(root)
    root.mainloop()