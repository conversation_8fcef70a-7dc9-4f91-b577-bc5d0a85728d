import os
import json
import time
import random  # 添加random模块导入
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import subprocess
import sys
import urllib.request
import zipfile
import shutil
import re
import socket
import tempfile
from datetime import datetime, timedelta
import string
import cv2
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.by import By

class AppConfig:
    """应用程序配置管理类"""
    def __init__(self, base_dir=None):
        if base_dir is None:
            base_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 设置基础数据目录
        self.base_dir = base_dir
        self.data_dir = os.path.join(self.base_dir, "kuaishou_data")
        self.cookies_dir = os.path.join(self.data_dir, "cookies")
        self.logs_dir = os.path.join(self.data_dir, "logs")
        self.cache_dir = os.path.join(self.data_dir, "cache")
        self.config_file = os.path.join(self.data_dir, "app_config.json")
        
        # 创建必要的目录
        self._init_directories()
    
    def _init_directories(self):
        """初始化所有必要的目录结构"""
        directories = [
            self.data_dir,
            self.cookies_dir,
            self.logs_dir,
            self.cache_dir
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"创建目录: {directory}")
    
    def get_cookies_path(self, account_name):
        """获取账号cookies文件路径"""
        return os.path.join(self.cookies_dir, f'kuaishou_cookies_{account_name}.json')
    
    def get_log_path(self, log_name):
        """获取日志文件路径"""
        return os.path.join(self.logs_dir, log_name)
    

        
    def save_accounts_config(self, accounts):
        """保存账号配置到文件"""
        try:
            config_data = []
            for account in accounts:
                account_data = {
                    'name': account.name,
                    'video_description': account.video_description,
                    'horizontal_cover_path': account.horizontal_cover_path,
                    'vertical_cover_path': account.vertical_cover_path,
                    'use_custom_cover': account.use_custom_cover,
                    'video_folder': account.video_folder,
                    'upload_count': account.upload_count,
                    'success_count': account.success_count,
                    'fail_count': account.fail_count,
                    'upload_hour': account.upload_hour,
                    'upload_minute': account.upload_minute,
                    'last_upload_date': account.last_upload_date.strftime('%Y-%m-%d') if account.last_upload_date else None,
                    'daily_upload_count': account.daily_upload_count,
                    'use_scheduled_time': account.use_scheduled_time,
                    # 设备指纹相关属性
                    'user_agent': getattr(account, 'user_agent', ''),
                    'screen_resolution': getattr(account, 'screen_resolution', '1920x1080'),
                    'hardware_concurrency': getattr(account, 'hardware_concurrency', 4),
                    'device_memory': getattr(account, 'device_memory', 8),
                    'webgl_vendor': getattr(account, 'webgl_vendor', 'Google Inc. (Intel)'),
                    'webgl_renderer': getattr(account, 'webgl_renderer', 'ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0)'),
                    'canvas_fingerprint': getattr(account, 'canvas_fingerprint', 'default')
                }
                config_data.append(account_data)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            print(f"配置已保存到: {self.config_file}")
            return True
        except Exception as e:
            print(f"保存配置时出错: {str(e)}")
            return False

    def load_accounts_config(self):
        """从文件加载账号配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                print(f"从 {self.config_file} 加载了配置")
                
                # 转换日期格式
                for account_data in config_data:
                    if 'last_upload_date' in account_data and account_data['last_upload_date']:
                        try:
                            account_data['last_upload_date'] = datetime.strptime(account_data['last_upload_date'], '%Y-%m-%d').date()
                        except:
                            account_data['last_upload_date'] = None
                    
                    # 确保daily_upload_count有默认值
                    if 'daily_upload_count' not in account_data or not isinstance(account_data['daily_upload_count'], int) or account_data['daily_upload_count'] < 1:
                        account_data['daily_upload_count'] = 1
                    
                    # 确保use_scheduled_time有默认值
                    if 'use_scheduled_time' not in account_data:
                        account_data['use_scheduled_time'] = False
                            
                return config_data
            return []
        except Exception as e:
            print(f"加载配置时出错: {str(e)}")
            return []

# 创建全局配置实例
app_config = AppConfig()

# 尝试导入selenium相关模块
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.common.action_chains import ActionChains
    from selenium.webdriver.common.keys import Keys
except ImportError:
    messagebox.showerror("错误", "请先安装selenium: pip install selenium")
    sys.exit(1)

# 尝试导入undetected_chromedriver，但不是必须的
try:
    import undetected_chromedriver as uc
except ImportError:
    uc = None

# 添加账号配置类
class AccountConfig:
    def __init__(self, name, cookies_file=None, video_description="", 
                 horizontal_cover_path="", vertical_cover_path="",
                 use_custom_cover=False, video_folder="", daily_upload_count=10):
        self.name = name
        self.cookies_file = cookies_file
        self.video_description = video_description
        self.horizontal_cover_path = horizontal_cover_path
        self.vertical_cover_path = vertical_cover_path
        self.use_custom_cover = use_custom_cover
        self.video_folder = video_folder
        self.upload_count = 0  # 已上传视频数量
        self.success_count = 0  # 成功上传数量
        self.fail_count = 0  # 失败上传数量
        self.upload_hour = 0  # 上传时间-小时(24小时制)
        self.upload_minute = 0  # 上传时间-分钟
        self.last_upload_date = None  # 最后上传日期
        self.daily_upload_count = daily_upload_count  # 每天上传视频数量
        self.use_scheduled_time = False  # 是否使用定时发布
        self.today_uploads = 0  # 今天已上传的数量
        self.last_reset_time = datetime.now()  # 上次重置计数的时间
        self.time_offset_minutes = 0  # 随机时间偏移
        self.last_offset_date = datetime.now().date()  # 最后一次生成偏移的日期
        
        # 设备指纹相关属性
        self.user_agent = ""  # 用户代理
        self.screen_resolution = "1920x1080"  # 屏幕分辨率
        self.hardware_concurrency = 4  # 硬件并发数
        self.device_memory = 8  # 设备内存(GB)
        self.webgl_vendor = "Google Inc. (Intel)"  # WebGL厂商
        self.webgl_renderer = "ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0)"  # WebGL渲染器
        self.canvas_fingerprint = "default"  # Canvas指纹

class KuaishouUploader:
    def __init__(self):
        self.creator_url = "https://cp.kuaishou.com/profile"
        self.driver = None
        self.chrome_path = None
        self.chrome_version = None
        self.is_running = False
        self.current_video = ""
        self.stop_flag = False
        self.status_callback = None  # 添加状态回调属性
        self.program_start_date = datetime.now().date()  # 记录程序启动日期
        
        # 添加账号相关属性
        self.accounts = []  # 存储所有账号配置
        self.current_account = None  # 当前正在使用的账号
        
        # 添加视频上传相关属性
        self.video_description = ""  # 视频描述
        self.cover_path = ""  # 封面图片路径
        self.use_custom_cover = False  # 是否使用自定义封面
        
        # 使用全局配置
        self.config = app_config
        
        # 预初始化Chrome配置
        self.chrome_options = self.prepare_chrome_options()
        
        # 预先检测Chrome并准备chromedriver
        self.detect_chrome()
        self.prepare_chromedriver()

    def add_account(self, name, cookies_file=None, video_description="", horizontal_cover_path="", vertical_cover_path="", use_custom_cover=False, video_folder="", daily_upload_count=10):
     account = AccountConfig(name, cookies_file, video_description, horizontal_cover_path, vertical_cover_path, use_custom_cover, video_folder, daily_upload_count)
     self.accounts.append(account)
     return account

    def switch_account(self, account):
        """切换到指定账号"""
        if self.current_account != account:
            self.current_account = account
            # 重置cookies
            if self.driver:
                self.driver.delete_all_cookies()
                cookies_file = self.config.get_cookies_path(account.name)
                if os.path.exists(cookies_file):
                    self.load_cookies()
                    return True
        return False

    def run_multi_accounts(self, default_video_folder, status_callback=None):
        """运行多账号上传任务"""
        self.is_running = True
        self.stop_flag = False
        self.user_stopped = False  # 重置用户停止标志
        self.status_callback = status_callback  # 设置状态回调
        
        def log_status(message):
            """记录状态到控制台和状态回调"""
            print(message)
            if status_callback:
                status_callback(message)
        
        def check_and_reset_upload_count():
            """检查并重置账号的上传计数"""
            current_time_var = datetime.now()
            for account in self.accounts:
                if not hasattr(account, 'last_reset_time'):
                    account.last_reset_time = current_time_var
                    continue
                
                # 计算距离上次重置的时间
                time_diff = current_time_var - account.last_reset_time
                if time_diff.total_seconds() >= 12 * 3600:  # 12小时 = 12 * 3600秒
                    # 重置计数
                    account.today_uploads = 0
                    account.last_reset_time = current_time_var
                    # 使用print而不是log_status，避免作用域问题
                    print(f"账号 {account.name} 的上传计数已重置（12小时已过）")
                    if status_callback:
                        status_callback(f"账号 {account.name} 的上传计数已重置（12小时已过）")
        
        try:
            # 确保每次启动都重新设置程序启动时间，而不是使用hasattr检查
            self.program_start_time = datetime.now()
            log_status(f"程序启动时间: {self.program_start_time.strftime('%H:%M:%S')}")
            
            if not self.accounts:
                log_status("错误：未添加任何账号配置")
                return
            
            # 添加登录失败计数字典
            if not hasattr(self, 'login_failure_counts'):
                self.login_failure_counts = {}
                
            # 添加上次检查时间，用于控制循环间隔
            last_check_time = datetime.now() - timedelta(seconds=60)  # 初始化为60秒前，确保第一次立即检查
            
            while not self.stop_flag:
                current_time = datetime.now()
                
                # 检查是否需要等待，确保每次循环之间至少间隔60秒
                time_since_last_check = (current_time - last_check_time).total_seconds()
                if time_since_last_check < 60:
                    # 如果距离上次检查不足60秒，则等待剩余时间
                    wait_seconds = 60 - time_since_last_check
                    log_status(f"避免频繁检查，等待 {int(wait_seconds)} 秒...")
                    # 直接使用模块名称
                    import time as time_module
                    time_module.sleep(wait_seconds)
                    current_time = datetime.now()
                
                # 更新上次检查时间
                last_check_time = current_time
                current_date = current_time.date()
                
                # 检查是否是新的一天，如果是则重置程序启动日期和时间
                if current_date > self.program_start_date:
                    log_status(f"检测到新的一天已开始，重置程序启动日期从 {self.program_start_date} 到 {current_date}")
                    self.program_start_date = current_date
                    # 在新的一天更新程序启动时间，以便正确处理定时上传
                    self.program_start_time = current_time
                    log_status(f"更新程序启动时间为: {self.program_start_time.strftime('%H:%M:%S')}")
                    # 重置登录失败计数
                    self.login_failure_counts = {}
                
                # 检查并重置上传计数
                check_and_reset_upload_count()
                
                # 排序账号列表，优先处理超时的账号
                accounts_to_process = []
                
                # 更新账号登录失败计数
                for account in self.accounts:
                    if account.name not in self.login_failure_counts:
                        self.login_failure_counts[account.name] = 0
                for account in self.accounts:
                    # 检查是否已达到今天的上传数量
                    if not hasattr(account, 'today_uploads'):
                        account.today_uploads = 0
                    
                    # 计算剩余上传数量
                    remaining_uploads = account.daily_upload_count - account.today_uploads
                    if remaining_uploads <= 0:
                        log_status(f"账号 {account.name} 今日已达到上传上限（{account.daily_upload_count}个），跳过处理")
                        continue
                    
                    # 计算设定的上传时间
                    try:
                        upload_hour = int(account.upload_hour)
                        upload_minute = int(account.upload_minute)
                        
                        # 为每个账号生成一个随机偏移时间（-10到+10分钟）
                        if not hasattr(account, 'time_offset_minutes'):
                            # 每天重新生成一个随机偏移
                            if not hasattr(account, 'last_offset_date') or account.last_offset_date != current_time.date():
                                account.time_offset_minutes = random.randint(-10, 10)
                                account.last_offset_date = current_time.date()
                                log_status(f"账号 {account.name} 今日随机偏移时间: {account.time_offset_minutes:+d} 分钟")
                        
                        # 创建基础定时点（不带偏移）
                        base_time = current_time.replace(
                            hour=upload_hour,
                            minute=upload_minute,
                            second=0,
                            microsecond=0
                        )
                        
                        # 应用随机偏移到计划时间
                        scheduled_time = base_time + timedelta(minutes=account.time_offset_minutes)
                        
                        # 格式化显示时间
                        original_time = f"{upload_hour:02d}:{upload_minute:02d}"
                        adjusted_time = scheduled_time.strftime("%H:%M")
                        
                        if account.use_scheduled_time:
                            # 构建今天的定时点
                            today_scheduled_time = current_time.replace(
                                hour=upload_hour,
                                minute=upload_minute,
                                second=0,
                                microsecond=0
                            ) + timedelta(minutes=account.time_offset_minutes)
                            
                            # 构建明天的定时点
                            tomorrow_scheduled_time = today_scheduled_time + timedelta(days=1)
                            
                            # 比较完整的程序启动时间与定时点（不仅仅是时间部分）
                            if self.program_start_time < today_scheduled_time:
                                # 当前时间是否已经超过定时点
                                if current_time < today_scheduled_time:
                                    log_status(f"账号 {account.name} 的定时点 {original_time} (调整为 {adjusted_time}) 还没到，等待定时点")
                                    continue
                                else:
                                    # 程序启动时间早于定时点，当前时间已过定时点，允许执行
                                    log_status(f"账号 {account.name} 的定时点 {original_time} (调整为 {adjusted_time}) 已到，允许执行")
                                    accounts_to_process.append(account)
                                    continue
                            else:
                                # 程序启动时间晚于或等于定时点，明天发布
                                log_status(f"账号 {account.name} 的定时点 {original_time} (调整为 {adjusted_time}) 启动时已过，计划明天 {adjusted_time} 发布")
                                continue
                    except ValueError as e:
                        log_status(f"账号 {account.name} 的发布时间设置无效: {str(e)}")
                        continue
                    
                    # 只有非定时发布的账号才会被添加到处理列表
                    if not account.use_scheduled_time:
                        accounts_to_process.append(account)
                
                # 如果没有可以立即处理的账号，检查是否有其他未达上限的账号
                if not accounts_to_process:
                    # 首先检查是否有未达上限的非定时账号，强制添加到处理队列
                    has_available_accounts = False
                    for account in self.accounts:
                        if account.use_scheduled_time:
                            continue  # 跳过定时账号
                            
                        if not hasattr(account, 'today_uploads'):
                            account.today_uploads = 0
                            
                        remaining_uploads = account.daily_upload_count - account.today_uploads
                        if remaining_uploads > 0:
                            log_status(f"找到未达上限的非定时账号: {account.name}，直接添加到处理队列")
                            accounts_to_process.append(account)
                            has_available_accounts = True
                            break
                            
                    # 如果没有找到可用的非定时账号，查找定时发布账号
                    if not has_available_accounts:
                        for account in self.accounts:
                            if not account.use_scheduled_time:
                                continue  # 跳过非定时账号
                                
                            if not hasattr(account, 'today_uploads'):
                                account.today_uploads = 0
                                
                            remaining_uploads = account.daily_upload_count - account.today_uploads
                            if remaining_uploads > 0:
                                # 避免重复输出相同账号的日志
                                current_time = datetime.now()
                                account_key = f"{account.name}_{current_time.strftime('%Y%m%d')}"
                                if not hasattr(self, 'logged_accounts'):
                                    self.logged_accounts = {}
                                
                                # 如果这个账号在最近5分钟内没有被记录过，才输出日志
                                if account_key not in self.logged_accounts or (current_time - self.logged_accounts[account_key]).total_seconds() > 300:
                                    log_status(f"找到未达上限的定时账号: {account.name}，添加到处理队列")
                                    self.logged_accounts[account_key] = current_time
                                
                                accounts_to_process.append(account)
                                has_available_accounts = True
                                break
                    
                    # 如果找到了可用账号，直接继续循环处理
                    if has_available_accounts:
                        continue
                    
                    # 找出最近的一个定时发布时间或下一次重置时间
                    next_times = []
                    for account in self.accounts:
                        # 添加下一次重置时间
                        next_reset_time = account.last_reset_time + timedelta(hours=12)
                        next_times.append((account.name, next_reset_time, "重置计数"))
                        
                        # 如果设置了定时发布且未达到上限，添加下一次发布时间
                        if account.use_scheduled_time and account.today_uploads < account.daily_upload_count:
                            # 应用随机偏移到计划时间
                            base_time = current_time.replace(
                                hour=account.upload_hour,
                                minute=account.upload_minute,
                                second=0,
                                microsecond=0
                            )
                            if not hasattr(account, 'time_offset_minutes') or not hasattr(account, 'last_offset_date') or account.last_offset_date != current_time.date():
                                account.time_offset_minutes = random.randint(-10, 10)
                                account.last_offset_date = current_time.date()
                                log_status(f"账号 {account.name} 今日随机偏移时间: {account.time_offset_minutes:+d} 分钟")
                            scheduled_time = base_time + timedelta(minutes=account.time_offset_minutes)
                            # 如果今天的时间已过，设置为明天的时间
                            if current_time > scheduled_time:
                                scheduled_time += timedelta(days=1)
                            
                            # 如果程序是在前一天启动的，但现在已经是新的一天，并且当前时间已经超过了设定的发布时间
                            # 那么我们应该立即处理这个账号
                            if self.program_start_date < current_date and current_time.time() >= datetime.time(account.upload_hour, account.upload_minute):
                                # 这种情况下应该立即处理，但这里只是计算下一次时间，所以不做特殊处理
                                pass
                            
                            next_times.append((account.name, scheduled_time, "定时发布"))
                    
                    if next_times:
                        # 按时间排序，找出最近的一个
                        next_times.sort(key=lambda x: x[1])
                        next_account, next_time, action_type = next_times[0]
                        wait_seconds = (next_time - current_time).total_seconds()
                        if action_type == "定时发布":
                            log_status(f"没有可以立即处理的账号，下一个定时发布是账号 {next_account}，将在 {next_time.strftime('%H:%M:%S')} 开始上传")
                        else:
                            log_status(f"没有可以立即处理的账号，账号 {next_account} 将在 {next_time.strftime('%H:%M:%S')} 重置计数")
                        # 等待一分钟后继续检查
                        time.sleep(60)
                        continue
                    else:
                        log_status("所有账号今天的任务都已完成")
                        break
                
                # 处理可以立即上传的账号
                while accounts_to_process and not self.stop_flag:
                    current_time = datetime.now()
                    account = accounts_to_process[0]
                    
                    # 在处理前再次检查上传限制
                    if not hasattr(account, 'today_uploads'):
                        account.today_uploads = 0
                    
                    # 检查是否已达到当天上传上限
                    remaining_uploads = account.daily_upload_count - account.today_uploads
                    log_status(f"\n开始处理账号: {account.name}")
                    log_status(f"今日已上传: {account.today_uploads}/{account.daily_upload_count} 个视频")
                    log_status(f"账号 {account.name}: 还需上传 {remaining_uploads} 个视频")
                    
                    # 如果已达到上传上限，直接跳过，不启动浏览器
                    if remaining_uploads <= 0:
                        log_status(f"账号 {account.name} 今日已达到上传上限（{account.daily_upload_count}个），跳过处理")
                        # 从处理队列中移除当前账号
                        if account in accounts_to_process:
                            accounts_to_process.remove(account)
                        continue
                    
                    # 完成当前账号的处理
                    try:
                        # 使用账号特定的视频文件夹
                        video_folder = account.video_folder or default_video_folder
                        
                        # 启动浏览器并上传视频
                        log_status(f"开始处理账号 {account.name} 的视频上传任务")
                        
                        # 切换到当前账号
                        self.current_account = account
                        
                        # 重置stop_flag，确保不会受到上一次上传的影响
                        self.stop_flag = False
                        
                        # 运行上传流程
                        result = self.run(video_folder, status_callback)
                        
                        # 检查是否需要切换账号（检测到上传限制）
                        if result == "SWITCH_ACCOUNT":
                            log_status(f"账号 {account.name} 已达上传限制，将切换到下一个账号")
                            # 更新账号的上传统计
                            if not hasattr(account, 'today_uploads') or not isinstance(account.today_uploads, int):
                                account.today_uploads = account.daily_upload_count
                            else:
                                account.today_uploads = account.daily_upload_count
                            # 从处理队列中移除当前账号
                            if account in accounts_to_process:
                                accounts_to_process.remove(account)
                                
                                                    # 将当前账号从处理队列中移除
                            if account in accounts_to_process:
                                accounts_to_process.remove(account)
                            
                            # 保存配置
                            self.config.save_accounts_config(self.accounts)
                            
                            # 关键修复：完成当前账号处理后，立即跳出内部循环
                            # 回到外层while循环重新扫描所有账号状态
                            log_status("账号达到上传限制，立即重新扫描所有账号状态...")
                            break  # 跳出内部循环，返回外层while循环重新检查所有账号
                            
                            # 保存配置
                            self.config.save_accounts_config(self.accounts)
                            continue
                        
                        # 更新账号的上传统计
                        if not hasattr(account, 'upload_count'):
                            account.upload_count = 0
                        if not hasattr(account, 'success_count'):
                            account.success_count = 0
                        if not hasattr(account, 'today_uploads'):
                            account.today_uploads = 0
                        
                        # 更新上传次数
                        account.upload_count += 1
                        account.success_count += 1
                        # 不在这里增加today_uploads，因为已经在upload_video方法中增加了
                        
                        # 检查是否达到当天上传上限
                        if account.today_uploads >= account.daily_upload_count:
                            log_status(f"账号 {account.name} 已达到今日上传上限（{account.daily_upload_count}个），将停止上传")
                            raise AccountLimitReachedException("今日上传次数已达上限，需停止上传")
                        
                        log_status(f"账号 {account.name} 的视频上传任务已完成")
                        
                        # 从处理队列中移除当前账号
                        if account in accounts_to_process:
                            accounts_to_process.remove(account)
                            
                        # 检查是否还有其他非定时账号需要处理
                        found_next_account = False
                        
                        # 首先检查非定时账号
                        for next_account in self.accounts:
                            # 跳过当前账号
                            if next_account == account:
                                continue
                                
                            # 确保today_uploads字段存在
                            if not hasattr(next_account, 'today_uploads'):
                                next_account.today_uploads = 0
                            
                            remaining = next_account.daily_upload_count - next_account.today_uploads
                            if remaining > 0 and not next_account.use_scheduled_time:  # 优先添加非定时账号
                                log_status(f"找到下一个可立即处理的非定时账号: {next_account.name}")
                                accounts_to_process.append(next_account)
                                found_next_account = True
                                break
                        
                        # 如果没有找到非定时账号，检查定时账号
                        if not found_next_account:
                            current_time = datetime.now()
                            
                            # 创建所有待处理定时账号的列表用于排序
                            scheduled_accounts = []
                            
                            for next_account in self.accounts:
                                # 跳过当前账号
                                if next_account == account:
                                    continue
                                    
                                # 确保today_uploads字段存在
                                if not hasattr(next_account, 'today_uploads'):
                                    next_account.today_uploads = 0
                                
                                remaining = next_account.daily_upload_count - next_account.today_uploads
                                if remaining > 0 and next_account.use_scheduled_time:
                                    # 检查定时账号是否可以立即处理
                                    try:
                                        upload_hour = int(next_account.upload_hour)
                                        upload_minute = int(next_account.upload_minute)
                                        
                                        # 确保time_offset_minutes存在
                                        if not hasattr(next_account, 'time_offset_minutes'):
                                            next_account.time_offset_minutes = random.randint(-10, 10)
                                            next_account.last_offset_date = current_time.date()
                                        
                                        # 计算今天的定时点（带随机偏移）
                                        today_scheduled_time = current_time.replace(
                                            hour=upload_hour,
                                            minute=upload_minute,
                                            second=0,
                                            microsecond=0
                                        ) + timedelta(minutes=next_account.time_offset_minutes)
                                        
                                        # 根据程序启动时间和当前时间判断是否应处理或等待明天
                                        if current_time > today_scheduled_time:
                                            # 当前时间已超过今天的定时点
                                            if self.program_start_time < today_scheduled_time:
                                                # 程序启动早于定时点，但当前已过定时点，今天应该执行
                                                scheduled_time = today_scheduled_time  # 立即处理
                                                wait_minutes = 0
                                                log_status(f"找到可用定时账号: {next_account.name}，定时点已到，还可上传 {remaining} 个视频")
                                                accounts_to_process.append(next_account)
                                                found_next_account = True
                                                break
                                            else:
                                                # 程序启动晚于定时点，明天执行
                                                scheduled_time = today_scheduled_time + timedelta(days=1)
                                        else:
                                            # 当前时间尚未达到今天的定时点
                                            scheduled_time = today_scheduled_time
                                        
                                        # 计算等待时间（分钟）
                                        wait_minutes = int((scheduled_time - current_time).total_seconds() / 60)
                                        
                                        # 添加到排序列表中
                                        scheduled_accounts.append((next_account, scheduled_time, wait_minutes))
                                        
                                    except ValueError:
                                        continue
                            
                            # 如果没有立即找到可用的账号，但有定时账号，按时间排序
                            if not found_next_account and scheduled_accounts:
                                # 按等待时间从小到大排序
                                scheduled_accounts.sort(key=lambda x: x[2])
                                
                                # 输出排序后的账号列表，方便调试
                                log_status("所有待处理定时账号排序:")
                                for idx, (acc, time, wait_mins) in enumerate(scheduled_accounts, 1):
                                    log_status(f"{idx}. 账号 {acc.name} 定时点 {time.strftime('%H:%M')} 还需等待 {wait_mins} 分钟")
                                
                                # 选择等待时间最短的账号作为下一个处理账号
                                next_account, next_time, wait_minutes = scheduled_accounts[0]
                                log_status(f"下一个待处理账号: {next_account.name}，将在 {next_time.strftime('%H:%M')} 开始上传，还需等待 {wait_minutes} 分钟")
                    except AccountLimitReachedException as e:
                        log_status(f"账号 {account.name} {str(e)}，将切换到下一个账号")
                        # 更新账号的上传统计
                        if self.current_account:
                            self.current_account.today_uploads = self.current_account.daily_upload_count
                        # 从处理队列中移除当前账号
                        if account in accounts_to_process:
                            accounts_to_process.remove(account)
                            
                                                    # 重新检查是否有其他可用账号
                            log_status("正在检查其他可用账号...")
                            found_next = False
                            
                            # 首先检查非定时账号
                            for next_account in self.accounts:
                                # 跳过当前账号
                                if next_account == account:
                                    continue
                                    
                                if not hasattr(next_account, 'today_uploads'):
                                    next_account.today_uploads = 0
                                
                                remaining = next_account.daily_upload_count - next_account.today_uploads
                                
                                # 优先处理非定时账号
                                if remaining > 0 and not next_account.use_scheduled_time:
                                    log_status(f"找到可用非定时账号: {next_account.name}，还可上传 {remaining} 个视频")
                                    accounts_to_process.append(next_account)
                                    # 重置停止标志，确保继续处理
                                    self.stop_flag = False
                                    found_next = True
                                    break
                                    
                            # 如果没有找到非定时账号，再检查定时账号
                            if not found_next:
                                current_time = datetime.now()
                                
                                # 创建所有待处理定时账号的列表用于排序
                                scheduled_accounts = []
                                
                                for next_account in self.accounts:
                                    # 跳过当前账号
                                    if next_account == account:
                                        continue
                                        
                                    if not hasattr(next_account, 'today_uploads'):
                                        next_account.today_uploads = 0
                                    
                                    remaining = next_account.daily_upload_count - next_account.today_uploads
                                    if remaining > 0 and next_account.use_scheduled_time:
                                        # 检查定时账号是否可以立即处理
                                        try:
                                            upload_hour = int(next_account.upload_hour)
                                            upload_minute = int(next_account.upload_minute)
                                            
                                            # 确保time_offset_minutes存在
                                            if not hasattr(next_account, 'time_offset_minutes'):
                                                next_account.time_offset_minutes = random.randint(-10, 10)
                                                next_account.last_offset_date = current_time.date()
                                            
                                            # 计算今天的定时点（带随机偏移）
                                            today_scheduled_time = current_time.replace(
                                                hour=upload_hour,
                                                minute=upload_minute,
                                                second=0,
                                                microsecond=0
                                            ) + timedelta(minutes=next_account.time_offset_minutes)
                                            
                                            # 根据程序启动时间和当前时间判断是否应处理或等待明天
                                            if current_time > today_scheduled_time:
                                                # 当前时间已超过今天的定时点
                                                if self.program_start_time < today_scheduled_time:
                                                    # 程序启动早于定时点，但当前已过定时点，今天应该执行
                                                    scheduled_time = today_scheduled_time  # 立即处理
                                                    wait_minutes = 0
                                                    log_status(f"找到可用定时账号: {next_account.name}，程序启动时间早于定时点，允许执行，还可上传 {remaining} 个视频")
                                                    accounts_to_process.append(next_account)
                                                    # 重置停止标志，确保继续处理
                                                    self.stop_flag = False
                                                    found_next = True
                                                    break
                                                else:
                                                    # 程序启动晚于定时点，明天执行
                                                    scheduled_time = today_scheduled_time + timedelta(days=1)
                                            else:
                                                # 当前时间尚未达到今天的定时点
                                                scheduled_time = today_scheduled_time
                                            
                                            # 计算等待时间（分钟）
                                            wait_minutes = int((scheduled_time - current_time).total_seconds() / 60)
                                            log_status(f"账号 {next_account.name} 定时点还未到，还需等待 {wait_minutes} 分钟")
                                            
                                            # 添加到排序列表中
                                            scheduled_accounts.append((next_account, scheduled_time, wait_minutes))
                                            
                                        except ValueError:
                                            continue
                                
                                # 如果没有立即找到可用的账号，但有定时账号，按时间排序
                                if not found_next and scheduled_accounts:
                                    # 按等待时间从小到大排序
                                    scheduled_accounts.sort(key=lambda x: x[2])
                                    
                                    # 输出排序后的账号列表，方便调试
                                    log_status("所有待处理定时账号排序:")
                                    for idx, (acc, time, wait_mins) in enumerate(scheduled_accounts, 1):
                                        log_status(f"{idx}. 账号 {acc.name} 定时点 {time.strftime('%H:%M')} 还需等待 {wait_mins} 分钟")
                                    
                                    # 如果最早的账号等待时间很短（小于5分钟），将其添加到处理队列
                                    earliest_account, earliest_time, wait_minutes = scheduled_accounts[0]
                                    if wait_minutes <= 5:  # 如果等待时间不超过5分钟
                                        log_status(f"找到最早的定时账号: {earliest_account.name}，等待时间只有 {wait_minutes} 分钟，将添加到处理队列")
                                        accounts_to_process.append(earliest_account)
                                        found_next = True
                    except Exception as e:
                        log_status(f"处理账号 {account.name} 时出错: {str(e)}")
                        
                        # 更新账号的上传统计
                        if not hasattr(account, 'upload_count'):
                            account.upload_count = 0
                        account.upload_count += 1
                        
                        # 从处理队列中移除当前账号
                        if account in accounts_to_process:
                            accounts_to_process.remove(account)
                            
                        # 从处理队列中移除当前账号
                        if account in accounts_to_process:
                            accounts_to_process.remove(account)
                        
                        # 保存配置
                        self.config.save_accounts_config(self.accounts)
                        
                        # 关键修复：发生错误后，立即跳出内部循环，回到外层循环重新检查所有账号
                        log_status("处理出错，立即重新扫描所有账号状态...")
                        break  # 跳出内部循环，返回外层while循环重新检查所有账号
                    
                    # 保存配置
                    self.config.save_accounts_config(self.accounts)
                    
                    # 关键修复：完成当前账号处理后，立即跳出内部循环
                    # 回到外层while循环重新扫描所有账号状态
                    # 这确保其他定时账号在前一个账号处理完后立即被重新评估
                    log_status("当前账号处理完成，立即重新扫描所有账号状态...")
                    break  # 跳出内部的while accounts_to_process循环
                
                # 内层处理循环结束后，继续外层循环，不等待
                continue
                
                # 保存配置
                self.config.save_accounts_config(self.accounts)
                
                # 检查是否所有账号今天都已完成上传
                all_completed = True
                for account in self.accounts:
                    if not hasattr(account, 'today_uploads'):
                        account.today_uploads = 0
                    if account.today_uploads < account.daily_upload_count:
                        all_completed = False
                        break
                
                if all_completed:
                    log_status("所有账号今天的视频都已上传完成")
                    # 等待到明天的第一个上传时间
                    earliest_hour = min(acc.upload_hour for acc in self.accounts)
                    earliest_minute = min(acc.upload_minute for acc in self.accounts 
                                       if acc.upload_hour == earliest_hour)
                    tomorrow = current_time.replace(
                        day=current_time.day + 1,
                        hour=earliest_hour,
                        minute=earliest_minute,
                        second=0,
                        microsecond=0
                    )
                    if tomorrow.day == 1:  # 处理月末的情况
                        tomorrow = current_time + timedelta(days=1)
                        tomorrow = tomorrow.replace(hour=earliest_hour, minute=earliest_minute,
                                                 second=0, microsecond=0)
                    
                    wait_seconds = (tomorrow - current_time).total_seconds()
                    log_status(f"等待到明天 {earliest_hour:02d}:{earliest_minute:02d} 继续上传")
                    # 每隔一分钟检查一次是否需要停止
                    while wait_seconds > 0 and not self.stop_flag:
                        time.sleep(min(60, wait_seconds))
                        wait_seconds -= 60
                        if wait_seconds > 0:
                            hours = int(wait_seconds // 3600)
                            minutes = int((wait_seconds % 3600) // 60)
                            log_status(f"距离明天上传还有 {hours} 小时 {minutes} 分钟")
                else:
                    # 如果还有账号未上传完成，立即重新检查所有账号状态
                    log_status("重新检查所有账号状态...")
                    # 不再等待，立即进入下一轮循环检查账号状态
                    continue
            
            # 检查是否是用户手动停止还是自然结束
            if self.stop_flag and hasattr(self, 'user_stopped') and self.user_stopped:
                log_status("上传任务已被用户停止")
            elif self.stop_flag:
                # 在结束前再检查一次是否有可用的非定时账号
                has_available_non_scheduled = False
                for account in self.accounts:
                    if not hasattr(account, 'today_uploads'):
                        account.today_uploads = 0
                    
                    remaining = account.daily_upload_count - account.today_uploads
                    if remaining > 0 and not account.use_scheduled_time:
                        has_available_non_scheduled = True
                        log_status(f"检测到可用的非定时账号: {account.name}，还可上传 {remaining} 个视频")
                        accounts_to_process.append(account)
                        # 重置停止标志，继续处理
                        self.stop_flag = False
                        break
                
                if not has_available_non_scheduled:
                    log_status("当前上传周期已完成，等待下一个处理时间")
            
        except Exception as e:
            log_status(f"发生错误: {str(e)}")
        finally:
            self.is_running = False
            if self.driver:
                try:
                    self.driver.quit()
                except Exception as e:
                    print(f"关闭浏览器时出错: {str(e)}")
                self.driver = None

    def detect_chrome(self):
        """检测Chrome浏览器路径和版本"""
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser("~") + r"\AppData\Local\Google\Chrome\Application\chrome.exe"
        ]
        
        for path in chrome_paths:
            if os.path.exists(path):
                self.chrome_path = path
                try:
                    # 使用更可靠的方式获取Chrome版本
                    try:
                        # 首先尝试使用wmic命令
                        path_escaped = path.replace("\\", "\\\\")
                        cmd = f'wmic datafile where name="{path_escaped}" get Version /value'
                        output = subprocess.check_output(cmd, shell=True, timeout=5)
                        output_str = output.decode('utf-8').strip()
                        if "Version=" in output_str:
                            self.chrome_version = output_str.split("=")[1]
                        else:
                            raise Exception("未找到版本信息")
                    except:
                        # 如果wmic失败，尝试使用reg命令
                        try:
                            cmd = 'reg query "HKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon" /v version'
                            output = subprocess.check_output(cmd, shell=True, timeout=5)
                            output_str = output.decode('utf-8').strip()
                            version_line = [line for line in output_str.split('\n') if "version" in line.lower()][0]
                            self.chrome_version = version_line.split()[-1]
                        except:
                            # 如果reg也失败，尝试直接运行Chrome获取版本
                            try:
                                cmd = f'"{path}" --version'
                                output = subprocess.check_output(cmd, shell=True, timeout=5)
                                output_str = output.decode('utf-8').strip()
                                self.chrome_version = output_str.replace('Google Chrome ', '')
                            except:
                                self.chrome_version = "未知版本"
                    
                    print(f"检测到Chrome版本: {self.chrome_version}")
                    return True
                    
                except Exception as e:
                    print(f"获取Chrome版本时出错: {str(e)}")
                    self.chrome_version = "未知版本"
                    return True
        
        print("未找到Chrome浏览器安装路径")
        return False

    def prepare_chrome_options(self):
        """预先准备Chrome选项"""
        options = webdriver.ChromeOptions()
        
        # 核心性能优化
        options.add_argument('--disable-gpu')  # 禁用GPU加速
        options.add_argument('--no-sandbox')  # 禁用沙箱模式
        options.add_argument('--disable-dev-shm-usage')  # 禁用/dev/shm使用
        
        # 内存优化
        options.add_argument('--disable-extensions')  # 禁用扩展
        options.add_argument('--disable-popup-blocking')  # 禁用弹窗拦截
        options.add_argument('--disable-notifications')  # 禁用通知
        
        # 启动优化
        options.add_argument('--no-first-run')  # 跳过首次运行检查
        options.add_argument('--no-default-browser-check')  # 跳过默认浏览器检查
        options.add_argument('--disable-background-networking')  # 禁用后台网络
        options.add_argument('--disable-sync')  # 禁用同步
        
        # 清除缓存相关选项
        options.add_argument('--disable-application-cache')  # 禁用应用缓存
        options.add_argument('--disable-cache')  # 禁用页面缓存
        options.add_argument('--disable-offline-load-stale-cache')  # 禁用离线缓存
        options.add_argument('--disk-cache-size=1')  # 将磁盘缓存大小设为最小
        options.add_argument('--media-cache-size=1')  # 将媒体缓存大小设为最小
        options.add_argument('--incognito')  # 使用隐身模式，不保存浏览记录
        
        # 缓存目录设置
        cache_dir = os.path.join(self.config.cache_dir, 'cache')
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
        options.add_argument(f'--disk-cache-dir={cache_dir}')
        
        # 使用快速页面加载策略
        options.page_load_strategy = 'eager'
        
        return options

    def prepare_chromedriver(self):
        """检查本地chromedriver"""
        try:
            if not self.chrome_path:
                print("未检测到Chrome，无法准备chromedriver")
                return False
                
            # 设置chromedriver路径
            driver_dir = os.path.join(self.config.cache_dir, "chromedriver")
            os.makedirs(driver_dir, exist_ok=True)
            driver_path = os.path.join(driver_dir, "chromedriver.exe")
            
            # 获取Chrome版本
            chrome_version = None
            if self.chrome_version and self.chrome_version != "未知版本":
                version_match = re.search(r'(\d+)', self.chrome_version)
                if version_match:
                    chrome_version = version_match.group(1)
                    print(f"检测到Chrome版本: {chrome_version}")
            
            # 如果已有chromedriver，检查版本
            if os.path.exists(driver_path):
                try:
                    # 尝试运行chromedriver获取版本
                    result = subprocess.run([driver_path, '--version'], 
                                         stdout=subprocess.PIPE, 
                                         stderr=subprocess.PIPE, 
                                         timeout=3,
                                         creationflags=subprocess.CREATE_NO_WINDOW)
                    output = result.stdout.decode()
                    if chrome_version and str(chrome_version) not in output:
                        print(f"警告：现有chromedriver版本可能不匹配")
                    else:
                        print(f"现有chromedriver版本匹配")
                    return True
                except Exception as e:
                    print(f"检查chromedriver版本时出错: {str(e)}")
                    return False
            
            # 尝试在常见位置查找chromedriver
            possible_paths = [
                os.path.join(os.path.dirname(self.chrome_path), "chromedriver.exe"),
                os.path.join(os.environ.get('LOCALAPPDATA', ''), "chromedriver.exe"),
                os.path.join(os.environ.get('PROGRAMFILES', ''), "chromedriver.exe"),
                os.path.join(os.environ.get('PROGRAMFILES(X86)', ''), "chromedriver.exe")
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    try:
                        # 检查版本是否匹配
                        result = subprocess.run([path, '--version'], 
                                             stdout=subprocess.PIPE, 
                                             stderr=subprocess.PIPE, 
                                             timeout=3,
                                             creationflags=subprocess.CREATE_NO_WINDOW)
                        output = result.stdout.decode()
                        if chrome_version and str(chrome_version) in output:
                            print(f"找到匹配的系统chromedriver: {path}")
                            import shutil
                            shutil.copy(path, driver_path)
                            print(f"已复制chromedriver到: {driver_path}")
                            return True
                    except Exception as e:
                        print(f"检查系统chromedriver {path} 时出错: {str(e)}")
                        continue
            
            print("未找到匹配的chromedriver，请手动下载并放置到以下路径：")
            print(f"路径: {driver_path}")
            return False
            
        except Exception as e:
            print(f"准备chromedriver时出错: {str(e)}")
            return False

    def download_chromedriver_local(self, chrome_version, driver_path):
        """已移除自动下载功能"""
        return False

    def use_bundled_chromedriver(self, driver_path):
        """已移除预编译chromedriver功能"""
        return False

    def setup_browser(self):
        """设置并启动Chrome浏览器"""
        if not self.chrome_path:
            raise Exception("未找到Chrome浏览器，请确保已安装Chrome")
        
        # 使用status_callback记录状态
        if self.status_callback:
            self.status_callback("正在启动Chrome浏览器...")
        else:
            print("正在启动Chrome浏览器...")
        
        # 首先清理所有Chrome相关进程
        self.cleanup_chrome_processes()
        time.sleep(2)  # 等待进程完全清理
        
        # 最多尝试3次启动
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 直接使用undetected_chromedriver启动
                if uc and self.try_undetected_start():
                    msg = "使用undetected_chromedriver启动成功!"
                    print(msg)
                    if self.status_callback:
                        self.status_callback(msg)
                    return True
                
                # 如果undetected_chromedriver失败，等待一会再重试
                if attempt < max_retries - 1:
                    print(f"第{attempt + 1}次启动失败，等待5秒后重试...")
                    if self.status_callback:
                        self.status_callback(f"第{attempt + 1}次启动失败，等待5秒后重试...")
                    time.sleep(5)
                    continue
            except Exception as e:
                print(f"第{attempt + 1}次启动出错: {str(e)}")
                if self.status_callback:
                    self.status_callback(f"第{attempt + 1}次启动出错: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(5)
                    continue
            
        # 如果所有尝试都失败，抛出异常
        error_msg = "浏览器启动失败，请检查Chrome和chromedriver版本是否匹配"
        if self.status_callback:
            self.status_callback(f"错误: {error_msg}")
        raise Exception(error_msg)

    def try_selenium_start(self):
        """尝试使用原生selenium方式启动"""
        try:
            print("尝试使用原生selenium方式启动...")
            
            # 创建用户数据目录
            user_data_dir = os.path.join(self.config.cache_dir, "chrome_user_data")
            os.makedirs(user_data_dir, exist_ok=True)
            
            # 创建Chrome选项
            options = webdriver.ChromeOptions()
            options.binary_location = self.chrome_path
            options.add_argument(f"--user-data-dir={user_data_dir}")
            options.add_argument("--no-first-run")
            options.add_argument("--no-default-browser-check")
            options.add_argument("--disable-extensions")
            options.add_argument("--disable-gpu")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            
            # 添加清除缓存相关选项
            options.add_argument('--disable-application-cache')
            options.add_argument('--disable-cache')
            options.add_argument('--disable-offline-load-stale-cache')
            options.add_argument('--disk-cache-size=1')
            options.add_argument('--media-cache-size=1')
            options.add_argument('--incognito')
            
            # 如果当前账号已设置，应用账号特定的设备指纹设置
            if self.current_account:
                print(f"正在应用账号 {self.current_account.name} 的设备指纹设置...")
                
                # 设置User-Agent
                if self.current_account.user_agent:
                    options.add_argument(f"--user-agent={self.current_account.user_agent}")
                    print(f"设置User-Agent: {self.current_account.user_agent}")
                
                # 设置屏幕分辨率
                if self.current_account.screen_resolution:
                    width, height = map(int, self.current_account.screen_resolution.split('x'))
                    print(f"设置屏幕分辨率: {width}x{height}")
            
            # 设置chromedriver路径
            driver_path = os.path.join(self.config.cache_dir, "chromedriver", "chromedriver.exe")
            
            # 创建Service对象
            service = webdriver.ChromeService(executable_path=driver_path)
            service.creation_flags = subprocess.CREATE_NO_WINDOW
            
            # 启动浏览器
            self.driver = webdriver.Chrome(service=service, options=options)
            
            # 配置浏览器
            self.driver.set_page_load_timeout(20)
            self.driver.set_script_timeout(20)
            
            # 如果当前账号已设置，应用屏幕分辨率
            if self.current_account and self.current_account.screen_resolution:
                width, height = map(int, self.current_account.screen_resolution.split('x'))
                self.driver.set_window_size(width, height)
            else:
                self.driver.set_window_size(1920, 1080)
            
            # 如果当前账号已设置，注入JavaScript来修改浏览器指纹
            if self.current_account:
                self.inject_fingerprint_scripts(self.status_callback)  # 传递status_callback
            else:
                # 如果没有设置账号，仍然需要清理缓存
                self.driver.execute_cdp_cmd('Network.clearBrowserCache', {})
                self.driver.execute_cdp_cmd('Network.clearBrowserCookies', {})
            
            print("原生selenium方式启动成功！")
            return True
            
        except Exception as e:
            print(f"原生selenium方式启动失败: {str(e)}")
            # 如果driver已创建但出错，确保关闭
            if hasattr(self, 'driver') and self.driver:
                try:
                    self.driver.quit()
                    self.driver = None
                except:
                    pass
            return False
            
    def inject_fingerprint_scripts(self, status_callback=None):
        """注入修改浏览器指纹的JavaScript脚本"""
        if not self.driver or not self.current_account:
            return
            
        # 设置状态回调
        self.status_callback = status_callback
            
        # 定义本地日志函数
        def log_status(message):
            print(message)
            if self.status_callback:
                self.status_callback(message)
            
        try:
            log_status("正在注入设备指纹脚本...")
            
            # 清除所有缓存数据
            self.driver.execute_cdp_cmd('Network.clearBrowserCache', {})
            self.driver.execute_cdp_cmd('Network.clearBrowserCookies', {})
            log_status("✅ 浏览器缓存清理成功")
            
            # 显示即将修改的指纹值
            log_status("\n即将设置的指纹值：")
            log_status(f"User-Agent: {self.current_account.user_agent}")
            log_status(f"屏幕分辨率: {self.current_account.screen_resolution}")
            log_status(f"硬件并发数: {self.current_account.hardware_concurrency}")
            log_status(f"设备内存: {self.current_account.device_memory}GB")
            log_status(f"WebGL厂商: {self.current_account.webgl_vendor}")
            log_status(f"WebGL渲染器: {self.current_account.webgl_renderer}")
            log_status(f"Canvas指纹: {self.current_account.canvas_fingerprint}")
            
            # 构建指纹修改脚本
            script = """
            // 修改硬件并发数
            Object.defineProperty(navigator, 'hardwareConcurrency', {
                get: () => %d
            });
            
            // 修改设备内存
            Object.defineProperty(navigator, 'deviceMemory', {
                get: () => %d
            });
            
            // 修改WebGL指纹
            const getParameterProxyHandler = {
                apply: function(target, thisArg, args) {
                    const param = args[0];
                    if (param === 37445) { // UNMASKED_VENDOR_WEBGL
                        return "%s";
                    }
                    if (param === 37446) { // UNMASKED_RENDERER_WEBGL
                        return "%s";
                    }
                    return Reflect.apply(target, thisArg, args);
                }
            };
            
            // 保存原始的getParameter方法
            const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
            
            // 替换getParameter方法
            WebGLRenderingContext.prototype.getParameter = new Proxy(originalGetParameter, getParameterProxyHandler);
            """ % (
                self.current_account.hardware_concurrency,
                self.current_account.device_memory,
                self.current_account.webgl_vendor,
                self.current_account.webgl_renderer
            )
            
            # 如果设置了Canvas指纹，添加Canvas指纹修改代码
            if self.current_account.canvas_fingerprint and self.current_account.canvas_fingerprint != "default":
                canvas_script = """
                // 修改Canvas指纹
                const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                HTMLCanvasElement.prototype.toDataURL = function(type) {
                    const result = originalToDataURL.apply(this, arguments);
                    if (result.length > 100) {
                        return "%s";
                    }
                    return result;
                }
                """ % self.current_account.canvas_fingerprint
                
                script += canvas_script
            
            # 执行脚本
            self.driver.execute_script(script)
            log_status("✅ 设备指纹脚本注入成功")
            
            # 获取并显示实际的浏览器指纹值
            log_status("\n当前浏览器实际指纹值：")
            
            # 获取User-Agent
            actual_ua = self.driver.execute_script("return navigator.userAgent;")
            log_status(f"User-Agent: {actual_ua}")
            
            # 获取屏幕分辨率
            actual_resolution = self.driver.execute_script("return window.screen.width + 'x' + window.screen.height;")
            log_status(f"屏幕分辨率: {actual_resolution}")
            
            # 获取硬件并发数
            actual_concurrency = self.driver.execute_script("return navigator.hardwareConcurrency;")
            log_status(f"硬件并发数: {actual_concurrency}")
            
            # 获取设备内存
            actual_memory = self.driver.execute_script("return navigator.deviceMemory;")
            log_status(f"设备内存: {actual_memory}GB")
            
            # 获取WebGL信息
            webgl_info = self.driver.execute_script("""
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl');
                const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                return {
                    vendor: gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL),
                    renderer: gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)
                };
            """)
            log_status(f"WebGL厂商: {webgl_info['vendor']}")
            log_status(f"WebGL渲染器: {webgl_info['renderer']}")
            
            # 获取Canvas指纹
            actual_canvas = self.driver.execute_script("""
                const canvas = document.createElement('canvas');
                canvas.width = 200;
                canvas.height = 200;
                const ctx = canvas.getContext('2d');
                ctx.textBaseline = "top";
                ctx.font = "14px 'Arial'";
                ctx.textBaseline = "alphabetic";
                ctx.fillStyle = "#f60";
                ctx.fillRect(125,1,62,20);
                ctx.fillStyle = "#069";
                ctx.fillText("abcdefghijklmnopqrstuvwxyz", 2, 15);
                ctx.fillStyle = "rgba(102, 204, 0, 0.7)";
                ctx.fillText("abcdefghijklmnopqrstuvwxyz", 4, 17);
                return canvas.toDataURL();
            """)
            log_status(f"Canvas指纹: {actual_canvas[:50]}...")  # 只显示前50个字符
            
        except Exception as e:
            log_status(f"❌ 注入设备指纹脚本失败: {str(e)}")
            print(f"注入设备指纹脚本失败: {str(e)}")
            
    def try_cdp_start(self):
        """尝试使用CDP方式启动Chrome"""
        try:
            print("尝试使用CDP方式启动Chrome...")
            
            # 先清理可能存在的Chrome进程
            self.cleanup_chrome_processes()
            
            # 创建用户数据目录
            user_data_dir = os.path.join(self.config.cache_dir, "chrome_user_data")
            os.makedirs(user_data_dir, exist_ok=True)
            
            # 准备启动参数
            chrome_cmd = [
                self.chrome_path,
                f"--user-data-dir={user_data_dir}",
                "--remote-debugging-port=0",  # 使用随机端口
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-extensions",
                "--disable-gpu",
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-application-cache",  # 禁用应用缓存
                "--disable-cache",  # 禁用页面缓存
                "--disable-offline-load-stale-cache",  # 禁用离线缓存
                "--disk-cache-size=1",  # 将磁盘缓存大小设为最小
                "--media-cache-size=1",  # 将媒体缓存大小设为最小
                "--incognito"  # 使用隐身模式
            ]
            
            # 如果当前账号已设置，应用账号特定的设备指纹设置
            if self.current_account:
                print(f"正在应用账号 {self.current_account.name} 的设备指纹设置...")
                
                # 设置User-Agent
                if self.current_account.user_agent:
                    chrome_cmd.append(f"--user-agent={self.current_account.user_agent}")
                    print(f"设置User-Agent: {self.current_account.user_agent}")
                
                # 设置屏幕分辨率
                if self.current_account.screen_resolution:
                    width, height = map(int, self.current_account.screen_resolution.split('x'))
                    chrome_cmd.append(f"--window-size={width},{height}")
                    print(f"设置屏幕分辨率: {width}x{height}")
            
            # 启动Chrome进程
            self.chrome_process = subprocess.Popen(
                chrome_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            # 等待Chrome启动并获取调试端口
            port = None
            start_time = time.time()
            while time.time() - start_time < 30:  # 最多等待30秒
                try:
                    if os.path.exists(user_data_dir):
                        for item in os.listdir(user_data_dir):
                            if item.startswith("DevToolsActivePort"):
                                port_file = os.path.join(user_data_dir, item)
                                with open(port_file, "r") as f:
                                    port = int(f.readline().strip())
                                break
                    if port:
                        break
                    time.sleep(0.1)
                except Exception as e:
                    print(f"等待Chrome启动时出错: {str(e)}")
                    time.sleep(0.1)
            
            if not port:
                raise Exception("无法获取Chrome调试端口")
            
            # 连接到Chrome
            chrome_options = webdriver.ChromeOptions()
            chrome_options.add_experimental_option("debuggerAddress", f"127.0.0.1:{port}")
            
            # 设置chromedriver路径
            driver_path = os.path.join(self.config.cache_dir, "chromedriver", "chromedriver.exe")
            service = webdriver.ChromeService(executable_path=driver_path)
            service.creation_flags = subprocess.CREATE_NO_WINDOW
            
            # 连接到Chrome
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            print("成功连接到Chrome！")
            
            # 配置浏览器
            self.driver.set_page_load_timeout(20)
            self.driver.set_script_timeout(20)
            
            # 如果当前账号已设置，注入JavaScript来修改浏览器指纹
            if self.current_account:
                self.inject_fingerprint_scripts(self.status_callback)
            
            return True
            
        except Exception as e:
            print(f"CDP方式启动失败: {str(e)}")
            # 清理进程
            self.cleanup_chrome_processes()
            return False

    def try_undetected_start(self):
        """尝试使用undetected_chromedriver启动"""
        if not uc:
            print("未安装undetected_chromedriver模块，无法使用此方法")
            return False
            
        try:
            print("尝试使用undetected_chromedriver启动...")
            
            # 创建用户数据目录
            user_data_dir = os.path.join(self.config.cache_dir, "chrome_user_data")
            if os.path.exists(user_data_dir):
                try:
                    shutil.rmtree(user_data_dir)
                    print(f"已清理旧的用户数据目录: {user_data_dir}")
                except Exception as e:
                    print(f"清理用户数据目录失败: {str(e)}")
            os.makedirs(user_data_dir, exist_ok=True)
            
            # 设置undetected_chromedriver的缓存目录
            uc.BASE_DIR = self.config.cache_dir
            
            # 创建选项
            options = uc.ChromeOptions()
            options.add_argument(f"--user-data-dir={user_data_dir}")
            options.add_argument("--disable-gpu")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-extensions")
            options.add_argument("--disable-automation")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.binary_location = self.chrome_path
            
            # 如果当前账号已设置，应用账号特定的设备指纹设置
            if self.current_account:
                print(f"正在应用账号 {self.current_account.name} 的设备指纹设置...")
                
                # 设置User-Agent
                if self.current_account.user_agent:
                    options.add_argument(f"--user-agent={self.current_account.user_agent}")
                    print(f"设置User-Agent: {self.current_account.user_agent}")
                
                # 设置屏幕分辨率
                window_size = "1920,1080"
                if self.current_account.screen_resolution:
                    width, height = map(int, self.current_account.screen_resolution.split('x'))
                    window_size = f"{width},{height}"
                    print(f"设置屏幕分辨率: {width}x{height}")
                options.add_argument(f"--window-size={window_size}")
            
            # 获取Chrome主版本号
            version_main = None
            if self.chrome_version and self.chrome_version != "未知版本":
                version_match = re.search(r'(\d+)', self.chrome_version)
                if version_match:
                    version_main = int(version_match.group(1))
                    print(f"检测到Chrome版本: {version_main}")
            
            # 确保chromedriver目录存在
            driver_dir = os.path.join(self.config.cache_dir, "chromedriver")
            os.makedirs(driver_dir, exist_ok=True)
            driver_path = os.path.join(driver_dir, "chromedriver.exe")
            
            print(f"使用以下配置启动Chrome:")
            print(f"- Chrome路径: {self.chrome_path}")
            print(f"- ChromeDriver路径: {driver_path}")
            print(f"- 用户数据目录: {user_data_dir}")
            print(f"- Chrome版本: {version_main}")
            
            # 启动Chrome
            self.driver = uc.Chrome(
                options=options,
                driver_executable_path=driver_path,
                browser_executable_path=self.chrome_path,
                version_main=version_main,
                headless=False,
                use_subprocess=True,
                service_creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            # 配置浏览器
            self.driver.set_page_load_timeout(20)
            self.driver.set_script_timeout(20)
            
            # 如果当前账号已设置，应用屏幕分辨率
            if self.current_account and self.current_account.screen_resolution:
                width, height = map(int, self.current_account.screen_resolution.split('x'))
                self.driver.set_window_size(width, height)
            else:
                self.driver.set_window_size(1920, 1080)
                
            # 如果当前账号已设置，注入JavaScript来修改浏览器指纹
            if self.current_account:
                self.inject_fingerprint_scripts(self.status_callback)
            
            print("undetected_chromedriver启动成功！")
            return True
            
        except Exception as e:
            error_msg = str(e)
            print(f"undetected_chromedriver启动失败: {error_msg}")
            
            # 记录更详细的错误信息
            if "chromedriver" in error_msg.lower():
                print("可能是ChromeDriver版本不匹配，请尝试重新下载对应版本的ChromeDriver")
            elif "chrome not reachable" in error_msg.lower():
                print("Chrome进程可能未正确启动或已崩溃")
            elif "cannot find chrome binary" in error_msg.lower():
                print("未找到Chrome浏览器可执行文件，请检查Chrome是否正确安装")
            elif "timed out" in error_msg.lower():
                print("浏览器启动超时，可能是系统资源不足或网络问题")
            
            # 如果driver已创建但出错，确保关闭
            if hasattr(self, 'driver') and self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
                self.driver = None
            
            return False

    def cleanup_chrome_processes(self):
        """清理Chrome相关进程"""
        try:
            # 使用taskkill命令快速结束进程
            cleanup_commands = [
                'taskkill /F /IM chrome.exe /T',
                'taskkill /F /IM chromedriver.exe /T'
            ]
            
            for cmd in cleanup_commands:
                try:
                    subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=3)
                except:
                    pass
            
            # 快速清理临时文件
            temp_patterns = [
                os.path.join(os.environ.get('TEMP', ''), 'chrome_automation*'),
                os.path.join(os.environ.get('TEMP', ''), 'scoped_dir*'),
                os.path.join(self.config.cache_dir, 'tmp*')
            ]
            
            for pattern in temp_patterns:
                try:
                    for path in glob.glob(pattern):
                        try:
                            if os.path.isfile(path):
                                os.remove(path)
                            elif os.path.isdir(path):
                                shutil.rmtree(path, ignore_errors=True)
                        except:
                            continue
                except:
                    continue
            
        except Exception as e:
            print(f"清理进程时出错: {str(e)}")
            pass  # 继续执行，不影响主流程

    def load_cookies(self):
        """加载保存的cookies"""
        try:
            print("正在加载cookies...")
            cookies_file = self.config.get_cookies_path(self.current_account.name)
            if os.path.exists(cookies_file):
                with open(cookies_file, 'r', encoding='utf-8') as f:
                    cookies = json.load(f)
                    print(f"从文件加载了 {len(cookies)} 个cookies")
                    
                    success_count = 0
                    for cookie in cookies:
                        try:
                            # 修复cookie格式问题
                            if 'expiry' in cookie:
                                cookie['expiry'] = int(cookie['expiry'])
                            self.driver.add_cookie(cookie)
                            success_count += 1
                        except Exception as e:
                            print(f"添加cookie时出错: {str(e)}")
                            continue
                    
                    print(f"成功添加了 {success_count}/{len(cookies)} 个cookies")
                return success_count > 0
            else:
                print(f"cookies文件不存在: {cookies_file}")
            return False
        except Exception as e:
            print(f"加载cookies时出错: {str(e)}")
            return False

    def save_cookies(self):
        """保存cookies"""
        try:
            print("正在保存cookies...")
            cookies = self.driver.get_cookies()
            print(f"获取到 {len(cookies)} 个cookies")
            
            # 确保cookies有效
            if not cookies or len(cookies) < 2:
                print("警告：获取到的cookies数量过少，可能无效")
            
            # 保存cookies到文件
            cookies_file = self.config.get_cookies_path(self.current_account.name)
            with open(cookies_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f)
            
            print(f"cookies已保存到文件: {cookies_file}")
            return True
        except Exception as e:
            print(f"保存cookies时出错: {str(e)}")
            return False

    def wait_for_login(self, status_callback=None):
        """等待用户扫码登录"""
        def log_status(message):
            """记录状态到控制台和状态回调"""
            print(message)
            if status_callback:
                status_callback(message)
                
        log_status("请使用快手APP扫描二维码登录...")
        log_status("您有10分钟时间完成扫码登录，请不要着急")
        
        # 增加等待时间至600秒（10分钟）
        max_wait_time = 600  # 10分钟
        check_interval = 5  # 每5秒检查一次
        waited_time = 0
        login_success = False
        
        try:
            # 循环检查登录状态，每5秒检查一次
            while waited_time < max_wait_time and not login_success:
                # 检查是否已登录成功
                try:
                    current_url = self.driver.current_url
                    page_source = self.driver.page_source
                    
                    # 打印当前URL和页面内容关键信息，辅助调试
                    print(f"当前URL: {current_url}")
                    print(f"页面是否包含'登录'关键词: {'是' if '登录' in page_source else '否'}")
                    print(f"页面是否包含'扫码'关键词: {'是' if '扫码' in page_source else '否'}")
                    
                    # 多种方式检测登录状态
                    login_indicators = [
                        "cp.kuaishou.com/profile" in current_url,
                        "快手创作服务平台" in page_source,
                        "发布作品" in page_source,
                        "数据中心" in page_source,
                        "内容管理" in page_source
                    ]
                    
                    print(f"登录状态指标: {login_indicators}")
                    
                    # 如果满足至少两个登录指标，认为已登录成功
                    if sum(login_indicators) >= 2:
                        log_status("检测到登录成功指标，正在确认...")
                        login_success = True
                        break
                    
                    # 额外检查页面元素
                    try:
                        # 检查页面中是否有典型的登录后才会出现的元素
                        elements = self.driver.find_elements(By.CSS_SELECTOR, "a[href], button, .btn")
                        element_texts = [elem.text for elem in elements if elem.text]
                        print(f"页面元素文本: {element_texts[:10]}...")  # 只显示前10个
                        
                        # 检查是否有登录后才会出现的文本
                        login_text_indicators = [
                            "发布" in element_texts,
                            "首页" in element_texts,
                            "数据" in element_texts,
                            "管理" in element_texts,
                            "上传" in element_texts
                        ]
                        
                        print(f"元素文本登录指标: {login_text_indicators}")
                        if sum(login_text_indicators) >= 2:
                            log_status("通过页面元素确认已登录")
                            login_success = True
                            break
                    except Exception as e:
                        print(f"检查元素时出错: {str(e)}")
                
                except Exception as e:
                    print(f"检查登录状态时出错: {str(e)}")
                
                # 计算剩余等待时间
                remaining_time = max_wait_time - waited_time
                minutes = remaining_time // 60
                seconds = remaining_time % 60
                
                # 每30秒提示一次剩余时间
                if waited_time % 30 == 0:
                    if minutes > 0:
                        log_status(f"请耐心等待，剩余时间: {minutes}分{seconds}秒")
                    else:
                        log_status(f"请耐心等待，剩余时间: {seconds}秒")
                
                time.sleep(check_interval)
                waited_time += check_interval
            
            # 最终检查登录状态
            if login_success:
                # 确认登录成功后，立即保存cookies
                log_status("检测到登录成功，正在保存登录信息...")
                self.save_cookies()
                log_status("登录信息已保存，下次可自动登录")
                # 重置登录失败计数
                if hasattr(self, 'login_failure_counts') and hasattr(self, 'current_account') and self.current_account:
                    if self.current_account.name in self.login_failure_counts:
                        self.login_failure_counts[self.current_account.name] = 0
                return True
            else:
                if waited_time >= max_wait_time:
                    log_status("登录等待超时，将尝试重新启动")
                else:
                    log_status("登录失败，请重试...")
                return False
                
        except Exception as e:
            log_status(f"登录过程中发生错误：{str(e)}")
            return False

    def set_cover_options(self, horizontal_cover_path, vertical_cover_path, use_custom_cover):
        """设置封面选项"""
        self.horizontal_cover_path = horizontal_cover_path
        self.vertical_cover_path = vertical_cover_path
        self.use_custom_cover = use_custom_cover

    def handle_cover_upload_process(self):
        if not self.cover_path or not os.path.exists(self.cover_path):
            print("未设置封面图片或图片路径不存在")
            return False
        
        print("正在设置自定义封面...")
            
        # 尝试检测界面是否已经打开
        cover_ui_opened = self.driver.execute_script("""
            // 检查是否存在宽高比选项 (4:3, 3:4, 1:1, 9:16)
            const ratioTexts = ['4:3', '3:4', '1:1', '9:16'];
            for (const ratio of ratioTexts) {
                const elements = document.querySelectorAll('div, span, p');
                for (const elem of elements) {
                    if (elem.textContent === ratio) {
                        console.log('找到宽高比选项: ' + ratio);
                        return {opened: true, type: '宽高比'};
                    }
                }
            }
            
            // 检查是否存在"封面截取"或"上传封面"选项
            const optionTexts = ['封面截取', '上传封面', '原始比例'];
            for (const text of optionTexts) {
                const elements = document.querySelectorAll('div, span, p, button');
                for (const elem of elements) {
                    if (elem.textContent.includes(text)) {
                        console.log('找到封面界面选项: ' + text);
                        return {opened: true, type: '封面选项'};
                    }
                }
            }
            
            return {opened: false};
        """)
        
        # 如果界面已打开，直接跳到上传逻辑
        if isinstance(cover_ui_opened, dict) and cover_ui_opened.get('opened'):
            print(f"检测到封面设置界面已打开 [{cover_ui_opened.get('type', '未知类型')}]")
            print("跳过点击封面设置按钮，直接处理上传...")
            # 点击上传封面按钮
            try:
                # 尝试查找并点击"上传封面"选项卡/按钮
                upload_tab_clicked = self.driver.execute_script("""
                    const elements = document.querySelectorAll('div, span, button, a');
                    for (const elem of elements) {
                        if (elem.textContent === '上传封面') {
                            elem.click();
                            return true;
                        }
                    }
                    return false;
                """)
                
                if upload_tab_clicked:
                    print("成功切换到「上传封面」选项卡")
                    time.sleep(1)
            except Exception as e:
                print(f"尝试切换到上传封面选项卡失败: {str(e)}")
        else:
            # 封面设置界面未打开，需要点击封面设置按钮
            try:
                # 点击封面设置按钮 - 精确版
                clicked = self.driver.execute_script("""
                    // 重点：优先精确匹配div元素而非span元素，避免点击错误的"封面设置"
                    function findExactCoverButton() {
                        // 方法1: 使用精确的类名匹配div
                        const specificButtons = document.querySelectorAll('div._cover-full-editor_1j1n1_40');
                        for (const btn of specificButtons) {
                            if (btn.textContent.includes('封面设置')) {
                                console.log('找到精确匹配的封面设置按钮(div元素)');
                                return btn;
                            }
                        }
                        
                        // 方法2: 使用更通用的div选择器但排除span
                        const divButtons = document.querySelectorAll('div[class*="cover-full-editor"], div[class*="_cover-full-editor_"], div[class*="cover-editor"]');
                        for (const btn of divButtons) {
                            // 确认是div元素而非内部的span
                            if (btn.tagName === 'DIV' && btn.textContent.includes('封面设置')) {
                                let containsSpan = false;
                                const spans = btn.querySelectorAll('span');
                                for (const span of spans) {
                                    if (span.textContent === '封面设置') {
                                        containsSpan = true;
                                        break;
                                    }
                                }
                                if (!containsSpan) {
                                    console.log('找到div封面设置按钮(通用类名)');
                                    return btn;
                                }
                            }
                        }
                        
                        return null;
                    }
                    
                    // 查找按钮
                    const coverButton = findExactCoverButton();
                    if (coverButton) {
                        try {
                            coverButton.click();
                            return true;
                        } catch (e) {
                            console.error('点击封面设置按钮失败:', e);
                            return false;
                        }
                    }
                    return false;
                """)
                
                if not clicked:
                    print("JavaScript方法未能找到封面设置按钮，尝试Selenium方法...")
                    
                    try:
                        # 只尝试精确定位div._cover-full-editor_1j1n1_40元素
                        print("尝试精确定位div._cover-full-editor_1j1n1_40元素")
                        selectors = [
                            # 优先使用提供的精确类名
                            'div._cover-full-editor_1j1n1_40'
                        ]
                        
                        cover_button = None
                        for selector in selectors:
                            try:
                                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                                for element in elements:
                                    if element.tag_name == 'div' and "封面设置" in element.text:
                                        cover_button = element
                                        print(f"找到div封面设置按钮: {selector}")
                                        break
                                
                                if cover_button:
                                    break
                                    
                            except Exception as e:
                                print(f"选择器 {selector} 失败: {str(e)}")
                        
                        # 尝试点击找到的按钮
                        if cover_button:

                            
                            # 尝试点击
                            try:
                                cover_button.click()
                                print("Selenium方法成功点击封面设置按钮")
                                clicked = True
                            except:
                                print("Selenium点击尝试失败")
                                return False
                        else:
                            print("无法找到封面设置按钮")
                            print("未能找到封面设置按钮")
                            return False
                        
                    except Exception as e:
                        print(f"Selenium尝试点击封面设置按钮失败: {str(e)}")
                        return False
                
                print("成功点击封面设置按钮")
                time.sleep(2)  # 等待新界面出现
                
                # 验证是否打开了封面上传界面
                try:
                    upload_panel_visible = self.driver.execute_script("""
                        // 检查是否存在上传封面的标题或界面
                        const headers = document.querySelectorAll('[class*="header-title"], [class*="title-item"], [class*="dialog-title"]');
                        for (const header of headers) {
                            if (header.textContent && (header.textContent.includes('上传封面') || header.textContent.includes('封面设置'))) {
                                return true;
                            }
                        }
                        
                        // 检查宽高比选项是否存在 (4:3, 3:4, 1:1, 9:16)
                        const ratioTexts = ['4:3', '3:4', '1:1', '9:16'];
                        for (const ratio of ratioTexts) {
                            const elements = document.querySelectorAll('div, span, p');
                            for (const elem of elements) {
                                if (elem.textContent === ratio) {
                                    console.log('找到宽高比选项: ' + ratio);
                                    return true;
                                }
                            }
                        }
                        
                        // 检查是否存在"封面截取"或"上传封面"选项
                        const optionTexts = ['封面截取', '上传封面', '原始比例'];
                        for (const text of optionTexts) {
                            const elements = document.querySelectorAll('div, span, p, button');
                            for (const elem of elements) {
                                if (elem.textContent.includes(text)) {
                                    console.log('找到封面界面选项: ' + text);
                                    return true;
                                }
                            }
                        }
                        
                        return false;
                    """)
                    
                    if not upload_panel_visible:
                        print("警告：点击后未检测到封面上传界面")

                        return False
                    else:
                        print("验证成功：封面上传界面已打开")
                except Exception as e:
                    print(f"验证封面上传界面时出错: {str(e)}")
                    # 即使验证出错，也继续尝试操作
                    
                time.sleep(2)  # 等待新界面出现
                
                # 尝试多种方式查找并点击上传封面按钮
                print("查找上传封面按钮...")
                
                # 基于截图分析，专门针对上传封面选项卡的精确定位
                print("使用精确的坐标和元素定位方式点击上传封面...")
                

                
                # 从截图分析，使用精确定位和坐标点击
                upload_clicked = self.driver.execute_script("""
                    function findAndClickUploadButton() {
                        // 从截图中获取的精确类名和结构
                        console.log("开始查找上传封面选项卡...");
                        
                        // 方法1: 使用截图中的精确div类名
                        const exactSelectors = [
                            'div._header-title-item_2t3fe_27',
                            'div.div_header-title-item_2t3fe_27',
                            'div._header-title-item-active_2t3fe_34',
                            'div[class*="_header-title-item_"]'
                        ];
                        
                        let foundElements = [];
                        
                        // 查找并收集所有可能的元素
                        for (const selector of exactSelectors) {
                            try {
                                const elements = document.querySelectorAll(selector);
                                for (const elem of elements) {
                                    if (elem.textContent && elem.textContent.includes('上传封面')) {
                                        foundElements.push({
                                            element: elem,
                                            method: '精确类名',
                                            selector: selector
                                        });
                                    }
                                }
                            } catch(e) {
                                console.error('选择器错误:', e);
                            }
                        }
                        
                        // 方法2: 从父元素定位
                        const headerDiv = document.querySelector('div._header-title_2t3fe_17');
                        if (headerDiv) {
                            const items = headerDiv.querySelectorAll('div');
                            for (const item of items) {
                                if (item.textContent && item.textContent.includes('上传封面')) {
                                    foundElements.push({
                                        element: item,
                                        method: '父元素定位',
                                        selector: 'div._header-title_2t3fe_17 > div'
                                    });
                                }
                            }
                        }
                        
                        // 方法3: 使用图片或特殊元素查找
                        // 从截图看，可能是包含图片的元素
                        const imgContainers = document.querySelectorAll('div[class*="header-title-item"], div[class*="title-item"]');
                        for (const container of imgContainers) {
                            if (container.textContent && container.textContent.includes('上传封面')) {
                                // 检查是否包含图片元素
                                const hasImg = container.querySelector('img') !== null;
                                foundElements.push({
                                    element: container,
                                    method: '图片容器',
                                    selector: hasImg ? '包含图片的容器' : '不包含图片的容器'
                                });
                            }
                        }
                        
                        console.log(`总共找到 ${foundElements.length} 个可能的上传封面选项卡`);
                        
                        // 如果找到了元素，尝试点击
                        if (foundElements.length > 0) {
                            // 高亮所有找到的元素
                            foundElements.forEach((item, index) => {
                                try {
                                    const elem = item.element;
                                    const originalStyle = elem.getAttribute('style') || '';
                                    elem.setAttribute('style', originalStyle + 
                                        '; border: 2px solid red !important; background-color: yellow !important; z-index: 9999 !important');
                                    console.log(`已高亮元素 ${index+1}: ${item.method} - ${item.selector}`);
                                } catch(e) {
                                    console.error(`高亮元素失败: ${e}`);
                                }
                            });
                            
                            // 尝试点击每个元素的中心位置
                            for (const item of foundElements) {
                                try {
                                    const elem = item.element;
                                    console.log(`尝试点击元素: ${item.method} - ${item.selector}`);
                                    
                                    // 获取元素位置和尺寸
                                    const rect = elem.getBoundingClientRect();
                                    
                                    // 确保元素在视口中
                                    if (rect.top < 0 || rect.bottom > window.innerHeight ||
                                        rect.left < 0 || rect.right > window.innerWidth) {
                                        elem.scrollIntoView({behavior: 'instant', block: 'center'});
                                        console.log('将元素滚动到视图中');
                                    }
                                    
                                    // 计算元素中心点
                                    const centerX = rect.left + rect.width / 2;
                                    const centerY = rect.top + rect.height / 2;
                                    
                                    console.log(`元素位置: left=${rect.left}, top=${rect.top}, width=${rect.width}, height=${rect.height}`);
                                    console.log(`点击坐标: x=${centerX}, y=${centerY}`);
                                    
                                    // 方法1: 使用模拟点击事件点击元素中心
                                    const clickEvent = new MouseEvent('click', {
                                        bubbles: true,
                                        cancelable: true,
                                        view: window,
                                        clientX: centerX,
                                        clientY: centerY
                                    });
                                    elem.dispatchEvent(clickEvent);
                                    console.log(`已使用事件模拟点击元素中心`);
                                    
                                    // 等待一小段时间检查是否成功
                                    return new Promise(resolve => {
                                        setTimeout(() => {
                                            // 检查是否出现文件上传输入框
                                            const fileInputs = document.querySelectorAll('input[type="file"]');
                                            if (fileInputs.length > 0) {
                                                console.log('点击成功: 找到文件上传输入框');
                                                resolve(true);
                                            } else {
                                                // 尝试直接点击
                                                try {
                                                    elem.click();
                                                    console.log('尝试直接点击元素');
                                                } catch(e) {
                                                    console.log(`直接点击失败: ${e}`);
                                                }
                                                resolve(false);
                                            }
                                        }, 500);
                                    });
                                } catch(e) {
                                    console.error(`点击元素失败: ${e}`);
                                }
                            }
                        }
                        
                        // 如果上面的方法都失败了，尝试直接点击坐标位置
                        // 从截图分析，上传封面选项卡的位置大约在页面顶部
                        console.log('尝试点击固定坐标位置...');
                        
                        // 获取上传封面区域的大致位置
                        const headerArea = document.querySelector('div[class*="header-title"]') || 
                                          document.querySelector('div[class*="tabs"]');
                        
                        if (headerArea) {
                            const rect = headerArea.getBoundingClientRect();
                            // 假设上传封面选项卡在标题区域的右侧位置
                            const clickX = rect.left + rect.width * 0.75; // 右侧3/4处
                            const clickY = rect.top + rect.height / 2;    // 垂直中心
                            
                            console.log(`尝试点击坐标: x=${clickX}, y=${clickY}`);
                            
                            // 创建并分发点击事件
                            const clickEvent = new MouseEvent('click', {
                                bubbles: true,
                                cancelable: true,
                                view: window,
                                clientX: clickX,
                                clientY: clickY
                            });
                            headerArea.dispatchEvent(clickEvent);
                            console.log('已点击估计位置');
                            
                            return true;
                        }
                        
                        return false;
                    }
                    
                    // 执行查找和点击
                    return findAndClickUploadButton();
                """)
                
                if not upload_clicked:
                    print("JavaScript方法未能点击上传封面按钮，尝试直接坐标点击...")
                    

                    
                    # 方法2：使用直接坐标点击 - 基于截图中"上传封面"的位置
                    try:
                        # 获取页面尺寸
                        window_size = self.driver.execute_script("""
                            return {
                                width: window.innerWidth,
                                height: window.innerHeight
                            };
                        """)
                        print(f"页面尺寸: 宽={window_size['width']}, 高={window_size['height']}")
                        
                        # 从截图分析，"上传封面"选项卡位于顶部标签栏，大约在右侧位置
                        # 使用相对坐标计算点击位置
                        
                        # 1. 首先尝试定位标题栏元素
                        header_rect = self.driver.execute_script("""
                            // 尝试找到标题栏/选项卡容器
                            const headerSelectors = [
                                'div._header-title_2t3fe_17',
                                'div[class*="header-title"]',
                                'div[class*="tabs"]',
                                'div[role="tablist"]'
                            ];
                            
                            for (const selector of headerSelectors) {
                                const elem = document.querySelector(selector);
                                if (elem) {
                                    const rect = elem.getBoundingClientRect();
                                    return {
                                        left: rect.left,
                                        top: rect.top,
                                        width: rect.width,
                                        height: rect.height,
                                        found: true,
                                        selector: selector
                                    };
                                }
                            }
                            
                            // 如果找不到标题栏，返回页面顶部区域的估计位置
                            return {
                                left: 0,
                                top: 0,
                                width: window.innerWidth,
                                height: 50,  // 假设标题栏高度约为50px
                                found: false
                            };
                        """)
                        
                        if header_rect.get('found'):
                            print(f"找到标题栏: {header_rect.get('selector')}, 位置: 左={header_rect['left']}, 顶={header_rect['top']}, 宽={header_rect['width']}, 高={header_rect['height']}")
                        else:
                            print("未找到标题栏，使用估计位置")
                        
                        # 2. 计算"上传封面"选项卡的估计位置
                        # 从截图看，"上传封面"选项卡位于右侧，约3/4处
                        tab_x = int(header_rect['left'] + header_rect['width'] * 0.75)  # 右侧3/4位置
                        tab_y = int(header_rect['top'] + header_rect['height'] / 2)      # 垂直中心
                        
                        print(f"计算的点击坐标: x={tab_x}, y={tab_y}")
                        
                        # 3. 使用ActionChains在计算的坐标处点击
                        print("使用ActionChains在计算坐标处点击...")
                        actions = ActionChains(self.driver)
                        actions.move_by_offset(tab_x, tab_y).click().perform()
                        
                        # 等待并检查点击是否成功
                        time.sleep(1.5)
                        
                        # 验证点击是否成功 - 检查是否出现文件上传输入框
                        file_inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="file"]')
                        if file_inputs:
                            print("坐标点击成功: 找到文件上传输入框")
                        else:
                            print("坐标点击可能未成功，尝试直接点击元素...")
                            
                            # 如果坐标点击失败，尝试使用更精确的元素定位
                            # 基于截图中的类名，尝试更精确的定位
                            upload_tab_clicked = self.driver.execute_script("""
                                // 从截图中的类名精确定位
                                const tabSelectors = [
                                    'div._header-title-item_2t3fe_27',
                                    'div._header-title-item-active_2t3fe_34',
                                    'div[class*="header-title-item_2t3fe"]'
                                ];
                                
                                // 查找并点击
                                for (const selector of tabSelectors) {
                                    const elements = document.querySelectorAll(selector);
                                    for (const elem of elements) {
                                        if (elem.textContent && elem.textContent.includes('上传封面')) {
                                            // 高亮元素
                                            elem.style.border = '3px solid red';
                                            elem.style.backgroundColor = 'yellow';
                                            
                                            // 获取元素中心坐标
                                            const rect = elem.getBoundingClientRect();
                                            const centerX = rect.left + rect.width / 2;
                                            const centerY = rect.top + rect.height / 2;
                                            
                                            // 创建点击事件
                                            const clickEvent = new MouseEvent('click', {
                                                bubbles: true,
                                                cancelable: true,
                                                view: window,
                                                clientX: centerX,
                                                clientY: centerY
                                            });
                                            
                                            // 分发事件
                                            elem.dispatchEvent(clickEvent);
                                            return true;
                                        }
                                    }
                                }
                                
                                // 如果上面的方法失败，尝试更通用的查找
                                const allElements = Array.from(document.querySelectorAll('*'));
                                const targetElements = allElements.filter(el => 
                                    el.textContent && el.textContent.includes('上传封面') && 
                                    window.getComputedStyle(el).display !== 'none');
                                
                                if (targetElements.length > 0) {
                                    const elem = targetElements[0];
                                    elem.style.border = '3px solid blue';
                                    elem.style.backgroundColor = 'lightblue';
                                    elem.click();
                                    return true;
                                }
                                
                                return false;
                            """)
                            
                            if not upload_tab_clicked:
                                print("所有方法都未能点击上传封面按钮，尝试最后的方法...")
                                
                                # 最后尝试：直接模拟键盘Tab键切换到上传封面选项卡
                                try:
                                    # 先点击封面截取选项卡
                                    self.driver.execute_script("""
                                        const allElements = Array.from(document.querySelectorAll('*'));
                                        const targetElements = allElements.filter(el => 
                                            el.textContent && (el.textContent.includes('封面截取') || el.textContent.includes('封面裁剪')) && 
                                            window.getComputedStyle(el).display !== 'none');
                                        
                                        if (targetElements.length > 0) {
                                            targetElements[0].click();
                                            return true;
                                        }
                                        return false;
                                    """)
                                    
                                    time.sleep(0.5)
                                    
                                    # 然后按Tab键切换到上传封面选项卡
                                    actions = ActionChains(self.driver)
                                    actions.send_keys(Keys.TAB).perform()
                                    time.sleep(0.5)
                                    actions.send_keys(Keys.ENTER).perform()
                                    
                                    print("尝试使用JavaScript查找并点击上传封面选项卡")
                                    time.sleep(1)
                                except Exception as e:
                                    print(f"Tab键切换失败: {str(e)}")
                    
                    except Exception as e:
                        print(f"坐标点击方法失败: {str(e)}")
                        # 即使出错也继续尝试，不要立即返回False
                
                # 无论上面的方法是否成功，都再次检查是否已经出现文件上传输入框
                try:
                    # 等待文件上传输入框出现
                    file_inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="file"]')
                    if file_inputs:
                        print("已找到文件上传输入框，点击成功")
                    else:
                        # 最后的尝试：查找任何可能的上传区域
                        upload_area_found = self.driver.execute_script("""
                            // 查找可能的上传区域
                            const uploadTexts = ['上传图片', '点击上传', '拖拽图片', '上传封面'];
                            
                            for (const text of uploadTexts) {
                                const elements = Array.from(document.querySelectorAll('*'));
                                for (const elem of elements) {
                                    if (elem.textContent && elem.textContent.includes(text)) {
                                        // 尝试点击
                                        try {
                                            elem.click();
                                            console.log(`点击了包含"${text}"的元素`);
                                            return true;
                                        } catch(e) {
                                            console.log(`点击失败: ${e}`);
                                        }
                                    }
                                }
                            }
                            
                            return false;
                        """)
                        
                        if not upload_area_found:
                            print("未能找到上传区域，可能需要手动点击")

                except Exception as e:
                    print(f"最终检查失败: {str(e)}")
                
                print("完成上传封面按钮点击尝试")
                time.sleep(2)  # 增加等待时间，确保文件选择界面完全加载
                
                # 检查当前是否在"封面截取"界面，如果是则切换到"上传封面"界面
                print("检查当前界面状态...")
                current_tab = self.driver.execute_script("""
                    // 检查当前是在"封面截取"还是"上传封面"界面
                    function checkCurrentTab() {
                        // 1. 检查宽高比选项是否存在 (如果存在，说明在"封面截取"界面)
                        const aspectRatioOptions = document.querySelectorAll('div[class*="aspect-ratio"], div[class*="ratio-item"]');
                        const aspectRatioTexts = ['4:3', '3:4', '1:1', '9:16', '宽高比'];
                        for (const option of aspectRatioOptions) {
                            for (const text of aspectRatioTexts) {
                                if (option.textContent.includes(text)) {
                                    return { tab: '封面截取', evidence: '找到宽高比选项: ' + text };
                                }
                            }
                        }
                        
                        // 2. 检查是否有裁剪、拖动、旋转等操作元素
                        const cropToolTexts = ['裁剪', '旋转', '拖动', '画面', '确认'];
                        const allElements = document.querySelectorAll('*');
                        for (const elem of allElements) {
                            if (elem.textContent) {
                                for (const text of cropToolTexts) {
                                    if (elem.textContent.includes(text)) {
                                        return { tab: '封面截取', evidence: '找到裁剪工具: ' + text };
                                    }
                                }
                            }
                        }
                        
                        // 3. 检查是否有"上传图片"或"拖拽图片"提示
                        const uploadTexts = ['上传图片', '点击上传', '拖拽图片'];
                        for (const elem of allElements) {
                            if (elem.textContent) {
                                for (const text of uploadTexts) {
                                    if (elem.textContent.includes(text)) {
                                        return { tab: '上传封面', evidence: '找到上传提示: ' + text };
                                    }
                                }
                            }
                        }
                        
                        // 4. 检查是否存在文件上传输入框
                        const fileInputs = document.querySelectorAll('input[type="file"]');
                        if (fileInputs.length > 0) {
                            return { tab: '上传封面', evidence: '找到文件上传输入框' };
                        }
                        
                        return { tab: '未知', evidence: '无法确定当前界面' };
                    }
                    
                    return checkCurrentTab();
                """)
                
                print(f"当前界面检测: {current_tab.get('tab', '未知')} ({current_tab.get('evidence', '无证据')})")
                
                # 如果当前在"封面截取"界面，需要切换到"上传封面"界面
                if current_tab.get('tab') == '封面截取':
                    print("检测到当前在封面截取界面，尝试切换到上传封面界面...")
                    
                    # 尝试点击"上传封面"选项卡
                    tab_clicked = self.driver.execute_script("""
                        // 寻找并点击"上传封面"选项卡
                        const tabSelectors = [
                            'div._header-title-item_2t3fe_27',  // 从截图中的精确类名
                            'div[class*="header-title-item"]',
                            'div[class*="tabs"] div',
                            'div[role="tab"]'
                        ];
                        
                        // 查找包含"上传封面"的选项卡
                        for (const selector of tabSelectors) {
                            const elements = document.querySelectorAll(selector);
                            for (const elem of elements) {
                                if (elem.textContent && elem.textContent.includes('上传封面')) {
                                    // 高亮并滚动到视图
                                    elem.style.border = '3px solid red';
                                    elem.scrollIntoView({behavior: 'instant', block: 'center'});
                                    
                                    // 点击元素
                                    try {
                                        elem.click();
                                        console.log('已点击上传封面选项卡');
                                        return true;
                                    } catch(e) {
                                        console.error('点击失败:', e);
                                        // 尝试JavaScript点击
                                        try {
                                            const event = new MouseEvent('click', {
                                                bubbles: true,
                                                cancelable: true,
                                                view: window
                                            });
                                            elem.dispatchEvent(event);
                                            console.log('已使用事件模拟点击上传封面选项卡');
                                            return true;
                                        } catch(e2) {
                                            console.error('事件模拟点击失败:', e2);
                                        }
                                    }
                                }
                            }
                        }
                        
                        // 如果找不到选项卡，尝试其他方法
                        const headerArea = document.querySelector('div[class*="header-title"]') || 
                                          document.querySelector('div[class*="tabs"]');
                                          
                        if (headerArea) {
                            // 获取所有子元素
                            const children = headerArea.children;
                            // 尝试点击第二个子元素 (假设第一个是"封面截取"，第二个是"上传封面")
                            if (children && children.length > 1) {
                                try {
                                    children[1].click();
                                    console.log('已点击标题区域第二个子元素');
                                    return true;
                                } catch(e) {
                                    console.error('点击第二个子元素失败:', e);
                                }
                            }
                        }
                        
                        return false;
                    """)
                    
                    if tab_clicked:
                        print("已点击上传封面选项卡，等待界面切换...")
                        time.sleep(2)  # 等待界面切换
                    else:
                        print("无法通过JavaScript点击上传封面选项卡，尝试使用坐标点击...")
                        
                        # 尝试使用坐标点击
                        try:
                            # 获取顶部标题栏位置
                            header_rect = self.driver.execute_script("""
                                const header = document.querySelector('div[class*="header-title"]') || 
                                              document.querySelector('div[class*="tabs"]');
                                
                                if (header) {
                                    const rect = header.getBoundingClientRect();
                                    return {
                                        left: rect.left,
                                        top: rect.top,
                                        width: rect.width,
                                        height: rect.height
                                    };
                                }
                                
                                return {
                                    left: 0,
                                    top: 50,
                                    width: window.innerWidth,
                                    height: 40
                                };
                            """)
                            
                            # 点击标题栏右侧位置
                            actions = ActionChains(self.driver)
                            tab_x = int(header_rect['left'] + header_rect['width'] * 0.75)  # 右侧3/4位置
                            tab_y = int(header_rect['top'] + header_rect['height'] / 2)      # 垂直中心
                            
                            print(f"尝试点击坐标位置: x={tab_x}, y={tab_y}")
                            actions.move_by_offset(tab_x, tab_y).click().perform()
                            time.sleep(2)
                        except Exception as e:
                            print(f"坐标点击失败: {str(e)}")
                
                # 查找文件上传input(使用原来能工作的方法)
                print("查找文件上传输入框...")
                upload_input = self.driver.execute_script("""
                    function findUploadInput() {
                        // 方法1：查找包含"拖拽图片到此或点击上传"相关文本的区域
                        const elements = document.querySelectorAll('div, p, span, button');
                        for (const elem of elements) {
                            if (elem.textContent && (
                                elem.textContent.includes('上传图片') ||
                                elem.textContent.includes('点击上传') ||
                                elem.textContent.includes('拖拽图片')
                            )) {
                                // 找到上传区域，查找最近的input
                                let container = elem.closest('[class*="upload"], [class*="uploader"]');
                                if (container) {
                                    const input = container.querySelector('input[type="file"]');
                                    if (input) {
                                        input.style.display = 'block';
                                        input.style.visibility = 'visible';
                                        input.style.opacity = '1';
                                        input.style.position = 'fixed';
                                        input.style.top = '50%';
                                        input.style.left = '50%';
                                        input.style.zIndex = '99999';
                                        return input;
                                    }
                                }
                                
                                // 如果没找到container，检查当前元素是否有input
                                const nearInput = elem.querySelector('input[type="file"]') || 
                                               elem.parentElement.querySelector('input[type="file"]');
                                if (nearInput) {
                                    nearInput.style.display = 'block';
                                    nearInput.style.visibility = 'visible';
                                    nearInput.style.opacity = '1';
                                    nearInput.style.position = 'fixed';
                                    nearInput.style.top = '50%';
                                    nearInput.style.left = '50%';
                                    nearInput.style.zIndex = '99999';
                                    return nearInput;
                                }
                            }
                        }
                        
                        return null;
                    }
                    
                    return findUploadInput();
                """)
                
                if not upload_input:
                    print("JavaScript方法未能找到文件上传输入框，尝试Selenium方法...")
                    try:
                        
                        # 检查界面状态(简化版本，不进行额外的操作)
                        
                                                 # 使用原来能正常工作的选择器
                        selectors = [
                            # 通过上传图标区域定位
                            '[class*="upload"] input[type="file"]',
                            '[class*="uploader"] input[type="file"]',
                            # 通过文本定位
                            '//div[contains(., "上传图片")]//input[@type="file"]',
                            '//div[contains(., "点击上传")]//input[@type="file"]',
                            # 通过图标定位
                            '//i[contains(@class, "upload") or contains(@class, "cloud")]/..//input[@type="file"]'
                        ]
                        
                        # 先点击上传封面选项卡
                        try:
                            cover_upload_tab_selectors = [
                                '//div[contains(text(), "上传封面")]', 
                                '//span[contains(text(), "上传封面")]',
                                '//li[contains(text(), "上传封面")]',
                                '//button[contains(text(), "上传封面")]'
                            ]
                            
                            for tab_selector in cover_upload_tab_selectors:
                                try:
                                    cover_tab = WebDriverWait(self.driver, 3).until(
                                        EC.element_to_be_clickable((By.XPATH, tab_selector))
                                    )
                                    cover_tab.click()
                                    print(f"成功点击上传封面选项卡: {tab_selector}")
                                    time.sleep(1)  # 等待切换到上传页面
                                    break
                                except:
                                    continue
                        except Exception as e:
                            print(f"点击上传封面选项卡失败: {str(e)}")
                        
                        # 查找文件上传输入框
                        for selector in selectors:
                            try:
                                if selector.startswith('//'):
                                    # XPath选择器
                                    upload_input = WebDriverWait(self.driver, 3).until(
                                        EC.presence_of_element_located((By.XPATH, selector))
                                    )
                                else:
                                    # CSS选择器
                                    upload_input = WebDriverWait(self.driver, 3).until(
                                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                                    )
                                if upload_input:
                                    print(f"使用Selenium找到文件上传输入框: {selector}")
                                    break
                            except:
                                continue
                    except Exception as e:
                        print(f"Selenium方法查找文件上传输入框失败: {str(e)}")
                        return False
                
                if not upload_input:
                    print("未能找到文件上传输入框")
                    return False
                
                # 上传封面图片
                abs_cover_path = os.path.abspath(self.cover_path)
                print(f"准备上传封面图片: {abs_cover_path}")
                upload_input.send_keys(abs_cover_path)
                print("封面图片上传完成")
                
                # 等待上传完成
                print("等待确认按钮出现...")
                time.sleep(1)
                
                # 先点击上传封面按钮
                try:
                    print("尝试点击上传封面按钮...")
                    # 查找上传封面按钮
                    upload_button = self.driver.execute_script("""
                        const uploadBtn = document.querySelector('div._header-title-item_2t3fe_27');
                        if (uploadBtn && uploadBtn.textContent === '上传封面') {
                            return uploadBtn;
                        }
                        return null;
                    """)
                    
                    upload_clicked = False
                    if upload_button:
                        print("找到上传封面按钮，尝试点击...")
                        try:
                            self.driver.execute_script("arguments[0].click();", upload_button)
                            print("使用JavaScript点击上传封面按钮")
                            time.sleep(1)
                            upload_clicked = True
                        except Exception as e:
                            print(f"点击按钮失败: {str(e)}")
                            upload_clicked = False
                    
                    if upload_clicked:
                        # 检查是否有"清空上传"按钮
                        has_clear_button = self.driver.execute_script("""
                            const clearButtons = document.querySelectorAll('div._cropper-upload-clear_1i0wh_115, [class*="cropper-upload-clear"]');
                            return clearButtons.length > 0;
                        """)
                        
                        if has_clear_button:
                            print("检测到'清空上传'按钮，图片已上传，直接点击确认")
                        else:
                            print("未检测到'清空上传'按钮，需要上传图片")
                            # 查找文件上传input
                            print("查找文件上传输入框...")
                            upload_input = self.driver.execute_script("""
                                function findUploadInput() {
                                     // 方法1：查找包含"拖拽图片到此或点击上传"相关文本的区域
                                   const elements = document.querySelectorAll('div, p, span, button');
                                  for (const elem of elements) {
                                    if (elem.textContent && (
                                        elem.textContent.includes('上传图片') ||
                                       elem.textContent.includes('点击上传') ||
                                       elem.textContent.includes('拖拽图片')
                                   )) {
                                     // 找到上传区域，查找最近的input
                                     let container = elem.closest('[class*="upload"], [class*="uploader"]');
                                       if (container) {
                                       const input = container.querySelector('input[type="file"]');
                                        if (input) {
                                          input.style.display = 'block';
                                         input.style.visibility = 'visible';
                                          input.style.opacity = '1';
                                           input.style.position = 'fixed';
                                          input.style.top = '50%';
                                          input.style.left = '50%';
                                          input.style.zIndex = '99999';
                                           return input;
                                        }
                                    }
                                
                                        // 如果没找到container，检查当前元素是否有input
                                          const nearInput = elem.querySelector('input[type="file"]') || 
                                                 elem.parentElement.querySelector('input[type="file"]');
                                          if (nearInput) {
                                              nearInput.style.display = 'block';
                                              nearInput.style.visibility = 'visible';
                                                 nearInput.style.opacity = '1';
                                               nearInput.style.position = 'fixed';
                                               nearInput.style.top = '50%';
                                                nearInput.style.left = '50%';
                                                  nearInput.style.zIndex = '99999';
                                              return nearInput;
                                            }
                                        }
                                    }
                                     return null;
                                }
                                return findUploadInput();
                            """)
                            
                            if upload_input:
                                # 上传封面图片
                                abs_cover_path = os.path.abspath(self.cover_path)
                                print(f"准备上传封面图片: {abs_cover_path}")
                                upload_input.send_keys(abs_cover_path)
                                print("封面图片上传完成")
                                time.sleep(1)  # 等待上传完成
                            else:
                                print("未找到文件上传输入框")
                    else:
                        print("未能点击上传封面按钮，继续尝试确认流程")
                except Exception as e:
                    print(f"点击上传封面按钮或检查清空上传按钮时出错: {str(e)}")
                
                # 最多尝试点击确认按钮3次
                max_attempts = 6
                for attempt in range(max_attempts):
                    try:
                        # 使用XPath精确定位按钮
                        # 先点击上传封面按钮
                        try:
                            upload_button = self.driver.find_element(By.XPATH, "//div[contains(text(), '上传封面')]")
                            if upload_button and upload_button.is_displayed():
                                upload_button.click()
                                print("点击上传封面按钮成功")
                                time.sleep(1)  # 等待响应
                        except Exception as e:
                            print(f"点击上传封面按钮失败，尝试其他方法: {str(e)}")
                            try:
                                # 使用JavaScript尝试点击
                                self.driver.execute_script("""
                                    const uploadBtns = document.querySelectorAll('div');
                                    for (const btn of uploadBtns) {
                                        if (btn.textContent === '上传封面') {
                                            btn.click();
                                            return true;
                                        }
                                    }
                                    return false;
                                """)
                                print("使用JavaScript点击上传封面按钮")
                                time.sleep(1)  # 等待响应
                            except Exception as e:
                                print(f"JavaScript点击上传封面按钮也失败: {str(e)}")

                        # 然后再点击确认按钮
                        confirm_button = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, 
                                "//button[contains(@class, 'ant-btn') and contains(@class, '_footer-btn')]//span[text()='确认']/.."))
                        )
                        
                        print(f"尝试 {attempt+1}/{max_attempts}: 找到确认按钮，尝试点击...")
                        confirm_button.click()
                        print("成功点击确认按钮")
                        
                        # 等待确认操作完成和错误提示出现
                        time.sleep(3)  # 增加等待时间
                        
                        # 专门检测span中的错误提示
                        span_error = self.driver.execute_script("""
                            // 专门搜索span标签中的错误提示
                            const spans = document.querySelectorAll('span');
                            for (const span of spans) {
                                if (span && span.textContent) {
                                    const text = span.textContent.trim();
                                    if (text.includes('今日操作次数已达上限') || 
                                        text.includes('今日操作次数已达到上限') || 
                                        text.includes('操作次数已达上限') || 
                                        text.includes('今日已达上限')) {
                                        console.log("找到span中的限额错误:", span.textContent);
                                        return "今日操作次数已达上限";
                                    }
                                }
                            }
                            return "";
                        """)
                        
                        if span_error:
                            print(f"在span标签中检测到限额错误: {span_error}")
                            if hasattr(self, 'current_account') and self.current_account:
                                print(f"结束账号 {self.current_account.name} 的上传任务，切换到下一个账号")
                            self.stop()
                            self.cleanup_chrome_processes()
                            return "SWITCH_ACCOUNT"
                        
                        # 检查是否出现错误提示 - 多次尝试检测错误消息，提高捕获率
                        error_msg = ""
                        for check_attempt in range(7):  # 增加检查次数到7次
                            # 先检查特定错误：今日操作次数已达到上限
                            limit_error = self.driver.execute_script("""
                                // 专门检查"今日操作次数已达上限"错误
                                // 先检查span标签
                                const spans = document.querySelectorAll('span');
                                for (const span of spans) {
                                    if (span && span.textContent) {
                                        const text = span.textContent.trim();
                                        if (text.includes('今日操作次数已达上限') || 
                                            text.includes('今日操作次数已达到上限') ||
                                            text.includes('操作次数已达上限') ||
                                            text.includes('今日已达上限')) {
                                            console.log("找到span中的限额错误:", span.textContent);
                                            return "今日操作次数已达上限";
                                        }
                                    }
                                }
                                
                                // 然后检查其他元素
                                const allElements = document.querySelectorAll('*');
                                for (const elem of allElements) {
                                    if (elem && elem.textContent && 
                                        (elem.textContent.includes('今日操作次数已达上限') || 
                                         elem.textContent.includes('今日操作次数已达到上限') ||
                                         elem.textContent.includes('操作次数已达上限') ||
                                         elem.textContent.includes('今日已达上限'))) {
                                        console.log("找到限制错误:", elem.textContent);
                                        return "今日操作次数已达上限";
                                    }
                                }
                                return "";
                            """)
                            
                            if limit_error:
                                error_msg = limit_error
                                print(f"直接检测到限额错误: {error_msg}")
                                break
                                
                            # 常规错误检测
                            error_msg = check_error_message()
                            if error_msg:
                                break
                                
                            time.sleep(1)  # 间隔时间

                        # 处理检测到的错误
                        if error_msg:
                            if error_msg == "今日操作次数已达上限":
                                print(f"检测到'{error_msg}'，该账号今日已达到上传上限")
                                if hasattr(self, 'current_account') and self.current_account:
                                    print(f"结束账号 {self.current_account.name} 的上传任务，切换到下一个账号")
                                self.stop()
                                self.cleanup_chrome_processes()
                                raise AccountLimitReachedException("今日操作次数已达上限")
                            
                            # 处理其他错误消息（操作失败，请稍后再试）
                            # 增加重试计数
                            if not hasattr(self, '_error_retry_count'):
                                self._error_retry_count = 1
                            else:
                                self._error_retry_count += 1
                                
                            print(f"检测到'{error_msg}'错误，这是第 {self._error_retry_count} 次重试")
                            
                            # 确保不增加上传计数
                            if hasattr(self, 'current_account') and self.current_account:
                                if self.current_account.today_uploads > 0:
                                    self.current_account.today_uploads -= 1
                                print(f"重置上传计数：{self.current_account.today_uploads}")
                                
                            # 如果重试次数达到3次，结束当前账号的上传
                            if self._error_retry_count >= 3:
                                print(f"已连续遇到3次错误，结束账号 {self.current_account.name} 的上传任务")
                                self.stop()
                                self.cleanup_chrome_processes()
                                return "SWITCH_ACCOUNT"
                                
                            # 停止当前浏览器
                            self.stop()
                            # 清理Chrome进程
                            self.cleanup_chrome_processes()
                            time.sleep(2)
                            
                            print("按照第一次发布流程重新执行...")
                            # 启动浏览器
                            if not self.setup_browser():
                                raise Exception("浏览器重启失败")
                            print("浏览器重启成功")
                            
                            # 先访问快手主页
                            print("访问快手主页...")
                            self.driver.get("https://www.kuaishou.com")
                            time.sleep(3)
                            
                            # 加载cookies
                            if not self.load_cookies():
                                raise Exception("Cookie加载失败")
                            print("Cookie加载成功")
                            time.sleep(2)
                            
                            # 访问创作者平台
                            print("访问创作者平台...")
                            self.driver.get(self.creator_url)
                            time.sleep(5)
                            
                            # 检查页面状态
                            page_source = self.driver.page_source
                            if "发布作品" not in page_source and "创作服务" not in page_source:
                                print("警告：创作者平台未正确加载，尝试刷新...")
                                self.driver.refresh()
                                time.sleep(5)
                                
                            # 再次检查页面状态
                            page_source = self.driver.page_source
                            if "发布作品" in page_source or "创作服务" in page_source:
                                print("成功访问创作者平台")
                                # 返回特殊值以通知调用者需要重新开始上传流程
                                return "RETRY_NEEDED"
                            else:
                                raise Exception("无法正确加载创作者平台")
                        
                        # 检查界面是否已关闭
                        interface_closed = self.driver.execute_script("""
                            // 检查上传封面界面是否已关闭
                            const elements = document.querySelectorAll('div._header-title-item_2t3fe_27, div[class*="header-title-item"]');
                            for (const elem of elements) {
                                if (elem.textContent === '上传封面' && elem.getBoundingClientRect().height > 0) {
                                    return false;  // 界面未关闭
                                }
                            }
                            return true;  // 界面已关闭
                        """)
                        
                        if interface_closed:
                            print("确认按钮点击成功，界面已关闭")
                            
                            # 即使界面关闭，也再次检查一下是否有错误提示（可能在界面关闭后才出现错误）
                            time.sleep(2)  # 等待可能的错误消息出现
                            final_error = self.driver.execute_script("""
                                // 最后检查一次错误提示，特别关注"今日操作次数已达到上限"
                                const elements = document.querySelectorAll('*');
                                for (const elem of elements) {
                                    if (elem && elem.textContent && elem.textContent.includes('今日操作次数已达到上限')) {
                                        return "今日操作次数已达到上限";
                                    }
                                }
                                return "";
                            """)
                            
                            if final_error:
                                print(f"界面关闭后检测到错误: {final_error}")
                                if hasattr(self, 'current_account') and self.current_account:
                                    print(f"结束账号 {self.current_account.name} 的上传任务，切换到下一个账号")
                                self.stop()
                                self.cleanup_chrome_processes()
                                return "SWITCH_ACCOUNT"
                                
                            return True
                        else:
                            print(f"尝试 {attempt+1}/{max_attempts}: 界面未关闭，将再次尝试点击确认按钮")
                            
                            # 尽管界面未关闭，仍然检查一次是否出现了错误提示
                            error_found = self.driver.execute_script("""
                                // 检查是否出现错误提示
                                const elements = document.querySelectorAll('*');
                                for (const elem of elements) {
                                    if (elem && elem.textContent && elem.textContent.includes('今日操作次数已达到上限')) {
                                        return true;
                                    }
                                }
                                return false;
                            """)
                            
                            if error_found:
                                print("界面未关闭但检测到今日操作次数已达到上限")
                                if hasattr(self, 'current_account') and self.current_account:
                                    print(f"结束账号 {self.current_account.name} 的上传任务，切换到下一个账号")
                                self.stop()
                                self.cleanup_chrome_processes()
                                return "SWITCH_ACCOUNT"
                            
                            if attempt == max_attempts - 1:
                                print("已达到最大尝试次数，返回成功")
                                return True
                            continue
                        
                    except Exception as e:
                        print(f"尝试 {attempt+1}/{max_attempts}: 点击确认按钮失败: {str(e)}")
                        print("尝试备用方法...")
                        
                        try:
                            # 使用JavaScript查找并点击按钮
                            clicked = self.driver.execute_script("""
                                // 查找各种可能的确认按钮
                                const buttonTexts = ['确认', '确定', '完成', '应用'];
                                const buttons = Array.from(document.querySelectorAll('button, [role="button"], [class*="btn"]'));
                                
                                for (const btn of buttons) {
                                    if (btn.textContent && buttonTexts.some(text => btn.textContent.includes(text)) && 
                                        window.getComputedStyle(btn).display !== 'none') {
                                        btn.click();
                                        return true;
                                    }
                                }
                                return false;
                            """)
                            
                            if clicked:
                                print(f"尝试 {attempt+1}/{max_attempts}: 使用JavaScript成功点击确认按钮")
                                time.sleep(2)
                                
                                # 检查界面是否已关闭
                                interface_closed = self.driver.execute_script("""
                                    // 检查上传封面界面是否已关闭
                                    const elements = document.querySelectorAll('div._header-title-item_2t3fe_27, div[class*="header-title-item"]');
                                    for (const elem of elements) {
                                        if (elem.textContent === '上传封面' && elem.getBoundingClientRect().height > 0) {
                                            return false;  // 界面未关闭
                                        }
                                    }
                                    return true;  // 界面已关闭
                                """)
                                
                                if interface_closed:
                                    print("确认按钮点击成功，界面已关闭")
                                    
                                    # 即使界面关闭，也检查一次是否有错误提示
                                    time.sleep(2)
                                    limit_error = self.driver.execute_script("""
                                        const elements = document.querySelectorAll('*');
                                        for (const elem of elements) {
                                            if (elem && elem.textContent && elem.textContent.includes('今日操作次数已达到上限')) {
                                                return "今日操作次数已达到上限";
                                            }
                                        }
                                        return "";
                                    """)
                                    
                                    if limit_error:
                                        print(f"通过JavaScript点击后，检测到错误: {limit_error}")
                                        if hasattr(self, 'current_account') and self.current_account:
                                            print(f"结束账号 {self.current_account.name} 的上传任务，切换到下一个账号")
                                        self.stop()
                                        self.cleanup_chrome_processes()
                                        return "SWITCH_ACCOUNT"
                                    
                                    time.sleep(1)
                                    return True
                                   
                                else:
                                    print(f"尝试 {attempt+1}/{max_attempts}: 界面未关闭，将再次尝试点击确认按钮")
                                    
                                    # 尽管界面未关闭，检查是否出现了错误提示
                                    error_found = self.driver.execute_script("""
                                        const elements = document.querySelectorAll('*');
                                        for (const elem of elements) {
                                            if (elem && elem.textContent && elem.textContent.includes('今日操作次数已达到上限')) {
                                                return true;
                                            }
                                        }
                                        return false;
                                    """)
                                    
                                    if error_found:
                                        print("检测到今日操作次数已达到上限")
                                        if hasattr(self, 'current_account') and self.current_account:
                                            print(f"结束账号 {self.current_account.name} 的上传任务，切换到下一个账号")
                                        self.stop()
                                        self.cleanup_chrome_processes()
                                        return "SWITCH_ACCOUNT"
                                    
                                    if attempt == max_attempts - 1:
                                        print("已达到最大尝试次数，返回成功")
                                        return True
                                    continue
                            else:
                                print(f"尝试 {attempt+1}/{max_attempts}: JavaScript方法也未能找到或点击按钮")
                                if attempt == max_attempts - 1:
                                    print("已达到最大尝试次数，返回失败")
                                    return False
                        except Exception as e:
                            print(f"尝试 {attempt+1}/{max_attempts}: JavaScript点击方法失败: {str(e)}")
                            if attempt == max_attempts - 1:
                                print("已达到最大尝试次数，返回失败")
                                return False
                    
                    # 如果不是最后一次尝试，等待一下再重试
                    if attempt < max_attempts - 1:
                        print(f"等待2秒后进行第 {attempt+2} 次尝试...")
                        time.sleep(2)
                
                # 如果所有尝试都失败，返回失败
                return False
                
            except Exception as e:
                print(f"处理封面上传过程时出错: {str(e)}")
                return False
          
    def upload_cover(self):
        """上传自定义封面"""
        try:
            if not self.use_custom_cover or not self.cover_path:
                print("未启用自定义封面或未设置封面路径，跳过")
                return True

            print("正在设置自定义封面...")
            
            # 首先检查封面设置界面是否已经打开 - 检查方式与handle_cover_upload_process一致
            cover_ui_opened = self.driver.execute_script("""
                // 检查是否存在宽高比选项 (4:3, 3:4, 1:1, 9:16)
                const ratioTexts = ['4:3', '3:4', '1:1', '9:16'];
                for (const ratio of ratioTexts) {
                    const elements = document.querySelectorAll('div, span, p');
                    for (const elem of elements) {
                        if (elem.textContent === ratio) {
                            console.log('找到宽高比选项: ' + ratio);
                            return {opened: true, type: '宽高比'};
                        }
                    }
                }
                
                // 检查是否存在"封面截取"或"上传封面"选项
                const optionTexts = ['封面截取', '上传封面', '原始比例'];
                for (const text of optionTexts) {
                    const elements = document.querySelectorAll('div, span, p, button');
                    for (const elem of elements) {
                        if (elem.textContent.includes(text)) {
                            console.log('找到封面界面选项: ' + text);
                            return {opened: true, type: '封面选项'};
                        }
                    }
                }
                
                return {opened: false};
            """)
            
            if isinstance(cover_ui_opened, dict) and cover_ui_opened.get('opened'):
                print(f"检测到封面设置界面已打开 [{cover_ui_opened.get('type', '未知类型')}]")
                
                # 尝试切换到"上传封面"选项卡
                try:
                    upload_tab_clicked = self.driver.execute_script("""
                        const elements = document.querySelectorAll('div, span, button, a');
                        for (const elem of elements) {
                            if (elem.textContent === '上传封面') {
                                elem.click();
                                return true;
                            }
                        }
                        return false;
                    """)
                    
                    if upload_tab_clicked:
                        print("成功切换到「上传封面」选项卡")
                        time.sleep(1)
                        
                        # 直接跳转到封面上传流程
                        if self.handle_cover_upload_process():
                            print("封面上传成功")
                            return True
                        else:
                            print("封面上传失败")
                            return False
                    else:
                        print("无法找到「上传封面」选项卡，尝试常规上传流程")
                except Exception as e:
                    print(f"切换到上传封面选项卡时出错: {str(e)}")
            
            # 如果界面未打开或选项卡切换失败，尝试常规上传流程
            # 尝试新的封面上传流程，最多重试3次
            max_retries = 3
            retry_count = 0
            
            while retry_count < max_retries:
                result = self.handle_cover_upload_process()
                if result == "RETRY_NEEDED":
                    print(f"需要重试上传流程 (尝试 {retry_count + 1}/{max_retries})")
                    retry_count += 1
                    time.sleep(5)  # 等待一段时间后重试
                    continue
                elif result:
                    print("封面上传成功")
                    return True
                else:
                    print("封面上传失败")
                    return False
            
            print(f"已达到最大重试次数 ({max_retries})")
            return False
                
        except Exception as e:
            print(f"上传封面时出错: {str(e)}")
            return False
    
    def click_confirm_button(self):
        """查找并点击确认按钮"""
        try:
            # 强制点击上传封面选项卡，确保正确界面
            print("在点击确认按钮前，先点击上传封面选项卡...")
            try:
                # 使用JavaScript点击上传封面选项卡
                self.driver.execute_script("""
                    // 寻找并点击上传封面选项卡
                    function clickUploadTab() {
                        // 查找所有可能包含"上传封面"文本的元素
                        const elements = Array.from(document.querySelectorAll('*'));
                        const tabElements = elements.filter(el => 
                            el.textContent && el.textContent.includes('上传封面') && 
                            window.getComputedStyle(el).display !== 'none'
                        );
                        
                        // 点击找到的第一个元素
                        if (tabElements.length > 0) {
                            tabElements[0].click();
                            console.log('已点击上传封面选项卡');
                            return true;
                        }
                        return false;
                    }
                    return clickUploadTab();
                """)
                time.sleep(1)  # 给界面切换一点时间
            except Exception as e:
                print(f"尝试点击上传封面选项卡时出错: {str(e)}")
            
            # 查找各种可能的确认按钮
            confirm_selectors = [
                "//button[contains(text(), '确定')]",
                "//button[contains(text(), '确认')]",
                "//button[contains(text(), '完成')]",
                "//button[contains(text(), '应用')]",
                "//button[contains(text(), '保存')]",
                "//div[contains(@class, 'button')][contains(text(), '确定')]",
                "//div[contains(@class, 'button')][contains(text(), '确认')]",
                "//div[contains(@class, 'btn')][contains(text(), '确定')]",
                "//div[contains(@class, 'btn')][contains(text(), '确认')]"
            ]
            
            for selector in confirm_selectors:
                try:
                    buttons = self.driver.find_elements(By.XPATH, selector)
                    for btn in buttons:
                        if btn.is_displayed():
                            print(f"找到确认按钮: {btn.text}")
                            btn.click()
                            print("点击确认按钮")
                            time.sleep(1)
                            return True
                except Exception as e:
                    print(f"查找确认按钮 {selector} 时出错: {str(e)}")
            
            # 如果没有找到确认按钮，尝试使用JavaScript点击
            print("尝试使用JavaScript查找并点击确认按钮")
            click_result = self.driver.execute_script("""
                // 查找所有可能的确认按钮文本
                const buttonTexts = ['确定', '确认', '完成', '应用', '保存'];
                
                // 查找所有按钮和类似按钮的元素
                const buttons = Array.from(document.querySelectorAll('button, [role="button"], [class*="button"], [class*="btn"]'));
                
                // 查找匹配的按钮
                for (const btn of buttons) {
                    if (btn.textContent && buttonTexts.some(text => btn.textContent.includes(text)) && 
                        window.getComputedStyle(btn).display !== 'none') {
                        // 找到匹配的按钮，尝试点击
                        try {
                            btn.click();
                            console.log('JavaScript点击确认按钮成功');
                            return true;
                        } catch (e) {
                            console.error('JavaScript点击确认按钮失败:', e);
                        }
                    }
                }
                
                return false;
            """)
            
            if click_result:
                print("JavaScript成功点击确认按钮")
                time.sleep(1)
                return True
            
            # 如果没有找到确认按钮，可能不需要确认
            print("未找到确认按钮，可能不需要确认")
            return True
            
        except Exception as e:
            print(f"点击确认按钮时出错: {str(e)}")
            return False



    def handle_popups(self):
        """处理可能出现的弹窗"""
        try:
            # 检查是否有"已达今日上传作品上限"的提示
            limit_messages = self.driver.find_elements(By.XPATH, "//*[contains(text(), '已达今日上传作品上限')]")
            if limit_messages:
                for msg in limit_messages:
                    if msg.is_displayed():
                        print("检测到上传限制提示: 已达今日上传作品上限")
                        # 更新当前账号的上传计数
                        if self.current_account:
                            self.current_account.today_uploads = self.current_account.daily_upload_count
                        # 设置停止标志，确保不会继续处理视频
                        self.stop_flag = True
                        # 强制回到创作者平台首页
                        try:
                            self.driver.get(self.creator_url)
                        except Exception:
                            pass
                        # 抛出账号限制异常，以便切换到下一个账号
                        return "SWITCH_ACCOUNT"
            
            # 尝试查找带有X按钮的弹窗
            close_buttons = self.driver.find_elements(By.XPATH, "//button[contains(@class, 'close') or contains(@aria-label, '关闭') or contains(text(), '×') or contains(text(), 'X') or contains(text(), '取消')]")
            
            # 添加查找明确的X图标按钮
            x_buttons = self.driver.find_elements(By.XPATH, "//i[contains(@class, 'close')] | //span[contains(@class, 'close')] | //div[contains(@class, 'close')] | //button[contains(@class, 'close')]")
            close_buttons.extend(x_buttons)
            
            # 尝试通过SVG图标查找关闭按钮
            svg_close = self.driver.find_elements(By.CSS_SELECTOR, "svg[class*='close'], svg[aria-label*='关闭']")
            close_buttons.extend(svg_close)
            
            # 尝试查找明确的X字符按钮
            x_chars = self.driver.find_elements(By.XPATH, "//*[text()='×' or text()='X' or text()='✕' or text()='✖' or text()='✗' or text()='✘']")
            close_buttons.extend(x_chars)
            
            if close_buttons:
                print("检测到弹窗，尝试关闭...")
                for btn in close_buttons:
                    if btn.is_displayed():
                        print("点击关闭按钮")
                        try:
                            btn.click()
                            print("成功关闭弹窗")
                            time.sleep(1)  # 等待弹窗消失
                            return True
                        except Exception as e:
                            print(f"点击关闭按钮失败: {str(e)}")
                            # 尝试使用JavaScript点击
                            try:
                                self.driver.execute_script("arguments[0].click();", btn)
                                print("使用JavaScript成功关闭弹窗")
                                time.sleep(1)
                                return True
                            except:
                                pass
            
            # 检查是否存在带有"下一步"按钮的作品信息弹窗
            next_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '下一步')]")
            for btn in next_buttons:
                if btn.is_displayed():
                    print("检测到'下一步'按钮，点击跳过")
                    try:
                        btn.click()
                        print("成功点击'下一步'按钮")
                        time.sleep(1)
                        return True
                    except:
                        pass
            
            # 检查是否有取消按钮
            cancel_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '取消')]")
            for btn in cancel_buttons:
                if btn.is_displayed():
                    print("检测到'取消'按钮，点击取消")
                    try:
                        btn.click()
                        print("成功点击'取消'按钮")
                        time.sleep(1)
                        return True
                    except:
                        pass
            
            return False
        except Exception as e:
            print(f"处理弹窗时出错: {str(e)}")
            return False

    def upload_video(self, video_path, status_callback=None):
        """上传视频"""
        # 添加重试计数器，防止无限递归
        if not hasattr(self, '_retry_count'):
            self._retry_count = 0
        else:
            self._retry_count += 1
            
        # 限制最大重试次数
        if self._retry_count > 3:
            print(f"已达到最大重试次数(3)，上传失败")
            self._retry_count = 0  # 重置计数器
            # 删除视频文件（无论成功与否）
            try:
                os.remove(video_path)
                print(f"已删除视频文件: {video_path}")
                if status_callback:
                    status_callback(f"上传失败，但已删除视频文件: {os.path.basename(video_path)}")
            except Exception as e:
                print(f"删除视频文件失败: {str(e)}")
                if status_callback:
                    status_callback(f"删除视频文件失败: {str(e)}")
            return False
            
        try:
            self.current_video = os.path.basename(video_path)
            
            # 从当前账号获取封面和描述设置
            if self.current_account:
                self.use_custom_cover = self.current_account.use_custom_cover
                # 检测视频方向并选择对应的封面
                video_orientation = self.detect_video_orientation(video_path)
                if video_orientation == 'horizontal':
                    self.cover_path = self.current_account.horizontal_cover_path
                else:
                    self.cover_path = self.current_account.vertical_cover_path
                self.video_description = self.current_account.video_description
            
            if status_callback:
                status_callback(f"准备上传视频: {self.current_video}")
            
            # 确保页面完全加载
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 先点击"发布作品"按钮
            print("尝试点击发布作品按钮...")
            max_retries = 5
            for attempt in range(max_retries):
                try:
                    # 查找具有"发布作品"文本的元素
                    publish_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '发布作品')]")
                    if publish_elements:
                        # 打印找到的元素信息
                        for i, elem in enumerate(publish_elements):
                            try:
                                print(f"找到发布作品元素 {i+1}: {elem.tag_name}, 文本: {elem.text}, 可见: {elem.is_displayed()}")
                            except:
                                print(f"找到发布作品元素 {i+1}: 无法获取详细信息")
                        
                        # 点击第一个可见的元素
                        for elem in publish_elements:
                            if elem.is_displayed():
                                print(f"点击发布作品按钮: {elem.text}")
                                elem.click()
                                print("发布作品按钮点击成功")
                                time.sleep(2)
                                
                                # 检测并处理作品信息弹窗(1/4步骤)
                                try:
                                    print("检测作品信息填写弹窗(1/4步骤)...")
                                    # 通过多种方式查找作品信息弹窗
                                    work_info_dialog = self.driver.execute_script("""
                                        // 检测作品信息弹窗(1/4步骤)
                                        // 方法1: 查找包含"作品信息"的弹窗
                                        let dialogs = document.querySelectorAll('div[class*="modal"], div[class*="dialog"], div[class*="popup"]');
                                        for (let dialog of dialogs) {
                                            // 检查是否包含"作品信息"或"1/4"文本
                                            if (dialog.textContent && (
                                                dialog.textContent.includes('作品信息') || 
                                                dialog.textContent.includes('1/4') ||
                                                dialog.textContent.includes('下一步')
                                            )) {
                                                console.log('找到作品信息弹窗');
                                                return {found: true, type: '作品信息弹窗'};
                                            }
                                        }
                                        
                                        // 方法2: 查找特定标题文本
                                        let titles = document.querySelectorAll('div[class*="title"], h1, h2, h3, h4, h5');
                                        for (let title of titles) {
                                            if (title.textContent && title.textContent.trim() === '作品信息') {
                                                console.log('找到作品信息标题');
                                                return {found: true, type: '作品信息标题'};
                                            }
                                        }

                                        // 方法3: 查找步骤指示器(1/4)
                                        let steps = document.querySelectorAll('*');
                                        for (let step of steps) {
                                            if (step.textContent && step.textContent.trim() === '1/4') {
                                                console.log('找到步骤指示器(1/4)');
                                                return {found: true, type: '步骤指示器'};
                                            }
                                        }
                                        
                                        return {found: false};
                                    """)
                                    
                                    if isinstance(work_info_dialog, dict) and work_info_dialog.get('found'):
                                        print(f"检测到作品信息弹窗 [{work_info_dialog.get('type', '未知类型')}]")
                                        
                                        # 尝试查找"下一步"按钮并点击
                                        next_step_clicked = self.driver.execute_script("""
                                            // 查找并点击"下一步"按钮
                                            let buttons = document.querySelectorAll('button');
                                            for (let btn of buttons) {
                                                if (btn.textContent && btn.textContent.trim() === '下一步') {
                                                    console.log('找到下一步按钮');
                                                    btn.click();
                                                    return true;
                                                }
                                            }
                                            return false;
                                        """)
                                        
                                        if next_step_clicked:
                                            print("成功点击'下一步'按钮，跳过作品信息填写")
                                            time.sleep(2)
                                        else:
                                            # 尝试使用Selenium查找并点击"下一步"按钮
                                            try:
                                                next_btn = WebDriverWait(self.driver, 5).until(
                                                    EC.element_to_be_clickable((By.XPATH, "//button[text()='下一步']"))
                                                )
                                                next_btn.click()
                                                print("使用Selenium成功点击'下一步'按钮")
                                                time.sleep(2)
                                            except Exception as e:
                                                print(f"无法点击'下一步'按钮: {str(e)}")
                                except Exception as e:
                                    print(f"处理作品信息弹窗时出错: {str(e)}")
                                
                                break
                        else:
                            # 如果没有可见元素，尝试使用JavaScript点击第一个元素
                            print("没有找到可见的发布作品按钮，尝试使用JavaScript点击")
                            self.driver.execute_script("arguments[0].click();", publish_elements[0])
                            print("使用JavaScript点击发布作品按钮")
                            time.sleep(2)
                            
                            # 检测并处理作品信息弹窗(1/4步骤)
                            try:
                                print("检测作品信息填写弹窗(1/4步骤)...")
                                # 通过多种方式查找作品信息弹窗
                                work_info_dialog = self.driver.execute_script("""
                                    // 检测作品信息弹窗(1/4步骤)
                                    // 方法1: 查找包含"作品信息"的弹窗
                                    let dialogs = document.querySelectorAll('div[class*="modal"], div[class*="dialog"], div[class*="popup"]');
                                    for (let dialog of dialogs) {
                                        // 检查是否包含"作品信息"或"1/4"文本
                                        if (dialog.textContent && (
                                            dialog.textContent.includes('作品信息') || 
                                            dialog.textContent.includes('1/4') ||
                                            dialog.textContent.includes('下一步')
                                        )) {
                                            console.log('找到作品信息弹窗');
                                            return {found: true, type: '作品信息弹窗'};
                                        }
                                    }
                                    
                                    // 方法2: 查找特定标题文本
                                    let titles = document.querySelectorAll('div[class*="title"], h1, h2, h3, h4, h5');
                                    for (let title of titles) {
                                        if (title.textContent && title.textContent.trim() === '作品信息') {
                                            console.log('找到作品信息标题');
                                            return {found: true, type: '作品信息标题'};
                                        }
                                    }

                                    // 方法3: 查找步骤指示器(1/4)
                                    let steps = document.querySelectorAll('*');
                                    for (let step of steps) {
                                        if (step.textContent && step.textContent.trim() === '1/4') {
                                            console.log('找到步骤指示器(1/4)');
                                            return {found: true, type: '步骤指示器'};
                                        }
                                    }
                                    
                                    return {found: false};
                                """)
                                
                                if isinstance(work_info_dialog, dict) and work_info_dialog.get('found'):
                                    print(f"检测到作品信息弹窗 [{work_info_dialog.get('type', '未知类型')}]")
                                    
                                    # 尝试查找"下一步"按钮并点击
                                    next_step_clicked = self.driver.execute_script("""
                                        // 查找并点击"下一步"按钮
                                        let buttons = document.querySelectorAll('button');
                                        for (let btn of buttons) {
                                            if (btn.textContent && btn.textContent.trim() === '下一步') {
                                                console.log('找到下一步按钮');
                                                btn.click();
                                                return true;
                                            }
                                        }
                                        return false;
                                    """)
                                    
                                    if next_step_clicked:
                                        print("成功点击'下一步'按钮，跳过作品信息填写")
                                        time.sleep(2)
                                    else:
                                        # 尝试使用Selenium查找并点击"下一步"按钮
                                        try:
                                            next_btn = WebDriverWait(self.driver, 5).until(
                                                EC.element_to_be_clickable((By.XPATH, "//button[text()='下一步']"))
                                            )
                                            next_btn.click()
                                            print("使用Selenium成功点击'下一步'按钮")
                                            time.sleep(2)
                                        except Exception as e:
                                            print(f"无法点击'下一步'按钮: {str(e)}")
                            except Exception as e:
                                print(f"处理作品信息弹窗时出错: {str(e)}")
                    else:
                        # 尝试查找包含"发布"的按钮
                        publish_btns = self.driver.find_elements(By.XPATH, "//button[contains(text(), '发布')]")
                        if publish_btns:
                            for i, btn in enumerate(publish_btns):
                                try:
                                    print(f"找到发布按钮 {i+1}: {btn.tag_name}, 文本: {btn.text}, 可见: {btn.is_displayed()}")
                                except:
                                    print(f"找到发布按钮 {i+1}: 无法获取详细信息")
                            
                            # 点击第一个可见的按钮
                            for btn in publish_btns:
                                if btn.is_displayed():
                                    print(f"点击发布按钮: {btn.text}")
                                    btn.click()
                                    print("发布按钮点击成功")
                                    time.sleep(2)
                                    break
                            else:
                                # 尝试用JavaScript点击
                                self.driver.execute_script("arguments[0].click();", publish_btns[0])
                                print("使用JavaScript点击发布按钮")
                                time.sleep(2)
                                
                                # 检测并处理作品信息弹窗(1/4步骤)
                                try:
                                    print("检测作品信息填写弹窗(1/4步骤)...")
                                    work_info_dialog = self.driver.execute_script("""
                                        // 检测作品信息弹窗(1/4步骤)
                                        let found = false;
                                        
                                        // 检查是否有步骤指示器(1/4)
                                        let steps = document.querySelectorAll('*');
                                        for (let step of steps) {
                                            if (step.textContent && step.textContent.trim() === '1/4') {
                                                console.log('找到步骤指示器(1/4)');
                                                found = true;
                                                break;
                                            }
                                        }
                                        
                                        // 检查是否有"下一步"按钮
                                        if (!found) {
                                            let buttons = document.querySelectorAll('button');
                                            for (let btn of buttons) {
                                                if (btn.textContent && btn.textContent.trim() === '下一步') {
                                                    console.log('找到下一步按钮');
                                                    found = true;
                                                    break;
                                                }
                                            }
                                        }
                                        
                                        if (found) {
                                            // 尝试点击"下一步"按钮
                                            let next_clicked = false;
                                            let buttons = document.querySelectorAll('button');
                                            for (let btn of buttons) {
                                                if (btn.textContent && btn.textContent.trim() === '下一步') {
                                                    btn.click();
                                                    console.log('已点击下一步按钮');
                                                    next_clicked = true;
                                                    break;
                                                }
                                            }
                                            return {found: true, clicked: next_clicked};
                                        }
                                        
                                        return {found: false};
                                    """)
                                    
                                    if isinstance(work_info_dialog, dict) and work_info_dialog.get('found'):
                                        print(f"检测到作品信息弹窗，点击结果: {work_info_dialog.get('clicked', False)}")
                                        
                                        # 如果JavaScript点击失败，尝试使用Selenium
                                        if not work_info_dialog.get('clicked'):
                                            try:
                                                next_btn = WebDriverWait(self.driver, 5).until(
                                                    EC.element_to_be_clickable((By.XPATH, "//button[text()='下一步']"))
                                                )
                                                next_btn.click()
                                                print("使用Selenium成功点击'下一步'按钮")
                                            except Exception as e:
                                                print(f"无法点击'下一步'按钮: {str(e)}")
                                        
                                        time.sleep(2)
                                except Exception as e:
                                    print(f"处理作品信息弹窗时出错: {str(e)}")
                        else:
                            # 尝试通过发布视频图标点击
                            print("尝试查找发布视频图标...")
                            upload_icons = self.driver.find_elements(By.CSS_SELECTOR, "i[class*='upload'], svg[class*='upload'], [aria-label*='上传'], [aria-label*='发布']")
                            if upload_icons:
                                for i, icon in enumerate(upload_icons):
                                    try:
                                        print(f"找到上传图标 {i+1}: {icon.tag_name}, 可见: {icon.is_displayed()}")
                                    except:
                                        print(f"找到上传图标 {i+1}: 无法获取详细信息")
                                
                                # 点击第一个可见的图标
                                for icon in upload_icons:
                                    if icon.is_displayed():
                                        print("点击上传图标")
                                        icon.click()
                                        print("上传图标点击成功")
                                        time.sleep(2)
                                        break
                                else:
                                    # 尝试用JavaScript点击
                                    self.driver.execute_script("arguments[0].click();", upload_icons[0])
                                    print("使用JavaScript点击上传图标")
                                    time.sleep(2)
                                    
                                    # 检测并处理作品信息弹窗(1/4步骤)
                                    try:
                                        print("检测作品信息填写弹窗(1/4步骤)...")
                                        # 通过脚本检测弹窗并处理
                                        found_and_handled = self.driver.execute_script("""
                                            // 检测并处理作品信息弹窗(1/4步骤)
                                            function detectWorkInfoDialog() {
                                                // 查找可能的弹窗内容
                                                const indicators = ['作品信息', '1/4', '下一步'];
                                                let dialogFound = false;
                                                
                                                // 检查所有可能包含这些指标的元素
                                                for (const indicator of indicators) {
                                                    const elements = document.querySelectorAll('*');
                                                    for (const elem of elements) {
                                                        if (elem.textContent && elem.textContent.includes(indicator)) {
                                                            dialogFound = true;
                                                            console.log(`找到弹窗指标: ${indicator}`);
                                                            break;
                                                        }
                                                    }
                                                    if (dialogFound) break;
                                                }
                                                
                                                if (dialogFound) {
                                                    // 尝试查找并点击"下一步"按钮
                                                    const buttons = document.querySelectorAll('button');
                                                    for (const btn of buttons) {
                                                        if (btn.textContent && btn.textContent.trim() === '下一步') {
                                                            try {
                                                                btn.click();
                                                                console.log('成功点击"下一步"按钮');
                                                                return {found: true, handled: true};
                                                            } catch (e) {
                                                                console.error('点击"下一步"按钮时出错:', e);
                                                                return {found: true, handled: false, error: e.toString()};
                                                            }
                                                        }
                                                    }
                                                    return {found: true, handled: false, error: '未找到"下一步"按钮'};
                                                }
                                                
                                                return {found: false};
                                            }
                                            
                                            return detectWorkInfoDialog();
                                        """)
                                        
                                        if isinstance(found_and_handled, dict) and found_and_handled.get('found'):
                                            if found_and_handled.get('handled'):
                                                print("成功检测并处理了作品信息弹窗")
                                                time.sleep(2)
                                            else:
                                                print(f"检测到作品信息弹窗但处理失败: {found_and_handled.get('error', '未知错误')}")
                                                # 尝试使用Selenium
                                                try:
                                                    next_btn = WebDriverWait(self.driver, 5).until(
                                                        EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '下一步')]"))
                                                    )
                                                    next_btn.click()
                                                    print("使用Selenium成功点击'下一步'按钮")
                                                    time.sleep(2)
                                                except Exception as e:
                                                    print(f"Selenium尝试点击'下一步'按钮失败: {str(e)}")
                                    except Exception as e:
                                        print(f"处理作品信息弹窗时出错: {str(e)}")
                            else:
                                print(f"未找到发布作品按钮或相关元素，尝试第 {attempt+1}/{max_retries} 次")
                                if attempt < max_retries - 1:
                                    time.sleep(2)
                                    self.driver.refresh()
                                    time.sleep(3)
                                    continue
                                else:
                                    raise Exception("无法找到发布作品按钮或相关元素")
                    
                    # 如果成功点击，跳出循环
                    break
                    
                except Exception as e:
                    print(f"点击发布作品时出错 (尝试 {attempt+1}/{max_retries}): {str(e)}")
                    if attempt < max_retries - 1:
                        time.sleep(2)
                        self.driver.refresh()
                        time.sleep(3)
                    else:
                        raise Exception(f"点击发布作品按钮失败: {str(e)}")
            
            # 等待上传界面出现
            print("等待上传界面出现...")
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='file'], [accept*='video']"))
            )
            
            # 在上传界面中查找文件输入元素
            print("查找文件输入元素...")
            file_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='file'], [accept*='video']")
            if not file_inputs:
                raise Exception("无法找到文件上传输入框")
            
            print(f"找到 {len(file_inputs)} 个文件输入元素")
            file_input = file_inputs[0]
            
            # 确保文件路径是绝对路径
            abs_video_path = os.path.abspath(video_path)
            print(f"准备上传文件: {abs_video_path}")
            
            # 确保文件输入元素可见
            self.driver.execute_script(
                "arguments[0].style.display = 'block'; arguments[0].style.visibility = 'visible';",
                file_input
            )
            
            # 发送文件路径
            file_input.send_keys(abs_video_path)
            print("已发送文件路径")
            
            if status_callback:
                status_callback(f"视频 {self.current_video} 上传中，请等待...")
            
            # 等待上传进度完成
            print("等待上传完成...")
            
            # 等待上传成功的标志
            print("等待上传成功标志...")
            
            # 等待重新上传按钮或其他成功标志出现
            max_wait_time = 300  # 最多等待5分钟
            check_interval = 3  # 每3秒检查一次
            waited_time = 0
            upload_success = False
            
            while waited_time < max_wait_time and not upload_success:
                try:
                    # 检查并处理可能出现的弹窗
                    if waited_time % 10 == 0:  # 每10秒检查一次弹窗
                        if self.handle_popups():
                            print("成功处理弹窗，继续等待上传完成")
                    
                    # 首先检查是否已进入预览界面（这是最明确的成功标志）
                    preview_elements = self.driver.find_elements(By.XPATH, 
                        "//*[contains(text(), '预览封面') or contains(text(), '预览作品') or contains(text(), '编辑画布') or contains(text(), '发布') or contains(text(), '添加封面')]")
                    
                    if preview_elements and any(elem.is_displayed() for elem in preview_elements):
                        print("已进入预览界面，检测到以下元素：")
                        for elem in preview_elements:
                            if elem.is_displayed():
                                try:
                                    print(f"- {elem.text}")
                                except:
                                    print("- [无法获取元素文本]")
                        print("确认上传成功！")
                        upload_success = True
                        break
                    
                    # 检查是否出现"重新上传"按钮
                    reupload_btns = self.driver.find_elements(By.XPATH, "//button[contains(text(), '重新上传')]")
                    if reupload_btns and any(btn.is_displayed() for btn in reupload_btns):
                        print("找到'重新上传'按钮，上传成功！")
                        upload_success = True
                        break
                    
                    # 检查其他可能的成功标志
                    success_elements = self.driver.find_elements(By.CSS_SELECTOR, "[class*='success'], [class*='完成']")
                    if success_elements and any(elem.is_displayed() for elem in success_elements):
                        print("找到成功标志元素，上传成功！")
                        upload_success = True
                        break
                    
                    # 检查是否有"继续上传"或"发布更多"按钮
                    continue_btns = self.driver.find_elements(By.XPATH, 
                        "//button[contains(text(), '继续上传') or contains(text(), '发布更多') or contains(text(), '确定')]")
                    if continue_btns and any(btn.is_displayed() for btn in continue_btns):
                        print("找到'继续上传'或'发布更多'按钮，上传成功！")
                        upload_success = True
                        break
                        
                    # 如果没有找到成功标志，继续等待
                    time.sleep(check_interval)
                    waited_time += check_interval
                    if waited_time % 30 == 0:  # 每30秒输出一次状态
                        remaining = max_wait_time - waited_time
                        print(f"仍在等待上传完成，剩余时间: {remaining}秒")
                        # 输出当前页面状态
                        try:
                            visible_elements = [elem for elem in self.driver.find_elements(By.XPATH, "//*[string-length(text()) > 0]") 
                                             if elem.is_displayed()][:5]  # 只获取前5个可见元素
                            print("当前页面可见元素:")
                            for elem in visible_elements:
                                try:
                                    print(f"- {elem.text}")
                                except:
                                    continue
                        except:
                            pass
                        
                except Exception as e:
                    print(f"检查上传状态时出错: {str(e)}")
                    time.sleep(check_interval)
                    waited_time += check_interval
            
            if upload_success:
                print("上传成功！正在处理视频描述和封面...")
                
                # 处理可能出现的弹窗
                self.handle_popups()
                
                # 先处理封面，后处理描述
                # 上传自定义封面
                cover_result = self.upload_cover()
                if cover_result == "RETRY_NEEDED":
                    print("封面上传过程中需要重试整个上传流程")
                    # 重置重试计数器以防止无限递归
                    if self._retry_count >= 3:
                        print("已达到最大重试次数(3)，继续尝试完成当前上传")
                    else:
                        return "RETRY_NEEDED"
                elif cover_result:
                    print("封面处理完成")
                else:
                    print("封面处理失败，但不影响上传")
                
                # 处理可能出现的弹窗
                self.handle_popups()
                
                # 填写视频描述
                if self.fill_video_description():
                    print("视频描述处理完成")
                else:
                    print("视频描述处理失败，但不影响上传")
                
                # 最终检查和处理弹窗
                self.handle_popups()
                
                # 查找并点击最终发布按钮
                try:
                    print("查找最终发布按钮...")
                    final_buttons = self.driver.find_elements(By.XPATH, 
                        "//button[contains(text(), '发布') or contains(text(), '完成') or contains(text(), '确定') or contains(text(), '确认')]")
                    
                    # 显示找到的按钮
                    if final_buttons:
                        print(f"找到 {len(final_buttons)} 个可能的发布按钮")
                        for btn in final_buttons:
                            if btn.is_displayed():
                                try:
                                    print(f"可见按钮: {btn.text}")
                                except:
                                    print("按钮文本无法获取")
                        
                        # 点击第一个可见的按钮
                        for btn in final_buttons:
                            if btn.is_displayed() and btn.is_enabled():
                                try:
                                    print(f"点击发布按钮: {btn.text}")
                                    btn.click()
                                    print("发布按钮点击成功")
                                    time.sleep(5)  # 等待点击响应
                                    break
                                except Exception as e:
                                    print(f"点击按钮失败: {str(e)}")
                                    # 尝试JavaScript点击
                                    try:
                                        self.driver.execute_script("arguments[0].click();", btn)
                                        print("使用JavaScript点击发布按钮")
                                        time.sleep(5)
                                        break
                                    except:
                                        pass
                except Exception as e:
                    print(f"查找发布按钮时出错: {str(e)}")
                
                # 等待发布完成
                print("等待预览作品界面出现...")
                max_attempts = 200  # 最多等待200次，每次2秒
                for attempt in range(max_attempts):
                    try:
                        # 检查预览作品标签
                        preview_tab = self.driver.execute_script("""
                            const tabs = document.querySelectorAll('span._tab_1eni7_101');
                            for (const tab of tabs) {
                                if (tab.textContent === '预览作品' && 
                                    tab.classList.contains('_active_1eni7_104')) {
                                    return true;
                                }
                            }
                            return false;
                        """)
                        
                        if preview_tab:
                            print("检测到预览作品标签")
                            # 查找并点击发布按钮
                            publish_clicked = self.driver.execute_script("""
                                const buttons = document.querySelectorAll('div._button_3a3lq_1._button-primary_3a3lq_60');
                                for (const btn of buttons) {
                                    if (btn.textContent.includes('发布')) {
                                        btn.click();
                                        return true;
                                    }
                                }
                                return false;
                            """)
                            
                            if publish_clicked:
                                print("成功点击发布按钮")
                                time.sleep(random.uniform(1, 3))  # 添加1-3秒随机延时
                                break
                            else:
                                print("未找到发布按钮，继续等待...")
                        else:
                            print(f"尝试 {attempt + 1}/{max_attempts}: 未检测到预览作品标签")
                            time.sleep(2)
                    except Exception as e:
                        print(f"检查预览作品标签时出错: {str(e)}")
                        time.sleep(2)
                
                # 处理可能的弹窗或确认对话框
                popup_result = self.handle_popups()
                if popup_result == "SWITCH_ACCOUNT":
                    print("检测到账号上传限制，立即结束视频上传流程")
                    if status_callback:
                        status_callback("检测到账号已达上传限制，停止上传")
                    self.stop_flag = True
                    # 尝试回到创作者平台
                    try:
                        self.driver.get(self.creator_url)
                    except Exception:
                        pass
                    return "SWITCH_ACCOUNT"  # 返回特殊状态码，通知外部需要切换账号
                
                print("发布流程已完成")
                time.sleep(random.uniform(1, 3))  # 添加1-3秒随机延时
                
                # 额外再次检查是否有上传限制提示
                try:
                    limit_messages = self.driver.find_elements(By.XPATH, "//*[contains(text(), '已达今日上传作品上限')]")
                    if limit_messages and any(msg.is_displayed() for msg in limit_messages):
                        print("检测到上传限制提示: 已达今日上传作品上限")
                        # 更新账号上传计数
                        if self.current_account:
                            self.current_account.today_uploads = self.current_account.daily_upload_count
                        self.stop_flag = True
                        return "SWITCH_ACCOUNT"
                except Exception:
                    pass
                
                # 检查是否跳转到视频管理页面
                print("检查是否跳转到视频管理页面...")
                max_wait_time = 60  # 最多等待60秒
                check_interval = 2  # 每2秒检查一次
                waited_time = 0
                management_page_detected = False
                
                while waited_time < max_wait_time:
                    try:
                        # 先检查是否有上传限制提示
                        limit_messages = self.driver.find_elements(By.XPATH, "//*[contains(text(), '已达今日上传作品上限')]")
                        if limit_messages and any(msg.is_displayed() for msg in limit_messages):
                            print("检测到上传限制提示: 已达今日上传作品上限")
                            # 更新账号上传计数
                            if self.current_account:
                                self.current_account.today_uploads = self.current_account.daily_upload_count
                            self.stop_flag = True
                            return "SWITCH_ACCOUNT"
                            
                        # 检查是否存在"视频管理"的h2标题
                        h2_elements = self.driver.find_elements(By.XPATH, "//h2[text()='视频管理']")
                        if h2_elements and any(elem.is_displayed() for elem in h2_elements):
                            print("检测到视频管理页面，确认发布成功！")
                            time.sleep(random.uniform(1, 3))  # 添加1-3秒随机延时
                            management_page_detected = True
                            break
                        
                        # 如果找不到h2元素，尝试其他可能的元素
                        management_elements = self.driver.find_elements(By.XPATH, 
                            "//*[contains(text(), '视频管理') and (self::h2 or self::div or self::span)]")
                        if management_elements and any(elem.is_displayed() for elem in management_elements):
                            print("检测到包含'视频管理'的元素，确认发布成功！")
                            time.sleep(random.uniform(1, 3))  # 添加1-3秒随机延时
                            management_page_detected = True
                            break
                            
                        time.sleep(check_interval)
                        waited_time += check_interval
                        if waited_time % 10 == 0:  # 每10秒输出一次等待状态
                            print(f"正在等待检测视频管理页面，已等待 {waited_time} 秒...")
                    except Exception as e:
                        print(f"检查页面状态时出错: {str(e)}")
                        time.sleep(check_interval)
                        waited_time += check_interval
                
                # 如果检测到视频管理页面，删除已上传的视频文件
                if management_page_detected:
                    print("视频确认发布成功，准备删除本地视频文件...")
                    if self.current_account:
                        self.current_account.today_uploads += 1
                    try:
                        # 删除已上传的视频文件
                        os.remove(video_path)
                        print(f"成功删除已上传的视频文件: {video_path}")
                    except Exception as e:
                        print(f"删除视频文件失败: {str(e)}")
                        # 文件删除失败不影响上传结果
                else:
                    print("未检测到视频管理页面，但视频可能已成功发布")
                
                if status_callback:
                    if management_page_detected:
                        status_callback(f"视频 {self.current_video} 上传成功并已删除本地文件！")
                    else:
                        status_callback(f"视频 {self.current_video} 上传成功！")
                # 重置重试计数器
                self._retry_count = 0
                return True
            else:
                # 上传超时，删除视频文件
                try:
                    os.remove(video_path)
                    print(f"上传超时，已删除视频文件: {video_path}")
                    if status_callback:
                        status_callback(f"上传超时，已删除视频文件: {os.path.basename(video_path)}")
                except Exception as e:
                    print(f"删除视频文件失败: {str(e)}")
                    if status_callback:
                        status_callback(f"删除视频文件失败: {str(e)}")
                raise Exception("上传超时，未检测到成功标志")
     
        except Exception as e:
            print(f"上传视频时出错: {str(e)}")
            # 删除视频文件（出现其他错误）
            try:
                os.remove(video_path)
                print(f"上传出错，已删除视频文件: {video_path}")
                if status_callback:
                    status_callback(f"上传出错，已删除视频文件: {os.path.basename(video_path)}")
            except Exception as del_e:
                print(f"删除视频文件失败: {str(del_e)}")
                if status_callback:
                    status_callback(f"删除视频文件失败: {str(del_e)}")
            return False

    def run(self, video_folder, status_callback=None, video_description=""):
        """运行完整的上传流程
        
        Args:
            video_folder: 视频文件夹路径
            status_callback: 状态回调函数
            video_description: 视频描述文本
        """
        self.is_running = True
        self.stop_flag = False
        self.video_description = video_description  # 保存视频描述
        self.status_callback = status_callback  # 设置状态回调
        
        def log_status(message):
            """记录状态到控制台和状态回调"""
            print(message)
            if status_callback:
                status_callback(message)
        
        try:
            log_status("启动Chrome浏览器...")
            self.setup_browser()
            log_status("Chrome浏览器启动成功，正在访问快手创作者平台...")
            
            try:
                self.driver.get(self.creator_url)
                log_status("成功访问快手创作者平台")
            except Exception as e:
                log_status(f"访问快手创作者平台失败: {str(e)}")
                log_status("尝试访问快手主页...")
                self.driver.get("https://www.kuaishou.com")
                time.sleep(3)
                log_status("正在重定向到创作者平台...")
                self.driver.get(self.creator_url)
            
            # 尝试加载cookies自动登录
            login_success = False
            try:
                # 检查是否有保存的cookies
                if os.path.exists(self.config.get_cookies_path(self.current_account.name)):
                    log_status("尝试使用已保存的登录信息...")
                    self.driver.get("https://www.kuaishou.com")
                    time.sleep(2)
                    
                    # 加载cookies
                    self.load_cookies()
                    log_status("已加载保存的登录信息")
                    
                    # 重新访问创作者平台
                    log_status("正在访问创作者平台...")
                    self.driver.get(self.creator_url)
                    time.sleep(5)  # 等待页面完全加载
                    
                    # 简单检查是否登录成功
                    page_source = self.driver.page_source
                    if "发布作品" in page_source or "数据中心" in page_source:
                        log_status("已通过cookies成功登录")
                        login_success = True
                    else:
                        log_status("自动登录失败，需要重新登录")
                else:
                    log_status("没有找到保存的登录信息，需要登录")
            except Exception as e:
                log_status(f"尝试自动登录时出错: {str(e)}")
            
            # 如果自动登录失败，进行扫码登录
            if not login_success:
                log_status("准备扫码登录...")
                self.driver.get(self.creator_url)  # 重新加载页面
                time.sleep(2)
                
                if self.wait_for_login(status_callback):
                    login_success = True
                    log_status("登录成功！开始处理视频上传...")
                else:
                    raise Exception("登录失败，无法继续上传")
            
            # 确保已成功登录后再继续
            if not login_success:
                log_status("账号未成功登录，上传任务已取消")
                return
            
            # 检查一下页面状态，确保登录后的页面已正确加载
            try:
                self.driver.get(self.creator_url)
                time.sleep(3)
                log_status("正在确认页面状态...")
                
                # 查找页面上的关键元素，确认是否真正登录成功
                page_source = self.driver.page_source
                if "发布作品" in page_source or "数据中心" in page_source:
                    log_status("已确认登录状态，开始处理视频上传...")
                else:
                    log_status("警告：页面状态异常，但将继续尝试上传")
            except Exception as e:
                log_status(f"确认页面状态时出错: {str(e)}")
            
            log_status("开始处理视频上传...")
            
            # 准备视频文件列表
            video_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']
            video_files = []
            
            log_status(f"正在扫描文件夹: {video_folder}")
            for file in os.listdir(video_folder):
                if any(file.lower().endswith(ext) for ext in video_extensions):
                    video_files.append(os.path.join(video_folder, file))
            
            if not video_files:
                log_status("错误：所选文件夹中没有找到视频文件")
                return
            
            log_status(f"找到 {len(video_files)} 个视频文件，准备上传")
            
            # 记录视频描述和封面状态
            if self.video_description:
                log_status(f"将使用以下视频描述: {self.video_description}")
            else:
                log_status("未设置视频描述")
                
            if self.use_custom_cover and self.cover_path:
                log_status(f"将使用自定义封面: {os.path.basename(self.cover_path)}")
            else:
                log_status("未使用自定义封面")
                
            success_count = 0
            fail_count = 0
            
            # 确保today_uploads初始化正确
            if hasattr(self, 'current_account') and self.current_account:
                if not hasattr(self.current_account, 'today_uploads') or not isinstance(self.current_account.today_uploads, int):
                    self.current_account.today_uploads = 0
            else:
                # 如果没有current_account，创建一个临时的
                class TempAccount:
                    pass
                self.current_account = TempAccount()
                self.current_account.today_uploads = 0
                self.current_account.daily_upload_count = 90
                
            # 重置成功上传计数，确保每个账号独立计数
            success_count = 0
            fail_count = 0
            
            for i, video_path in enumerate(video_files):
                if self.stop_flag:
                    log_status("操作已被用户停止")
                    break
                    
                # 使用当前账号的today_uploads作为视频序号，与上传后的显示保持一致
                if hasattr(self, 'current_account') and self.current_account and hasattr(self.current_account, 'today_uploads'):
                    next_video_number = self.current_account.today_uploads
                else:
                    next_video_number = success_count
                log_status(f"正在处理第 {next_video_number}/{self.current_account.daily_upload_count} 个视频: {os.path.basename(video_path)}...")
                
                try:
                    # 如果已标记停止，跳过此次迭代
                    if self.stop_flag:
                        log_status("操作已被停止，跳过当前视频")
                        continue
                        
                    # 确保回到创作者平台首页
                    log_status("正在回到创作者平台首页...")
                    self.driver.get(self.creator_url)
                    time.sleep(5)  # 增加等待时间，确保页面完全加载
                    
                    # 检查页面是否正确加载
                    try:
                        page_title = self.driver.title
                        log_status(f"当前页面标题: {page_title}")
                        
                        # 确认页面源码中包含期望的关键词
                        page_source = self.driver.page_source
                        if "发布作品" not in page_source and "创作服务" not in page_source:
                            log_status("警告: 页面可能未正确加载，尝试重新加载...")
                            self.driver.refresh()
                            time.sleep(5)
                    except Exception as e:
                        log_status(f"检查页面时出错: {str(e)}")
                    
                    # 上传视频
                    log_status(f"开始上传视频: {os.path.basename(video_path)}")
                    upload_result = self.upload_video(video_path, status_callback)
                    
                    if upload_result == "RETRY_NEEDED":
                        print("需要重新执行上传流程")
                        # 完全按照第一次发布的流程重新执行
                        log_status("重新执行完整的发布流程...")
                        
                        # 确保页面状态正确
                        try:
                            self.driver.get(self.creator_url)
                            time.sleep(3)
                            log_status("正在确认页面状态...")
                            
                            # 查找页面上的关键元素，确认是否真正登录成功
                            page_source = self.driver.page_source
                            if "发布作品" not in page_source and "创作服务" not in page_source:
                                log_status("页面状态异常，尝试重新加载...")
                                self.driver.refresh()
                                time.sleep(5)
                        except Exception as e:
                            log_status(f"确认页面状态时出错: {str(e)}")
                        
                        # 然后再次尝试上传
                        log_status("重新开始上传视频...")
                        return self.upload_video(video_path, status_callback)
                    elif upload_result == "SWITCH_ACCOUNT":
                        log_status(f"账号 {self.current_account.name} 已达上传限制，需要切换到其他账号")
                        # 重置错误重试计数
                        if hasattr(self, '_error_retry_count'):
                            delattr(self, '_error_retry_count')
                        return "SWITCH_ACCOUNT"  # 返回特殊状态码，通知外部需要切换账号
                    
                    if upload_result:
                        log_status(f"视频 {os.path.basename(video_path)} 上传成功")
                        
                        # 检查账号当前上传数量是否达到限制
                        if hasattr(self, 'current_account') and self.current_account:
                            # 确保today_uploads是有效数字，但不在这里加1，而是在upload_video方法中加1
                            if not hasattr(self.current_account, 'today_uploads') or not isinstance(self.current_account.today_uploads, int):
                                self.current_account.today_uploads = 0
                                
                            # 确保daily_upload_count是有效数字
                            if not hasattr(self.current_account, 'daily_upload_count') or not isinstance(self.current_account.daily_upload_count, int) or self.current_account.daily_upload_count <= 0:
                                self.current_account.daily_upload_count = 90  # 设置默认值
                            
                            # 使用today_uploads更新success_count，确保一致性
                            success_count = self.current_account.today_uploads
                                
                            # 使用当前账号的today_uploads作为已上传数量，确保显示正确
                            log_status(f"账号 {self.current_account.name} 今日已上传: {self.current_account.today_uploads}/{self.current_account.daily_upload_count} 个视频")
                            
                            # 如果达到上传上限，抛出异常中断处理
                            if self.current_account.today_uploads >= self.current_account.daily_upload_count:
                                log_status(f"账号 {self.current_account.name} 已达到今日上传上限（{self.current_account.daily_upload_count}个），将停止上传")
                                self.stop_flag = True  # 设置停止标志，确保不会继续处理后续视频
                                raise AccountLimitReachedException("今日上传次数已达上限，需停止上传")
                        
                        # 确保回到创作者平台
                        self.driver.get(self.creator_url)
                        time.sleep(5)
                    else:
                        log_status(f"视频 {os.path.basename(video_path)} 上传失败")
                        fail_count += 1
                        
                        # 上传失败后尝试回到创作者平台
                        try:
                            self.driver.get(self.creator_url)
                            time.sleep(5)
                        except:
                            pass
                except Exception as e:
                    # 检查是否是账号达到上传上限的错误
                    if isinstance(e, AccountLimitReachedException):
                        log_status(f"上传过程中发生错误: {str(e)}")
                        log_status("检测到账号已达今日上传上限，停止当前账号上传")
                        # 更新账号计数，但不要累加前一个账号的数据
                        if self.current_account:
                            self.current_account.today_uploads = self.current_account.daily_upload_count
                        # 重置计数器，确保切换到新账号后从0开始计数
                        success_count = 0
                        fail_count = 0
                        return "SWITCH_ACCOUNT"  # 返回特殊状态码，通知外部需要切换账号
                    else:
                        log_status(f"上传过程中发生错误: {str(e)}")
                        fail_count += 1
                    
                    # 尝试恢复到创作者平台
                    try:
                        self.driver.get(self.creator_url)
                        time.sleep(5)
                    except:
                        pass
                
                # 如果已设置停止标志，不再继续后续处理
                if self.stop_flag:
                    log_status("操作已被停止，不再处理后续视频")
                    break
                    
                # 上传后等待随机时间
                wait_time = random.uniform(4, 8)
                log_status(f"随机等待{wait_time:.1f}秒后继续下一个视频...")
                time.sleep(wait_time)
                
                # 处理可能的弹窗
                try:
                    popup_result = self.handle_popups()
                    if popup_result == "SWITCH_ACCOUNT":
                        log_status("检测到账号上传限制，立即停止当前账号上传")
                        # 重置计数器，确保切换到新账号后从0开始计数
                        success_count = 0
                        fail_count = 0
                        return "SWITCH_ACCOUNT"  # 返回特殊状态码，通知外部需要切换账号
                    elif popup_result:
                        log_status("成功处理弹窗")
                except Exception as e:
                    log_status(f"处理弹窗时出错: {str(e)}")
            
            log_status(f"任务完成！成功上传 {success_count} 个视频，失败 {fail_count} 个视频")
        
        except Exception as e:
            log_status(f"发生错误: {str(e)}")
            # 记录详细错误信息
            try:
                with open("error_log.txt", "a", encoding="utf-8") as f:
                    f.write(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] 错误: {str(e)}\n")
                    import traceback
                    traceback.print_exc(file=f)
                log_status("错误详情已记录到 error_log.txt")
            except Exception:
                pass
        finally:
            self.is_running = False
            if self.driver:
                log_status("正在关闭浏览器...")
                try:
                    self.driver.quit()
                    log_status("浏览器已关闭")
                except Exception as e:
                    log_status(f"关闭浏览器时出错: {str(e)}")
                    try:
                        os.system('taskkill /f /im chrome.exe')
                        os.system('taskkill /f /im chromedriver.exe')
                        log_status("已强制关闭Chrome进程")
                    except Exception:
                        pass

    def stop(self):
        """停止上传过程"""
        self.stop_flag = True
        self.user_stopped = True  # 标记为用户手动停止

    def fill_video_description(self):
        """填写视频描述，模拟人工输入"""
        try:
            if not self.video_description:
                print("没有设置视频描述，跳过")
                return True

            print("正在填写视频描述...")
            
            # 首先通过文本提示"0/500"找到相关元素，再定位描述输入框
            try:
                # 查找包含"0/500"或类似文本的计数器元素
                text_tip = self.driver.find_element(By.CSS_SELECTOR, "span._text-tip_2klkp_250")
                print(f"找到文本提示元素: {text_tip.text}")
                
                # 直接使用文本提示元素作为定位点
                description_input = self.driver.find_element(By.CSS_SELECTOR, "div[contenteditable='true']")
                print("通过计数器元素找到描述输入框")
            except Exception as e:
                print(f"通过计数器元素查找失败: {str(e)}")
                
                # 备用方法：直接寻找可编辑元素
                try:
                    description_input = self.driver.find_element(By.CSS_SELECTOR, 
                        "div[contenteditable='true']")
                    print("直接找到可编辑元素")
                except Exception as e:
                    print(f"无法找到任何可编辑元素: {str(e)}")
                    return False
            
            # 清除默认内容
            try:
                description_input.clear()
                print("已清除输入框默认内容")
            except Exception as e:
                print(f"清除输入框内容失败: {str(e)}")
                try:
                    self.driver.execute_script("arguments[0].innerHTML = '';", description_input)
                    print("使用JavaScript清除输入框内容")
                except Exception as e:
                    print(f"使用JavaScript清除内容也失败: {str(e)}")
            
            # 模拟人工输入
            print(f"开始模拟人工输入描述内容: {self.video_description}")
            
            # 创建ActionChains实例用于模拟键盘输入
            actions = ActionChains(self.driver)
            
            # 将文本分成小块，模拟人类输入习惯
            text_chunks = self.video_description.split()
            for i, chunk in enumerate(text_chunks):
                # 每个词之间添加空格（除了第一个词）
                if i > 0:
                    description_input.send_keys(Keys.SPACE)
                    time.sleep(random.uniform(0.1, 0.3))
                
                # 模拟不同速度的输入
                for char in chunk:
                    # 随机决定是否使用退格键来模拟打字错误（5%的概率）
                    if random.random() < 0.05:
                        description_input.send_keys(char)
                        time.sleep(random.uniform(0.1, 0.3))
                        description_input.send_keys(Keys.BACKSPACE)
                        time.sleep(random.uniform(0.2, 0.4))
                        description_input.send_keys(char)
                    else:
                        description_input.send_keys(char)
                    
                    # 为每个字符添加随机延迟，模拟真实打字速度
                    time.sleep(random.uniform(0.05, 0.2))
                
                # 词与词之间的停顿
                time.sleep(random.uniform(0.2, 0.5))
                
                # 随机暂停一下（模拟思考，10%的概率）
                if random.random() < 0.1:
                    time.sleep(random.uniform(0.8, 1.5))
            
            # 最后的停顿
            time.sleep(random.uniform(0.5, 1.0))
            
            # 触发change和input事件以确保内容更新
            try:
                self.driver.execute_script("""
                    var event = new Event('change', { bubbles: true });
                    arguments[0].dispatchEvent(event);
                    var inputEvent = new Event('input', { bubbles: true });
                    arguments[0].dispatchEvent(inputEvent);
                """, description_input)
                print("成功触发change和input事件")
            except Exception as e:
                print(f"触发事件失败: {str(e)}")
            
            print("视频描述填写完成")
            return True
            
        except Exception as e:
            print(f"填写视频描述时出错: {str(e)}")
            return False

    def try_direct_start(self):
        """尝试直接使用本地chromedriver启动浏览器"""
        try:
            print("尝试直接使用本地chromedriver启动...")
            
            # 检查chromedriver是否存在
            driver_dir = os.path.join(self.config.cache_dir, "chromedriver")
            driver_path = os.path.join(driver_dir, "chromedriver.exe")
            
            if not os.path.exists(driver_path):
                # 尝试查找系统中的chromedriver
                possible_paths = [
                    os.path.join(os.path.dirname(self.chrome_path), "chromedriver.exe"),
                    os.path.join(os.environ.get('LOCALAPPDATA', ''), "chromedriver.exe"),
                    os.path.join(os.environ.get('PROGRAMFILES', ''), "chromedriver.exe"),
                    os.path.join(os.environ.get('PROGRAMFILES(X86)', ''), "chromedriver.exe")
                ]
                
                for path in possible_paths:
                    if os.path.exists(path):
                        driver_path = path
                        print(f"找到系统chromedriver: {driver_path}")
                        break
                else:
                    print("未找到可用的chromedriver，跳过直接启动")
                    return False
            
            print(f"使用chromedriver: {driver_path}")
            
            # 创建用户数据目录
            user_data_dir = os.path.join(self.config.cache_dir, "chrome_user_data")
            os.makedirs(user_data_dir, exist_ok=True)
            
            # 创建Chrome选项
            from selenium import webdriver
            from selenium.webdriver.chrome.service import Service
            
            options = webdriver.ChromeOptions()
            options.binary_location = self.chrome_path
            options.add_argument(f"--user-data-dir={user_data_dir}")
            options.add_argument("--no-first-run")
            options.add_argument("--no-default-browser-check")
            options.add_argument("--disable-extensions")
            options.add_argument("--window-size=1920,1080")
            options.add_argument("--disable-gpu")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-web-security")
            options.add_argument("--disable-features=IsolateOrigins,site-per-process")
            
            # 创建Service对象
            service = Service(executable_path=driver_path)
            service.creation_flags = subprocess.CREATE_NO_WINDOW
            
            # 启动浏览器
            self.driver = webdriver.Chrome(service=service, options=options)
            
            # 配置浏览器
            self.driver.set_page_load_timeout(20)
            self.driver.set_script_timeout(20)
            self.driver.set_window_size(1920, 1080)
            
            print("直接启动Chrome成功！")
            return True
            
        except Exception as e:
            print(f"直接启动失败: {str(e)}")
            # 如果driver已创建但出错，确保关闭
            if hasattr(self, 'driver') and self.driver:
                try:
                    self.driver.quit()
                    self.driver = None
                except:
                    pass
            return False

    def update_status(self, message):
        """更新状态信息，并通过状态回调函数传递"""
        print(message)
        if self.status_callback:
            self.status_callback(message)
        return True

    def detect_video_orientation(self, video_path):
        """检测视频是横屏还是竖屏
        Returns:
            str: 'horizontal' 或 'vertical'
        """
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return 'horizontal'  # 默认横屏
            
            width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
            height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
            cap.release()
            
            return 'horizontal' if width > height else 'vertical'
        except Exception as e:
            print(f"检测视频方向时出错: {str(e)}")
            return 'horizontal'  # 出错时默认横屏


class KuaishouUploaderApp:
    def __init__(self, root):
        self.root = root
        self.root.title("快手视频批量上传工具")
        self.root.geometry("1260x800")
        
        # 使用全局配置
        self.config = app_config
        
        # 创建并配置上传器
        self.uploader = KuaishouUploader()
        self.uploader.cache_dir = self.config.cache_dir  # 设置缓存目录
        self.upload_thread = None
        
        self.create_widgets()
        self.detect_chrome()
        
        # 加载保存的配置
        self.load_saved_config()
        
        # 注册窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def load_saved_config(self):
        """加载保存的配置"""
        try:
            config_data = self.config.load_accounts_config()
            if config_data:
                for account_data in config_data:
                    # 创建账号对象
                    account = self.uploader.add_account(
                        name=account_data.get('name', ''),
                        cookies_file=None,  # 使用默认命名规则
                        video_description=account_data.get('video_description', ''),
                        horizontal_cover_path=account_data.get('horizontal_cover_path', ''),
                        vertical_cover_path=account_data.get('vertical_cover_path', ''),
                        use_custom_cover=account_data.get('use_custom_cover', False),
                        video_folder=account_data.get('video_folder', '')
                    )
                    # 恢复上传统计数据
                    account.upload_count = account_data.get('upload_count', 0)
                    account.success_count = account_data.get('success_count', 0)
                    account.fail_count = account_data.get('fail_count', 0)
                    account.upload_hour = account_data.get('upload_hour', 0)
                    account.upload_minute = account_data.get('upload_minute', 0)
                    account.last_upload_date = account_data.get('last_upload_date')
                    account.daily_upload_count = account_data.get('daily_upload_count', 1)
                    account.use_scheduled_time = account_data.get('use_scheduled_time', False)
                    
                    # 加载设备指纹相关属性
                    account.user_agent = account_data.get('user_agent', '')
                    account.screen_resolution = account_data.get('screen_resolution', '1920x1080')
                    account.hardware_concurrency = account_data.get('hardware_concurrency', 4)
                    account.device_memory = account_data.get('device_memory', 8)
                    account.webgl_vendor = account_data.get('webgl_vendor', 'Google Inc. (Intel)')
                    account.webgl_renderer = account_data.get('webgl_renderer', 'ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0)')
                    account.canvas_fingerprint = account_data.get('canvas_fingerprint', 'default')
                
                self.update_account_list()
                self.update_status("已加载保存的配置")
        except Exception as e:
            print(f"加载配置时出错: {str(e)}")
    
    def save_config(self):
        """保存当前配置"""
        try:
            if self.config.save_accounts_config(self.uploader.accounts):
                print("配置已保存")
        except Exception as e:
            print(f"保存配置时出错: {str(e)}")
    
    def on_closing(self):
        """关闭窗口时的处理"""
        if self.uploader.is_running:
            if messagebox.askyesno("确认", "上传任务正在进行中，确定要退出吗？"):
                self.update_status("正在停止上传任务...")
                self.uploader.stop()
                # 保存配置
                self.save_config()
                if self.uploader.driver:
                    try:
                        self.uploader.driver.quit()
                    except Exception:
                        pass
                self.root.destroy()
        else:
            # 保存配置
            self.save_config()
            self.root.destroy()

    def _log_operation(self, message):
        """记录操作日志"""
        log_file = self.config.get_log_path(f"operation_{time.strftime('%Y%m%d')}.log")
        try:
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {message}\n")
        except Exception as e:
            print(f"写入日志失败: {e}")
    
    def _save_cookies(self, account_name):
        """保存cookies的具体实现"""
        cookies_file = self.config.get_cookies_path(account_name)
        
        # 创建一个线程来执行浏览器操作
        def get_cookies_thread():
            try:
                # 更新状态显示
                self.root.after(0, lambda: self.update_status(f"正在启动Chrome获取 {account_name} 的CK..."))
                
                # 查找对应的账号配置
                account = next((acc for acc in self.uploader.accounts 
                              if str(acc.name).strip().replace("'", "") == str(account_name).strip().replace("'", "")), 
                             None)
                             
                if not account:
                    self.root.after(0, lambda: self.update_status(f"错误：未找到账号 {account_name} 的配置信息"))
                    self.root.after(0, lambda: messagebox.showerror("错误", f"未找到账号 {account_name} 的配置信息"))
                    return
                    
                # 设置当前账号并应用指纹配置
                self.uploader.current_account = account
                self.uploader.status_callback = lambda msg: self.root.after(0, lambda: self.update_status(msg))
                
                # 使用KuaishouUploader的浏览器启动方法，这样会应用账号的指纹设置
                try:
                    success = self.uploader.setup_browser()
                    if not success:
                        self.root.after(0, lambda: self.update_status("启动Chrome失败，请查看日志"))
                        self.root.after(0, lambda: messagebox.showerror("Chrome启动错误", "启动Chrome失败，请确保已安装Chrome浏览器并且系统可正常启动。"))
                        return
                except Exception as e:
                    error_msg = f"启动Chrome失败: {str(e)}"
                    self.root.after(0, lambda: self.update_status(error_msg))
                    self.root.after(0, lambda: messagebox.showerror("Chrome启动错误", 
                                                                  f"启动Chrome失败，请确保已安装Chrome浏览器并且系统可正常启动。\n\n错误信息: {str(e)}"))
                    self._log_operation(error_msg)
                    return
                
                # 访问快手创作者平台
                try:
                    self.uploader.driver.get("https://cp.kuaishou.com/profile")
                except Exception as e:
                    error_msg = f"访问快手网站失败: {str(e)}"
                    self.root.after(0, lambda: self.update_status(error_msg))
                    self.root.after(0, lambda: messagebox.showerror("网络错误", f"访问快手网站失败，请检查网络连接。\n\n错误信息: {str(e)}"))
                    self.uploader.driver.quit()
                    self.uploader.driver = None
                    self._log_operation(error_msg)
                    return
                
                # 使用after方法在主线程中安全地显示消息框
                self.root.after(0, lambda: messagebox.showinfo("提示", f"请使用快手APP扫码登录账号：{account_name}，登录成功后会自动保存CK。\n您有10分钟的时间完成扫码。"))
                
                # 设置最大等待时间为10分钟
                max_wait = 600  # 10分钟 = 600秒
                start_time = time.time()
                
                for _ in range(max_wait):
                    if "发布作品" in self.uploader.driver.page_source or "数据中心" in self.uploader.driver.page_source:
                        break
                    # 每30秒提示一次剩余时间
                    elapsed_time = time.time() - start_time
                    remaining_time = max_wait - int(elapsed_time)
                    if remaining_time % 30 == 0 and remaining_time > 0:
                        self.root.after(0, lambda remain_time=remaining_time: self.update_status(f"请尽快完成扫码，剩余时间：{remain_time//60}分{remain_time%60}秒"))
                    time.sleep(1)
                else:
                    self.root.after(0, lambda: messagebox.showerror("超时", "扫码超时（10分钟），请重试。"))
                    self.uploader.driver.quit()
                    self.uploader.driver = None
                    return
                    
                # 获取cookies并保存
                try:
                    cookies = self.uploader.driver.get_cookies()
                    with open(cookies_file, "w", encoding="utf-8") as f:
                        json.dump(cookies, f)
                except Exception as e:
                    error_msg = f"保存cookies失败: {str(e)}"
                    self.root.after(0, lambda: self.update_status(error_msg))
                    self.root.after(0, lambda: messagebox.showerror("保存失败", f"保存cookies文件失败。\n\n错误信息: {str(e)}"))
                    self._log_operation(error_msg)
                    self.uploader.driver.quit()
                    self.uploader.driver = None
                    return
                
                self.uploader.driver.quit()
                self.uploader.driver = None
                
                # 在主线程中更新UI
                self.root.after(0, lambda: self.update_status(f"账号 {account_name} 的CK已保存成功"))
                self.root.after(0, lambda: messagebox.showinfo("成功", f"账号 {account_name} 的CK已保存到 cookies 目录"))
                self.root.after(0, self.update_account_list)
                
                # 记录日志
                self._log_operation(f"保存账号 {account_name} 的cookies成功")
            except Exception as e:
                error_msg = f"获取CK失败：{e}"
                self.root.after(0, lambda: self.update_status(error_msg))
                self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
                self._log_operation(f"保存账号 {account_name} 的cookies失败: {error_msg}")
                if hasattr(self.uploader, 'driver') and self.uploader.driver:
                    try:
                        self.uploader.driver.quit()
                    except:
                        pass
                    self.uploader.driver = None
        
        # 创建并启动线程
        import threading
        ck_thread = threading.Thread(target=get_cookies_thread)
        ck_thread.daemon = True
        ck_thread.start()
        
        # 更新状态
        self.update_status(f"正在后台获取账号 {account_name} 的CK，请等待浏览器打开...")
    
    def update_status(self, message):
        """更新状态信息并记录日志"""
        self.status_text.insert(tk.END, f"[{time.strftime('%H:%M:%S')}] {message}\n")
        self.status_text.see(tk.END)
        self.status_bar.config(text=message)
        self._log_operation(message)  # 同时记录到日志文件
    
    def create_widgets(self):
        try:
            # 创建主框架，使用网格布局
            main_frame = ttk.Frame(self.root, padding="5")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 左侧面板 - 账号管理
            left_frame = ttk.Frame(main_frame)
            left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)

            # 账号管理区域
            account_frame = ttk.LabelFrame(left_frame, text="账号管理", padding="5")
            account_frame.pack(fill=tk.BOTH, expand=True)

            # 创建账号表格
            columns = ("name", "status", "upload_info", "uploaded")
            self.account_tree = ttk.Treeview(account_frame, columns=columns, show="headings", height=10)
            
            # 设置列标题
            self.account_tree.heading("name", text="账号名称", command=lambda: self.treeview_sort_column("name", False))
            self.account_tree.heading("status", text="登录状态", command=lambda: self.treeview_sort_column("status", False))
            self.account_tree.heading("upload_info", text="上传设置", command=lambda: self.treeview_sort_column("upload_info", False))
            self.account_tree.heading("uploaded", text="已上传", command=lambda: self.treeview_sort_column("uploaded", False))
            
            # 设置列宽
            self.account_tree.column("name", width=120, minwidth=100)
            self.account_tree.column("status", width=100, minwidth=80)
            self.account_tree.column("upload_info", width=120, minwidth=100)
            self.account_tree.column("uploaded", width=80, minwidth=70)
            
            # 添加滚动条
            account_scroll = ttk.Scrollbar(account_frame, orient="vertical", command=self.account_tree.yview)
            self.account_tree.configure(yscrollcommand=account_scroll.set)
            
            # 放置表格和滚动条
            self.account_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            account_scroll.pack(side=tk.RIGHT, fill=tk.Y)

            # 绑定双击事件
            self.account_tree.bind('<Double-1>', lambda e: self.edit_account_dialog())
            
            # 账号操作按钮
            account_btn_frame = ttk.Frame(account_frame)
            account_btn_frame.pack(fill=tk.X, pady=2)
            
            ttk.Button(account_btn_frame, text="添加账号", width=10, 
                      command=self.add_account_dialog).pack(side=tk.LEFT, padx=2)
            ttk.Button(account_btn_frame, text="编辑账号", width=10,
                      command=self.edit_account_dialog).pack(side=tk.LEFT, padx=2)
            ttk.Button(account_btn_frame, text="删除账号", width=10,
                      command=self.delete_account).pack(side=tk.LEFT, padx=2)
            ttk.Button(account_btn_frame, text="刷新", width=8,
                      command=self.update_account_list).pack(side=tk.RIGHT, padx=2)

            # Chrome信息区域
            chrome_frame = ttk.LabelFrame(left_frame, text="Chrome信息", padding="5")
            chrome_frame.pack(fill=tk.X, pady=5)

            self.chrome_status_label = ttk.Label(chrome_frame, text="检测中...", font=("", 9))
            self.chrome_status_label.pack(anchor=tk.W)
            
            self.chrome_path_label = ttk.Label(chrome_frame, text="路径: 未检测", font=("", 9))
            self.chrome_path_label.pack(anchor=tk.W)
            
            self.chrome_version_label = ttk.Label(chrome_frame, text="版本: 未检测", font=("", 9))
            self.chrome_version_label.pack(anchor=tk.W)
            
            # 添加测试设备指纹按钮
            ttk.Button(chrome_frame, text="测试设备指纹", 
                    command=self.test_fingerprint).pack(fill=tk.X, pady=5)

            # 右侧面板 - 状态信息
            right_frame = ttk.Frame(main_frame)
            right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)

            # 操作按钮区域
            button_frame = ttk.Frame(right_frame)
            button_frame.pack(fill=tk.X, pady=5)

            self.start_button = ttk.Button(button_frame, text="开始上传", width=15,
                                         command=self.start_upload)
            self.start_button.pack(side=tk.LEFT, padx=2)

            self.stop_button = ttk.Button(button_frame, text="停止上传", width=15,
                                        command=self.stop_upload, state=tk.DISABLED)
            self.stop_button.pack(side=tk.LEFT, padx=2)

            # 状态信息区域
            status_frame = ttk.LabelFrame(right_frame, text="状态信息", padding="5")
            status_frame.pack(fill=tk.BOTH, expand=True, pady=2)

            # 状态文本框和滚动条
            scrollbar = ttk.Scrollbar(status_frame)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            self.status_text = tk.Text(status_frame, height=25, wrap=tk.WORD,
                                     yscrollcommand=scrollbar.set, font=("", 9))
            self.status_text.pack(fill=tk.BOTH, expand=True)
            scrollbar.config(command=self.status_text.yview)

            # 状态栏
            self.status_bar = ttk.Label(self.root, text="就绪", relief=tk.SUNKEN,
                                      anchor=tk.W, font=("", 9))
            self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        except Exception as e:
            messagebox.showerror("错误", f"创建界面时出错：{str(e)}")
    
    def treeview_sort_column(self, col, reverse):
        """表格排序功能"""
        try:
            l = [(self.account_tree.set(k, col), k) for k in self.account_tree.get_children('')]
            
            # 根据列的类型进行排序
            if col == "total":
                l.sort(key=lambda t: int(t[0]) if t[0].isdigit() else 0, reverse=reverse)
            elif col == "uploaded":
                l.sort(key=lambda t: int(t[0].split('/')[0]) if '/' in t[0] else 0, reverse=reverse)
            else:
                l.sort(reverse=reverse)
                
            for index, (val, k) in enumerate(l):
                self.account_tree.move(k, '', index)
            
            # 切换排序方向
            self.account_tree.heading(col, command=lambda: self.treeview_sort_column(col, not reverse))
            
        except Exception as e:
            print(f"排序时出错: {str(e)}")

    def update_account_list(self):
        """更新账号列表显示"""
        try:
            # 保存当前选中项
            selected = self.account_tree.selection()
            selected_names = [self.account_tree.item(item)['values'][0] for item in selected]
            
            # 清空现有项目
            for item in self.account_tree.get_children():
                self.account_tree.delete(item)
            
            # 添加账号信息
            for account in self.uploader.accounts:
                try:
                    # 计算视频总数
                    total_videos = 0
                    if account.video_folder and os.path.exists(account.video_folder):
                        video_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']
                        total_videos = len([f for f in os.listdir(account.video_folder)
                                        if any(f.lower().endswith(ext) for ext in video_extensions)])
                    
                    # 确定登录状态 - 修改这里的逻辑，使用全局配置路径
                    cookies_file = self.config.get_cookies_path(account.name)
                    status = "未登录"
                    if os.path.exists(cookies_file):
                        try:
                            with open(cookies_file, 'r', encoding='utf-8') as f:
                                cookies = json.load(f)
                                if cookies and len(cookies) > 0:
                                    status = "已保存登录信息"
                        except:
                            status = "登录信息损坏"
                    
                    # 获取今日上传信息
                    today_uploads = 0
                    if hasattr(account, 'today_uploads'):
                        today_uploads = account.today_uploads
                    
                    # 上传时间和数量信息
                    time_info = f"{account.upload_hour:02d}:{account.upload_minute:02d}"
                    upload_info = f"{today_uploads}/{account.daily_upload_count}个/天"
                    
                    # 插入账号信息
                    item = self.account_tree.insert("", tk.END, values=(
                        account.name,
                        status,
                        f"{time_info} {upload_info}",
                        f"{account.success_count}/{account.upload_count}"
                    ))
                    
                    # 如果这个账号之前被选中，恢复选中状态
                    if account.name in selected_names:
                        self.account_tree.selection_add(item)
                        
                except Exception as e:
                    print(f"更新账号 {account.name} 信息时出错: {str(e)}")
                    continue
                    
        except Exception as e:
            messagebox.showerror("错误", f"更新账号列表时出错：{str(e)}")
            print(f"更新账号列表时出错: {str(e)}")
    
    def delete_account(self):
        """删除选中的账号"""
        try:
            selection = self.account_tree.selection()
            if not selection:
                messagebox.showwarning("警告", "请先选择要删除的账号")
                return
                
            # 获取选中的账号名称
            account_names = []
            for item in selection:
                try:
                    account_name = self.account_tree.item(item)['values'][0]
                    account_names.append(account_name)
                except:
                    continue
            
            if not account_names:
                return
                
            # 如果选中多个账号，确认信息需要相应修改
            confirm_msg = "确定要删除选中的账号吗？" if len(account_names) == 1 else \
                         f"确定要删除选中的 {len(account_names)} 个账号吗？"
            
            if messagebox.askyesno("确认", confirm_msg):
                # 删除账号
                self.uploader.accounts = [acc for acc in self.uploader.accounts if str(acc.name).strip().replace("'", "") not in [str(n).strip().replace("'", "") for n in account_names]]
                self.update_account_list()
                self.update_status(f"已删除 {len(account_names)} 个账号")
                # 保存配置
                self.save_config()
                
        except Exception as e:
            messagebox.showerror("错误", f"删除账号时出错：{str(e)}")
    
    def detect_chrome(self):
        if self.uploader.detect_chrome():
            self.chrome_status_label.config(text="已检测到Chrome浏览器")
            self.chrome_path_label.config(text=f"路径: {self.uploader.chrome_path}")
            self.chrome_version_label.config(text=f"版本: {self.uploader.chrome_version}")
            self.update_status("Chrome浏览器检测成功")
        else:
            self.chrome_status_label.config(text="未检测到Chrome浏览器")
            self.update_status("错误：未检测到Chrome浏览器，请确保已安装")
            messagebox.showerror("错误", "未检测到Chrome浏览器，请确保已安装")
    
    def browse_folder(self):
        """移除此方法"""
        pass

    def scan_videos(self):
        """移除此方法"""
        pass
    
    def update_char_count(self, event=None):
        """更新字数统计"""
        current_text = self.description_text.get("1.0", tk.END)
        char_count = len(current_text.strip())
        self.char_count_label.config(text=f"{char_count}/500")
        
        # 如果超过500字，禁止继续输入
        if char_count > 500:
            self.description_text.delete("1.0", tk.END)
            self.description_text.insert("1.0", current_text[:500])
            self.char_count_label.config(text="500/500")

    def start_upload(self):
        """开始上传任务"""
        if not self.uploader.accounts:
            messagebox.showerror("错误", "请先添加至少一个账号")
            return
            
        # 检查是否有账号设置了视频文件夹
        valid_accounts = [acc for acc in self.uploader.accounts if acc.video_folder and os.path.exists(acc.video_folder)]
        if not valid_accounts:
            messagebox.showerror("错误", "没有找到有效的账号和视频文件夹配置")
            return
            
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        # 创建并启动上传线程
        self.upload_thread = threading.Thread(
            target=self.uploader.run_multi_accounts,
            args=(None, self.update_status)  # 使用None作为默认文件夹，因为每个账号都有自己的文件夹
        )
        self.upload_thread.daemon = True
        self.upload_thread.start()
        
        # 启动状态检查
        self.check_thread_status()
    
    def stop_upload(self):
        if self.uploader.is_running:
            self.update_status("正在停止上传任务...")
            self.uploader.stop()
            self.stop_button.config(state=tk.DISABLED)
    
    def check_thread_status(self):
        try:
            if self.upload_thread and not self.upload_thread.is_alive():
                self.start_button.config(state=tk.NORMAL)
                self.stop_button.config(state=tk.DISABLED)
                self.update_status("上传任务已结束")
            elif self.uploader.is_running:
                self.root.after(1000, self.check_thread_status)
        except Exception as e:
            self.update_status(f"监控线程状态时发生错误: {str(e)}")
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
    
    def browse_cover(self):
        """选择封面图片"""
        filetypes = [
            ("图片文件", "*.jpg;*.jpeg;*.png"),
            ("JPEG文件", "*.jpg;*.jpeg"),
            ("PNG文件", "*.png"),
            ("所有文件", "*.*")
        ]
        cover_path = filedialog.askopenfilename(
            title="选择封面图片",
            filetypes=filetypes
        )
        if cover_path:
            self.cover_path_var.set(cover_path)
            self.update_status(f"已选择封面图片: {os.path.basename(cover_path)}")
    def add_account_dialog(self):
        """添加账号对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("添加账号")
        dialog.geometry("500x650")  # 增加对话框高度
        dialog.transient(self.root)
        
        # 创建一个带滚动条的主框架
        main_canvas = tk.Canvas(dialog)
        main_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        scrollbar = ttk.Scrollbar(dialog, orient=tk.VERTICAL, command=main_canvas.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        main_canvas.configure(yscrollcommand=scrollbar.set)
        main_canvas.bind('<Configure>', lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all")))
        
        main_frame = ttk.Frame(main_canvas)
        main_canvas.create_window((0, 0), window=main_frame, anchor="nw")
        
        # 账号名称
        ttk.Label(main_frame, text="账号名称:").pack(pady=5)
        name_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=name_var).pack(fill=tk.X)
        
        # 定时上传设置
        time_frame = ttk.LabelFrame(main_frame, text="定时上传设置")
        time_frame.pack(fill=tk.X, pady=5)
        
        # 添加定时发布勾选框
        use_scheduled_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(time_frame, text="启用定时发布", variable=use_scheduled_var).pack(anchor=tk.W, padx=5, pady=2)
        
        time_select_frame = ttk.Frame(time_frame)
        time_select_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(time_select_frame, text="每天上传时间:").pack(side=tk.LEFT)
        hour_var = tk.StringVar(value="0")
        minute_var = tk.StringVar(value="0")
        
        # 小时选择器（24小时制）
        hour_select = ttk.Spinbox(time_select_frame, from_=0, to=23, width=5, textvariable=hour_var)
        hour_select.pack(side=tk.LEFT, padx=2)
        ttk.Label(time_select_frame, text="时").pack(side=tk.LEFT)
        
        # 分钟选择器
        minute_select = ttk.Spinbox(time_select_frame, from_=0, to=59, width=5, textvariable=minute_var)
        minute_select.pack(side=tk.LEFT, padx=2)
        ttk.Label(time_select_frame, text="分").pack(side=tk.LEFT)
        
        # 每天上传视频数量
        daily_count_frame = ttk.Frame(time_frame)
        daily_count_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(daily_count_frame, text="每天上传视频数量:").pack(side=tk.LEFT)
        daily_count_var = tk.StringVar(value="1")
        daily_count_select = ttk.Spinbox(daily_count_frame, from_=1, to=100, width=5, textvariable=daily_count_var)
        daily_count_select.pack(side=tk.LEFT, padx=2)
        ttk.Label(daily_count_frame, text="个").pack(side=tk.LEFT)
        
        # 视频文件夹选择
        folder_frame = ttk.LabelFrame(main_frame, text="视频文件夹")
        folder_frame.pack(fill=tk.X, pady=5)
        
        folder_path_var = tk.StringVar()
        ttk.Entry(folder_frame, textvariable=folder_path_var).pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(folder_frame, text="选择文件夹", 
                   command=lambda: folder_path_var.set(filedialog.askdirectory(title="选择视频文件夹"))).pack(pady=2)
        
        # 视频描述
        ttk.Label(main_frame, text="视频描述:").pack(pady=(5,2))  # 减小上下间距
        desc_text = tk.Text(main_frame, height=2)  # 将高度从4改为2
        desc_text.pack(fill=tk.X)
        
        # 封面设置
        cover_frame = ttk.LabelFrame(main_frame, text="封面设置")
        cover_frame.pack(fill=tk.X, pady=5)
        
        use_cover_var = tk.BooleanVar()
        ttk.Checkbutton(cover_frame, text="使用自定义封面", variable=use_cover_var).pack(anchor=tk.W)
        
        # 横屏封面
        horizontal_frame = ttk.Frame(cover_frame)
        horizontal_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(horizontal_frame, text="横屏封面:").pack(side=tk.LEFT)
        horizontal_cover_path_var = tk.StringVar()
        ttk.Entry(horizontal_frame, textvariable=horizontal_cover_path_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(horizontal_frame, text="选择", 
                   command=lambda: horizontal_cover_path_var.set(filedialog.askopenfilename(
                       title="选择横屏封面",
                       filetypes=[("图片文件", "*.jpg;*.jpeg;*.png")]))).pack(side=tk.LEFT, padx=2)
        
        # 竖屏封面
        vertical_frame = ttk.Frame(cover_frame)
        vertical_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(vertical_frame, text="竖屏封面:").pack(side=tk.LEFT)
        vertical_cover_path_var = tk.StringVar()
        ttk.Entry(vertical_frame, textvariable=vertical_cover_path_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(vertical_frame, text="选择", 
                   command=lambda: vertical_cover_path_var.set(filedialog.askopenfilename(
                       title="选择竖屏封面",
                       filetypes=[("图片文件", "*.jpg;*.jpeg;*.png")]))).pack(side=tk.LEFT, padx=2)
        
        # 设备指纹设置
        fingerprint_frame = ttk.LabelFrame(main_frame, text="设备指纹设置")
        fingerprint_frame.pack(fill=tk.X, pady=5)
        
        # User-Agent设置
        ttk.Label(fingerprint_frame, text="User-Agent:").pack(anchor=tk.W, padx=5, pady=2)
        user_agent_var = tk.StringVar(value="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36")
        ttk.Entry(fingerprint_frame, textvariable=user_agent_var).pack(fill=tk.X, padx=5, pady=2)
        
        # 屏幕分辨率设置
        resolution_frame = ttk.Frame(fingerprint_frame)
        resolution_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(resolution_frame, text="屏幕分辨率:").pack(side=tk.LEFT)
        resolution_var = tk.StringVar(value="1920x1080")
        resolution_combo = ttk.Combobox(resolution_frame, textvariable=resolution_var, 
                                      values=["1920x1080", "1366x768", "1440x900", "1536x864", "1280x720", "1600x900", "2560x1440"])
        resolution_combo.pack(side=tk.LEFT, padx=2)
        
        # 硬件并发数设置
        concurrency_frame = ttk.Frame(fingerprint_frame)
        concurrency_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(concurrency_frame, text="硬件并发数:").pack(side=tk.LEFT)
        concurrency_var = tk.StringVar(value="4")
        concurrency_combo = ttk.Combobox(concurrency_frame, textvariable=concurrency_var, 
                                       values=["2", "4", "6", "8", "12", "16"])
        concurrency_combo.pack(side=tk.LEFT, padx=2)
        
        # 设备内存设置
        memory_frame = ttk.Frame(fingerprint_frame)
        memory_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(memory_frame, text="设备内存(GB):").pack(side=tk.LEFT)
        memory_var = tk.StringVar(value="8")
        memory_combo = ttk.Combobox(memory_frame, textvariable=memory_var, 
                                  values=["2", "4", "8", "16", "32"])
        memory_combo.pack(side=tk.LEFT, padx=2)
        
        # WebGL厂商设置
        webgl_vendor_frame = ttk.Frame(fingerprint_frame)
        webgl_vendor_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(webgl_vendor_frame, text="WebGL厂商:").pack(side=tk.LEFT)
        webgl_vendor_var = tk.StringVar(value="Google Inc. (Intel)")
        webgl_vendor_combo = ttk.Combobox(webgl_vendor_frame, textvariable=webgl_vendor_var, 
                                        values=["Google Inc. (Intel)", "Google Inc. (NVIDIA)", "Google Inc. (AMD)", "Microsoft"])
        webgl_vendor_combo.pack(side=tk.LEFT, padx=2)
        
        # WebGL渲染器设置
        webgl_renderer_frame = ttk.Frame(fingerprint_frame)
        webgl_renderer_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(webgl_renderer_frame, text="WebGL渲染器:").pack(side=tk.LEFT)
        webgl_renderer_var = tk.StringVar(value="ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0)")
        webgl_renderer_entry = ttk.Entry(webgl_renderer_frame, textvariable=webgl_renderer_var)
        webgl_renderer_entry.pack(side=tk.LEFT, padx=2, fill=tk.X, expand=True)
        
        # Canvas指纹设置
        canvas_frame = ttk.Frame(fingerprint_frame)
        canvas_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(canvas_frame, text="Canvas指纹:").pack(side=tk.LEFT)
        canvas_var = tk.StringVar(value="default")
        canvas_entry = ttk.Entry(canvas_frame, textvariable=canvas_var)
        canvas_entry.pack(side=tk.LEFT, padx=2, fill=tk.X, expand=True)
        
        # 默认值 - 仅用于初始化，删除重复赋值
        user_agent_default = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36"
        webgl_renderer_default = "ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0)"
        canvas_fp_default = "default"

        # 删除这部分重复变量定义
        # user_agent_var = tk.StringVar(value=user_agent_default)
        # webgl_renderer_var = tk.StringVar(value=webgl_renderer_default)
        # canvas_var = tk.StringVar(value=canvas_fp_default)

        # 随机生成函数
        def random_fingerprint():
            ua_list = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36"
            ]
            resolutions = ["1920x1080", "1366x768", "1440x900", "1536x864", "1280x720", "1600x900", "2560x1440"]
            concurrency = [2, 4, 6, 8, 12, 16]
            memory = [2, 4, 8, 16, 32]
            vendors = ["Google Inc. (Intel)", "Google Inc. (NVIDIA)", "Google Inc. (AMD)", "Microsoft"]
            renderers = [
                "ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0)",
                "ANGLE (NVIDIA, NVIDIA GeForce GTX 1660 Ti Direct3D11 vs_5_0 ps_5_0)",
                "ANGLE (AMD, AMD Radeon RX 580 Series Direct3D11 vs_5_0 ps_5_0)",
                "ANGLE (Microsoft, Microsoft Basic Render Driver Direct3D11 vs_5_0 ps_5_0)"
            ]
            canvas_fp = ''.join(random.choices(string.ascii_letters + string.digits, k=32))
            return {
                'user_agent': random.choice(ua_list),
                'screen_resolution': random.choice(resolutions),
                'hardware_concurrency': str(random.choice(concurrency)),
                'device_memory': str(random.choice(memory)),
                'webgl_vendor': random.choice(vendors),
                'webgl_renderer': random.choice(renderers),
                'canvas_fingerprint': canvas_fp
            }

        def set_random_fingerprint():
            fp = random_fingerprint()
            user_agent_var.set(fp['user_agent'])
            resolution_var.set(fp['screen_resolution'])
            concurrency_var.set(fp['hardware_concurrency'])
            memory_var.set(fp['device_memory'])
            webgl_vendor_var.set(fp['webgl_vendor'])
            webgl_renderer_var.set(fp['webgl_renderer'])
            canvas_var.set(fp['canvas_fingerprint'])

        # 在设备指纹设置区域下方添加按钮
        random_btn = ttk.Button(fingerprint_frame, text="随机生成指纹", command=set_random_fingerprint)
        random_btn.pack(fill=tk.X, padx=5, pady=5)
        
        def save_account():
            name = str(name_var.get()).strip().replace("'", "")  # 强制转字符串并去除引号
            if not name:
                messagebox.showerror("错误", "请输入账号名称")
                return
                
            video_folder = folder_path_var.get().strip()
            if not video_folder:
                if not messagebox.askyesno("确认", "未设置视频文件夹，将使用默认文件夹，是否继续？"):
                    return
            
            try:
                upload_hour = int(hour_var.get())
                upload_minute = int(minute_var.get())
                if not (0 <= upload_hour <= 23 and 0 <= upload_minute <= 59):
                    messagebox.showerror("错误", "请输入有效的时间")
                    return
            except ValueError:
                messagebox.showerror("错误", "请输入有效的时间")
                return
                
            try:
                daily_upload_count = int(daily_count_var.get())
                if daily_upload_count < 1:
                    messagebox.showerror("错误", "每天上传视频数量必须大于0")
                    return
            except ValueError:
                messagebox.showerror("错误", "请输入有效的视频数量")
                return
                
            try:
                hardware_concurrency = int(concurrency_var.get())
                if hardware_concurrency < 1:
                    messagebox.showerror("错误", "硬件并发数必须大于0")
                    return
            except ValueError:
                messagebox.showerror("错误", "请输入有效的硬件并发数")
                return
                
            try:
                device_memory = int(memory_var.get())
                if device_memory < 1:
                    messagebox.showerror("错误", "设备内存必须大于0")
                    return
            except ValueError:
                messagebox.showerror("错误", "请输入有效的设备内存")
                return
            
            account = self.uploader.add_account(
                name=name,
                cookies_file=None,  # 使用默认命名规则
                video_description=desc_text.get("1.0", tk.END).strip(),
                horizontal_cover_path=horizontal_cover_path_var.get(),
                vertical_cover_path=vertical_cover_path_var.get(),
                use_custom_cover=use_cover_var.get(),
                video_folder=video_folder,
                daily_upload_count=daily_upload_count
            )
            
            # 设置上传时间和定时发布选项
            account.upload_hour = upload_hour
            account.upload_minute = upload_minute
            account.daily_upload_count = daily_upload_count
            account.use_scheduled_time = use_scheduled_var.get()  # 保存定时发布设置
            
            # 设置设备指纹信息
            account.user_agent = user_agent_var.get()
            account.screen_resolution = resolution_var.get()
            account.hardware_concurrency = hardware_concurrency
            account.device_memory = device_memory
            account.webgl_vendor = webgl_vendor_var.get()
            account.webgl_renderer = webgl_renderer_var.get()
            account.canvas_fingerprint = canvas_var.get()
            
            self.update_account_list()
            # 保存配置
            self.save_config()
            dialog.destroy()
        
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        ttk.Button(button_frame, text="保存", command=save_account).pack(side=tk.LEFT, expand=True, fill=tk.X, padx=5)
        ttk.Button(button_frame, text="获取CK", 
                  command=lambda: self.get_ck(str(name_var.get()).strip().replace("'", ""))).pack(side=tk.LEFT, expand=True, fill=tk.X, padx=5)
    
    def edit_account_dialog(self):
        print("当前所有账号对象：", [repr(acc.name) for acc in self.uploader.accounts])
        selection = self.account_tree.selection()
        print("当前选中项：", selection)
        if not selection:
            messagebox.showwarning("警告", "请先选择要编辑的账号")
            return

        # 获取选中的账号
        item = selection[0]
        account_name = self.account_tree.item(item)['values'][0]
        print("选中的账号名称：", account_name)
        # 修正：全部转为字符串后strip和去除引号再比对
        account = next((acc for acc in self.uploader.accounts if str(acc.name).strip().replace("'", "") == str(account_name).strip().replace("'", "")), None)
        print("找到的账号对象：", account)
        if not account:
            print("未找到账号对象")
            return
        
        dialog = tk.Toplevel(self.root)
        dialog.title(f"编辑账号 - {account.name}")
        dialog.geometry("500x650")  # 增加对话框高度
        dialog.transient(self.root)
        
        # 创建一个带滚动条的主框架
        main_canvas = tk.Canvas(dialog)
        main_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        scrollbar = ttk.Scrollbar(dialog, orient=tk.VERTICAL, command=main_canvas.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        main_canvas.configure(yscrollcommand=scrollbar.set)
        main_canvas.bind('<Configure>', lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all")))
        
        main_frame = ttk.Frame(main_canvas)
        main_canvas.create_window((0, 0), window=main_frame, anchor="nw")
        
        # 账号名称
        ttk.Label(main_frame, text="账号名称:").pack(pady=5)
        name_var = tk.StringVar(value=str(account.name).strip().replace("'", ""))  # 强制转字符串并去除引号
        ttk.Entry(main_frame, textvariable=name_var).pack(fill=tk.X)
        
        # 定时上传设置
        time_frame = ttk.LabelFrame(main_frame, text="定时上传设置")
        time_frame.pack(fill=tk.X, pady=5)
        
        # 添加定时发布勾选框
        use_scheduled_var = tk.BooleanVar(value=account.use_scheduled_time)
        ttk.Checkbutton(time_frame, text="启用定时发布", variable=use_scheduled_var).pack(anchor=tk.W, padx=5, pady=2)
        
        time_select_frame = ttk.Frame(time_frame)
        time_select_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(time_select_frame, text="每天上传时间:").pack(side=tk.LEFT)
        hour_var = tk.StringVar(value=str(account.upload_hour))
        minute_var = tk.StringVar(value=str(account.upload_minute))
        
        # 小时选择器（24小时制）
        hour_select = ttk.Spinbox(time_select_frame, from_=0, to=23, width=5, textvariable=hour_var)
        hour_select.pack(side=tk.LEFT, padx=2)
        ttk.Label(time_select_frame, text="时").pack(side=tk.LEFT)
        
        # 分钟选择器
        minute_select = ttk.Spinbox(time_select_frame, from_=0, to=59, width=5, textvariable=minute_var)
        minute_select.pack(side=tk.LEFT, padx=2)
        ttk.Label(time_select_frame, text="分").pack(side=tk.LEFT)
        
        # 每天上传视频数量
        daily_count_frame = ttk.Frame(time_frame)
        daily_count_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(daily_count_frame, text="每天上传视频数量:").pack(side=tk.LEFT)
        daily_count_var = tk.StringVar(value=str(account.daily_upload_count))
        daily_count_select = ttk.Spinbox(daily_count_frame, from_=1, to=100, width=5, textvariable=daily_count_var)
        daily_count_select.pack(side=tk.LEFT, padx=2)
        ttk.Label(daily_count_frame, text="个").pack(side=tk.LEFT)
        
        # 视频文件夹选择
        folder_frame = ttk.LabelFrame(main_frame, text="视频文件夹")
        folder_frame.pack(fill=tk.X, pady=5)
        
        folder_path_var = tk.StringVar(value=account.video_folder)
        ttk.Entry(folder_frame, textvariable=folder_path_var).pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(folder_frame, text="选择文件夹", 
                   command=lambda: folder_path_var.set(filedialog.askdirectory(title="选择视频文件夹"))).pack(pady=2)
        
        # 视频描述
        ttk.Label(main_frame, text="视频描述:").pack(pady=(5,2))  # 减小上下间距
        desc_text = tk.Text(main_frame, height=2)  # 将高度从4改为2
        desc_text.insert("1.0", account.video_description)
        desc_text.pack(fill=tk.X)
        
        # 封面设置
        cover_frame = ttk.LabelFrame(main_frame, text="封面设置")
        cover_frame.pack(fill=tk.X, pady=5)
        
        use_cover_var = tk.BooleanVar(value=account.use_custom_cover)
        ttk.Checkbutton(cover_frame, text="使用自定义封面", variable=use_cover_var).pack(anchor=tk.W)
        
        # 横屏封面
        horizontal_frame = ttk.Frame(cover_frame)
        horizontal_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(horizontal_frame, text="横屏封面:").pack(side=tk.LEFT)
        horizontal_cover_path_var = tk.StringVar(value=account.horizontal_cover_path)
        ttk.Entry(horizontal_frame, textvariable=horizontal_cover_path_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(horizontal_frame, text="选择", 
                   command=lambda: horizontal_cover_path_var.set(filedialog.askopenfilename(
                       title="选择横屏封面",
                       filetypes=[("图片文件", "*.jpg;*.jpeg;*.png")]))).pack(side=tk.LEFT, padx=2)
        
        # 竖屏封面
        vertical_frame = ttk.Frame(cover_frame)
        vertical_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(vertical_frame, text="竖屏封面:").pack(side=tk.LEFT)
        vertical_cover_path_var = tk.StringVar(value=account.vertical_cover_path)
        ttk.Entry(vertical_frame, textvariable=vertical_cover_path_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(vertical_frame, text="选择", 
                   command=lambda: vertical_cover_path_var.set(filedialog.askopenfilename(
                       title="选择竖屏封面",
                       filetypes=[("图片文件", "*.jpg;*.jpeg;*.png")]))).pack(side=tk.LEFT, padx=2)
        
        # 设备指纹设置
        fingerprint_frame = ttk.LabelFrame(main_frame, text="设备指纹设置")
        fingerprint_frame.pack(fill=tk.X, pady=5)
        
        # User-Agent设置
        ttk.Label(fingerprint_frame, text="User-Agent:").pack(anchor=tk.W, padx=5, pady=2)
        user_agent_var = tk.StringVar(value=account.user_agent)
        ttk.Entry(fingerprint_frame, textvariable=user_agent_var).pack(fill=tk.X, padx=5, pady=2)
        
        # 屏幕分辨率设置
        resolution_frame = ttk.Frame(fingerprint_frame)
        resolution_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(resolution_frame, text="屏幕分辨率:").pack(side=tk.LEFT)
        resolution_var = tk.StringVar(value=account.screen_resolution)
        resolution_combo = ttk.Combobox(resolution_frame, textvariable=resolution_var, 
                                      values=["1920x1080", "1366x768", "1440x900", "1536x864", "1280x720", "1600x900", "2560x1440"])
        resolution_combo.pack(side=tk.LEFT, padx=2)
        
        # 硬件并发数设置
        concurrency_frame = ttk.Frame(fingerprint_frame)
        concurrency_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(concurrency_frame, text="硬件并发数:").pack(side=tk.LEFT)
        concurrency_var = tk.StringVar(value=str(account.hardware_concurrency))
        concurrency_combo = ttk.Combobox(concurrency_frame, textvariable=concurrency_var, 
                                       values=["2", "4", "6", "8", "12", "16"])
        concurrency_combo.pack(side=tk.LEFT, padx=2)
        
        # 设备内存设置
        memory_frame = ttk.Frame(fingerprint_frame)
        memory_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(memory_frame, text="设备内存(GB):").pack(side=tk.LEFT)
        memory_var = tk.StringVar(value=str(account.device_memory))
        memory_combo = ttk.Combobox(memory_frame, textvariable=memory_var, 
                                  values=["2", "4", "8", "16", "32"])
        memory_combo.pack(side=tk.LEFT, padx=2)
        
        # WebGL厂商设置
        webgl_vendor_frame = ttk.Frame(fingerprint_frame)
        webgl_vendor_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(webgl_vendor_frame, text="WebGL厂商:").pack(side=tk.LEFT)
        webgl_vendor_var = tk.StringVar(value=account.webgl_vendor)
        webgl_vendor_combo = ttk.Combobox(webgl_vendor_frame, textvariable=webgl_vendor_var, 
                                        values=["Google Inc. (Intel)", "Google Inc. (NVIDIA)", "Google Inc. (AMD)", "Microsoft"])
        webgl_vendor_combo.pack(side=tk.LEFT, padx=2)
        
        # WebGL渲染器设置
        webgl_renderer_frame = ttk.Frame(fingerprint_frame)
        webgl_renderer_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(webgl_renderer_frame, text="WebGL渲染器:").pack(side=tk.LEFT)
        webgl_renderer_var = tk.StringVar(value=account.webgl_renderer)
        webgl_renderer_entry = ttk.Entry(webgl_renderer_frame, textvariable=webgl_renderer_var)
        webgl_renderer_entry.pack(side=tk.LEFT, padx=2, fill=tk.X, expand=True)
        
        # Canvas指纹设置
        canvas_frame = ttk.Frame(fingerprint_frame)
        canvas_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(canvas_frame, text="Canvas指纹:").pack(side=tk.LEFT)
        canvas_var = tk.StringVar(value=account.canvas_fingerprint)
        canvas_entry = ttk.Entry(canvas_frame, textvariable=canvas_var)
        canvas_entry.pack(side=tk.LEFT, padx=2, fill=tk.X, expand=True)
        
        # 添加随机生成指纹功能
        def random_fingerprint():
            ua_list = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36"
            ]
            resolutions = ["1920x1080", "1366x768", "1440x900", "1536x864", "1280x720", "1600x900", "2560x1440"]
            concurrency = [2, 4, 6, 8, 12, 16]
            memory = [2, 4, 8, 16, 32]
            vendors = ["Google Inc. (Intel)", "Google Inc. (NVIDIA)", "Google Inc. (AMD)", "Microsoft"]
            renderers = [
                "ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0)",
                "ANGLE (NVIDIA, NVIDIA GeForce GTX 1660 Ti Direct3D11 vs_5_0 ps_5_0)",
                "ANGLE (AMD, AMD Radeon RX 580 Series Direct3D11 vs_5_0 ps_5_0)",
                "ANGLE (Microsoft, Microsoft Basic Render Driver Direct3D11 vs_5_0 ps_5_0)"
            ]
            canvas_fp = ''.join(random.choices(string.ascii_letters + string.digits, k=32))
            return {
                'user_agent': random.choice(ua_list),
                'screen_resolution': random.choice(resolutions),
                'hardware_concurrency': str(random.choice(concurrency)),
                'device_memory': str(random.choice(memory)),
                'webgl_vendor': random.choice(vendors),
                'webgl_renderer': random.choice(renderers),
                'canvas_fingerprint': canvas_fp
            }

        def set_random_fingerprint():
            fp = random_fingerprint()
            user_agent_var.set(fp['user_agent'])
            resolution_var.set(fp['screen_resolution'])
            concurrency_var.set(fp['hardware_concurrency'])
            memory_var.set(fp['device_memory'])
            webgl_vendor_var.set(fp['webgl_vendor'])
            webgl_renderer_var.set(fp['webgl_renderer'])
            canvas_var.set(fp['canvas_fingerprint'])

        # 在设备指纹设置区域下方添加随机生成按钮
        random_btn = ttk.Button(fingerprint_frame, text="随机生成指纹", command=set_random_fingerprint)
        random_btn.pack(fill=tk.X, padx=5, pady=5)
        
        def save_changes():
            name = str(name_var.get()).strip().replace("'", "")  # 强制转字符串并去除引号
            if not name:
                messagebox.showerror("错误", "请输入账号名称")
                return
                
            video_folder = folder_path_var.get().strip()
            if not video_folder and not messagebox.askyesno("确认", "未设置视频文件夹，将使用默认文件夹，是否继续？"):
                return
            
            try:
                upload_hour = int(hour_var.get())
                upload_minute = int(minute_var.get())
                if not (0 <= upload_hour <= 23 and 0 <= upload_minute <= 59):
                    messagebox.showerror("错误", "请输入有效的时间")
                    return
            except ValueError:
                messagebox.showerror("错误", "请输入有效的时间")
                return
                
            try:
                daily_upload_count = int(daily_count_var.get())
                if daily_upload_count < 1:
                    messagebox.showerror("错误", "每天上传视频数量必须大于0")
                    return
            except ValueError:
                messagebox.showerror("错误", "请输入有效的视频数量")
                return
                
            try:
                hardware_concurrency = int(concurrency_var.get())
                if hardware_concurrency < 1:
                    messagebox.showerror("错误", "硬件并发数必须大于0")
                    return
            except ValueError:
                messagebox.showerror("错误", "请输入有效的硬件并发数")
                return
                
            try:
                device_memory = int(memory_var.get())
                if device_memory < 1:
                    messagebox.showerror("错误", "设备内存必须大于0")
                    return
            except ValueError:
                messagebox.showerror("错误", "请输入有效的设备内存")
                return
            
            # 更新账号信息
            account.name = name
            account.video_folder = video_folder
            account.video_description = desc_text.get("1.0", tk.END).strip()
            account.horizontal_cover_path = horizontal_cover_path_var.get()
            account.vertical_cover_path = vertical_cover_path_var.get()
            account.use_custom_cover = use_cover_var.get()
            account.upload_hour = upload_hour
            account.upload_minute = upload_minute
            account.daily_upload_count = daily_upload_count
            account.use_scheduled_time = use_scheduled_var.get()  # 保存定时发布设置
            
            # 更新设备指纹信息
            account.user_agent = user_agent_var.get()
            account.screen_resolution = resolution_var.get()
            account.hardware_concurrency = hardware_concurrency
            account.device_memory = device_memory
            account.webgl_vendor = webgl_vendor_var.get()
            account.webgl_renderer = webgl_renderer_var.get()
            account.canvas_fingerprint = canvas_var.get()
            
            self.update_account_list()
            # 保存配置
            self.save_config()
            dialog.destroy()
        
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        ttk.Button(button_frame, text="保存", command=save_changes).pack(side=tk.LEFT, expand=True, fill=tk.X, padx=5)
        ttk.Button(button_frame, text="获取CK", 
                  command=lambda: self.get_ck(str(name_var.get()).strip().replace("'", ""))).pack(side=tk.LEFT, expand=True, fill=tk.X, padx=5)
    
    def update_account_list(self):
        """更新账号列表显示"""
        try:
            # 保存当前选中项
            selected = self.account_tree.selection()
            selected_names = [self.account_tree.item(item)['values'][0] for item in selected]
            
            # 清空现有项目
            for item in self.account_tree.get_children():
                self.account_tree.delete(item)
            
            # 添加账号信息
            for account in self.uploader.accounts:
                try:
                    # 计算视频总数
                    total_videos = 0
                    if account.video_folder and os.path.exists(account.video_folder):
                        video_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']
                        total_videos = len([f for f in os.listdir(account.video_folder)
                                        if any(f.lower().endswith(ext) for ext in video_extensions)])
                    
                    # 确定登录状态 - 修改这里的逻辑，使用全局配置路径
                    cookies_file = self.config.get_cookies_path(account.name)
                    status = "未登录"
                    if os.path.exists(cookies_file):
                        try:
                            with open(cookies_file, 'r', encoding='utf-8') as f:
                                cookies = json.load(f)
                                if cookies and len(cookies) > 0:
                                    status = "已保存登录信息"
                        except:
                            status = "登录信息损坏"
                    
                    # 获取今日上传信息
                    today_uploads = 0
                    if hasattr(account, 'today_uploads'):
                        today_uploads = account.today_uploads
                    
                    # 上传时间和数量信息
                    time_info = f"{account.upload_hour:02d}:{account.upload_minute:02d}"
                    upload_info = f"{today_uploads}/{account.daily_upload_count}个/天"
                    
                    # 插入账号信息
                    item = self.account_tree.insert("", tk.END, values=(
                        account.name,
                        status,
                        f"{time_info} {upload_info}",
                        f"{account.success_count}/{account.upload_count}"
                    ))
                    
                    # 如果这个账号之前被选中，恢复选中状态
                    if account.name in selected_names:
                        self.account_tree.selection_add(item)
                        
                except Exception as e:
                    print(f"更新账号 {account.name} 信息时出错: {str(e)}")
                    continue
                    
        except Exception as e:
            messagebox.showerror("错误", f"更新账号列表时出错：{str(e)}")
            print(f"更新账号列表时出错: {str(e)}")
    
    def get_ck(self, account_name=None):
        """获取快手账号的CK（cookies）
        Args:
            account_name: 如果提供，直接使用该账号名；否则弹窗要求输入
        """
        if not account_name:
            # 弹窗获取账号名
            dialog = tk.Toplevel(self.root)
            dialog.title("获取CK")
            dialog.geometry("300x150")
            dialog.transient(self.root)
            
            ttk.Label(dialog, text="请输入账号名称:").pack(pady=10)
            name_var = tk.StringVar()
            ttk.Entry(dialog, textvariable=name_var).pack(fill=tk.X, padx=10)
            
            def do_get_ck():
                name = str(name_var.get()).strip().replace("'", "")
                if not name:
                    messagebox.showerror("错误", "请先输入账号名称")
                    return
                dialog.destroy()
                self._save_cookies(name)
                # 保存配置
                self.save_config()
            
            ttk.Button(dialog, text="获取CK", command=do_get_ck).pack(pady=10)
        else:
            # 直接使用提供的账号名
            self._save_cookies(account_name)
            # 保存配置
            self.save_config()

    def test_fingerprint(self):
        """测试设备指纹功能"""
        # 检查是否有账号
        if not self.uploader.accounts:
            messagebox.showerror("错误", "请先添加至少一个账号")
            return
            
        # 获取选中的账号，如果没有选中，使用第一个账号
        selection = self.account_tree.selection()
        if selection:
            account_name = self.account_tree.item(selection[0])['values'][0]
            account = next((acc for acc in self.uploader.accounts 
                          if str(acc.name).strip().replace("'", "") == str(account_name).strip().replace("'", "")), 
                         None)
        else:
            account = self.uploader.accounts[0]
            
        if not account:
            messagebox.showerror("错误", "未找到有效账号")
            return
            
        # 禁用按钮防止重复点击
        self.start_button.config(state=tk.DISABLED)
        
        def run_test():
            try:
                self.update_status("开始测试设备指纹功能...")
                self.update_status(f"使用账号: {account.name}")
                
                # 设置当前账号
                self.uploader.current_account = account
                self.uploader.status_callback = self.update_status
                
                # 启动浏览器
                try:
                    self.uploader.setup_browser()
                    self.update_status("浏览器启动成功!")
                    
                    # 访问测试网站
                    self.uploader.driver.get("https://www.baidu.com")
                    self.update_status("已访问测试网站，指纹信息应已显示在上方")
                    
                    # 等待几秒后关闭浏览器
                    time.sleep(5)
                    if self.uploader.driver:
                        self.uploader.driver.quit()
                        self.uploader.driver = None
                    
                    self.update_status("测试完成，浏览器已关闭")
                    
                except Exception as e:
                    self.update_status(f"测试过程中出错: {str(e)}")
                    if self.uploader.driver:
                        try:
                            self.uploader.driver.quit()
                        except:
                            pass
                        self.uploader.driver = None
            finally:
                # 恢复按钮状态
                self.root.after(0, lambda: self.start_button.config(state=tk.NORMAL))
        
        # 创建并启动测试线程
        test_thread = threading.Thread(target=run_test)
        test_thread.daemon = True
        test_thread.start()


class AccountLimitReachedException(Exception):
    """账号今日上传次数已达上限，需立即终止流程"""
    pass


if __name__ == "__main__":
    try:
        print("=" * 50)
        print("快手视频批量上传工具启动")
        print("版本: 1.0.1")
        print("=" * 50)
        print("初始化程序...")
        
        root = tk.Tk()
        app = KuaishouUploaderApp(root)
        
        print("程序初始化完成，启动主界面")
        root.mainloop()
        
    except Exception as e:
        print(f"\n程序发生严重错误: {str(e)}")
        print("错误详情:")
        import traceback
        traceback.print_exc()
        
        # 在程序崩溃时尝试关闭Chrome浏览器
        try:
            os.system('taskkill /f /im chrome.exe')
            os.system('taskkill /f /im chromedriver.exe')
            print("已尝试关闭所有Chrome进程")
        except Exception:
            pass
            
        # 保持窗口不关闭，让用户可以看到错误信息
        print("\n按Enter键退出程序...")
        input()
        input()