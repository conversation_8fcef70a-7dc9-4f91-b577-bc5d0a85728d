﻿import sys
import os
import random
import subprocess
import cv2
import numpy as np
import torch
import glob
import shutil
from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, QVBoxLayout, QHBoxLayout, 
                            QWidget, QFileDialog, QLabel, QSlider, QProgressBar, QLineEdit, QGroupBox,
                            QRadioButton, QButtonGroup, QTextEdit, QComboBox, QCheckBox, QMessageBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
import mediapipe as mp
from PIL import Image, ImageFilter, ImageOps, ImageEnhance
import tempfile
import json
import math
from pathlib import Path
import heapq  # 用于A*算法

# AI模型相关库
from transformers import pipeline, AutoFeatureExtractor, AutoModelForImageClassification
from diffusers import ControlNetModel, StableDiffusionControlNetPipeline

# 删除A*路径规划算法和Blender相关代码

class VideoProcessingThread(QThread):
    progress_update = pyqtSignal(int)
    processing_complete = pyqtSignal(str)

    def __init__(self, video_folder, audio_folder, output_folder, audio_volume, model_paths, selected_styles=None, inference_steps=25, use_controlnet=False, controlnet_model_path="", drawing_method="standard", background_folder="", delete_original_videos=False, template_folder="", use_template_merge=False):
        super().__init__()
        self.video_folder = video_folder
        self.audio_folder = audio_folder
        self.output_folder = output_folder
        self.audio_volume = audio_volume
        self.model_paths = model_paths
        self.selected_styles = selected_styles or []  # 用户选择的绘画风格
        self.inference_steps = inference_steps  # 存储用户设置的步数
        self.use_controlnet = use_controlnet  # 是否使用ControlNet模型
        self.controlnet_model_path = controlnet_model_path  # ControlNet模型路径
        self.background_folder = background_folder  # 背景图片文件夹
        self.delete_original_videos = delete_original_videos  # 是否删除原始视频
        self.template_folder = template_folder  # 模板视频文件夹
        self.use_template_merge = use_template_merge  # 是否启用模板合并
        
        # 设置为标准绘画方式
        self.drawing_method = "standard"
        
        # 初始化AI模型
        self.init_ai_models()
        
    def init_ai_models(self):
        """初始化CPU友好的AI模型"""
        print("开始初始化AI模型...")
        # 检查是否有可用的GPU
        try:
            # 尝试导入 torch_directml 以支持 Intel GPU
            try:
                import torch_directml
                self.device = torch_directml.device()
                print(f"使用设备: DirectML (Intel GPU)")
            except ImportError:
                # 如果没有 torch_directml，则回退到标准 CUDA 或 CPU
                if torch.cuda.is_available():
                    self.device = "cuda"
                    cuda_device = torch.cuda.get_device_name(0)
                    cuda_mem = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                    print(f"使用设备: {self.device} ({cuda_device}, 内存: {cuda_mem:.2f} GB)")
                else:
                    self.device = "cpu"
                    print(f"使用设备: CPU")
        except Exception as e:
            self.device = "cpu"
            print(f"设备检测出错，默认使用 CPU: {str(e)}")
            
        print(f"最终选择设备: {self.device}")
        
        # 初始化视频高潮检测 - 使用MediaPipe和图像分类器
        self.mp_face_detection = mp.solutions.face_detection
        self.mp_pose = mp.solutions.pose
        self.mp_drawing = mp.solutions.drawing_utils
        
        self.face_detector = self.mp_face_detection.FaceDetection(
            model_selection=0,  # 0为近距离检测，1为远距离检测
            min_detection_confidence=0.5
        )
        
        self.pose_detector = self.mp_pose.Pose(
            static_image_mode=False,
            model_complexity=0,  # 0为轻量级模型，适合CPU
            min_detection_confidence=0.5
        )
        
        # 初始化图像分类器（情感分析替代品）
        sentiment_model_path = self.model_paths.get('sentiment_model', '')
        if os.path.exists(sentiment_model_path):
            try:
                self.feature_extractor = AutoFeatureExtractor.from_pretrained(
                    sentiment_model_path, local_files_only=True  # 禁止自动下载
                )
                self.image_classifier = AutoModelForImageClassification.from_pretrained(
                    sentiment_model_path, local_files_only=True  # 禁止自动下载
                )
                self.has_classifier = True
                print("图像分类器加载成功")
            except:
                self.has_classifier = False
                print("图像分类器加载失败，使用基本检测方法")
        else:
            self.has_classifier = False
            print("图像分类器路径无效，使用基本检测方法")
            
        # 加载ControlNet模型
        if self.use_controlnet and self.controlnet_model_path and os.path.exists(self.controlnet_model_path):
            # 加载ControlNet模型
            self.load_controlnet_model()
        else:
            print("未配置ControlNet模型或路径无效，无法进行线稿控制")
            self.controlnet = None
            self.drawing_generator = None
                
    # 添加单独加载ControlNet模型的方法
    def load_controlnet_model(self):
        """单独加载ControlNet模型"""
        try:
            print(f"正在加载ControlNet模型: {self.controlnet_model_path}")
            
            # 根据设备类型选择数据类型
            if self.device == "cpu":
                dtype = torch.float32
            else:
                dtype = torch.float16
                
            print(f"使用数据类型: {dtype}")
            
            # 检查路径是否存在
            if not os.path.exists(self.controlnet_model_path):
                print(f"错误: ControlNet模型路径不存在: {self.controlnet_model_path}")
                return False
                
            # 检查路径是否是文件夹
            if os.path.isdir(self.controlnet_model_path):
                print(f"ControlNet模型路径是文件夹，尝试查找模型文件...")
                # 检查是否存在config.json或model_index.json
                if os.path.exists(os.path.join(self.controlnet_model_path, "config.json")):
                    print(f"找到config.json，尝试加载模型...")
                elif os.path.exists(os.path.join(self.controlnet_model_path, "model_index.json")):
                    print(f"找到model_index.json，尝试加载模型...")
                else:
                    print(f"警告: 在文件夹中未找到config.json或model_index.json")
                    # 列出文件夹内容
                    print(f"文件夹内容: {os.listdir(self.controlnet_model_path)}")
            
            # 加载ControlNet模型
            try:
                print(f"开始加载ControlNet模型...")
                # 优先尝试加载线稿专用模型
                try:
                    print("尝试加载线稿专用的ControlNet模型...")
                    self.controlnet = ControlNetModel.from_pretrained(
                        os.path.join(self.controlnet_model_path, "controlnet-lineart") if os.path.exists(os.path.join(self.controlnet_model_path, "controlnet-lineart")) else self.controlnet_model_path,
                        torch_dtype=dtype
                    )
                    print("成功加载线稿ControlNet模型")
                except Exception as lineart_error:
                    print(f"加载线稿模型失败: {str(lineart_error)}")
                    print("尝试加载普通ControlNet模型...")
                self.controlnet = ControlNetModel.from_pretrained(
                    self.controlnet_model_path,
                    torch_dtype=dtype
                )
                print("ControlNet模型加载成功")
            except Exception as load_error:
                print(f"加载ControlNet模型出错: {str(load_error)}")
                # 尝试从Hugging Face加载
                try:
                    print("尝试从Hugging Face加载预训练线稿模型...")
                    # 尝试加载线稿专用的ControlNet模型
                    try:
                        self.controlnet = ControlNetModel.from_pretrained(
                            "lllyasviel/sd-controlnet-lineart",
                            torch_dtype=dtype
                        )
                        print("成功从Hugging Face加载线稿ControlNet模型")
                    except:
                        print("线稿模型加载失败，尝试加载canny模型...")
                    self.controlnet = ControlNetModel.from_pretrained(
                        "lllyasviel/sd-controlnet-canny",
                        torch_dtype=dtype
                    )
                    print("成功从Hugging Face加载canny ControlNet模型")
                except Exception as hf_error:
                    print(f"从Hugging Face加载失败: {str(hf_error)}")
                    self.controlnet = None
                    return False
            
            # 如果没有SD模型，创建一个可调用的drawing_generator占位符
            if not hasattr(self, 'drawing_generator') or self.drawing_generator is None:
                # 创建一个可调用的对象，而不是简单的占位符
                class CallableDummyGenerator:
                    def __call__(self, prompt=None, image=None, num_inference_steps=25, guidance_scale=7.5, controlnet_conditioning_scale=1.0, **kwargs):
                        print("使用CallableDummyGenerator处理请求")
                        # 直接使用输入的线稿图像作为结果
                        if image is not None:
                            # 将图像转换为PIL图像
                            if isinstance(image, np.ndarray):
                                image = Image.fromarray(image)
                            # 创建一个简单的结果对象
                            class DummyResult:
                                def __init__(self, img):
                                    self.images = [img]
                            # 返回输入图像作为结果
                            return DummyResult(image)
                        else:
                            # 如果没有输入图像，创建一个空白图像
                            blank_image = Image.new('RGB', (512, 512), color='white')
                            return DummyResult(blank_image)
                
                self.drawing_generator = CallableDummyGenerator()
                print("创建了可调用的ControlNet占位符")
                
            return True
        except Exception as e:
            print(f"加载ControlNet模型失败: {str(e)}")
            import traceback
            traceback.print_exc()
            self.controlnet = None
            return False
            
    def check_ready(self):
        """检查是否所有必要的设置都已完成"""
        if self.video_folder and self.audio_folder and self.output_folder:
            # 检查模型路径
            models_ready = bool(self.controlnet_model_path)
            
            # 检查模板文件夹（如果勾选了模板合并选项）
            template_ready = True
            if hasattr(self, 'use_template_merge') and self.use_template_merge.isChecked():
                template_ready = bool(self.template_folder and os.path.exists(self.template_folder))
                if not template_ready:
                    self.status_label.setText("错误：启用模板合并功能时必须选择有效的模板文件夹")
                
            self.process_btn.setEnabled(models_ready and template_ready)
        else:
            self.process_btn.setEnabled(False)
    
    def run(self):
        print("开始执行视频处理线程...")
        # 1. 获取视频文件列表
        video_files = [f for f in os.listdir(self.video_folder) 
                      if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]
        
        print(f"找到 {len(video_files)} 个视频文件")
        
        if not video_files:
            self.processing_complete.emit("错误：未在选择的文件夹中找到视频文件")
            print(f"视频文件夹路径: {self.video_folder}")
            print(f"文件夹内容: {os.listdir(self.video_folder)}")
            return
            
        # 2. 获取音频文件列表
        audio_files = [f for f in os.listdir(self.audio_folder) 
                      if f.lower().endswith(('.mp3', '.wav', '.ogg', '.aac'))]
        
        print(f"找到 {len(audio_files)} 个音频文件")
        
        if not audio_files:
            self.processing_complete.emit("错误：未在选择的文件夹中找到音频文件")
            print(f"音频文件夹路径: {self.audio_folder}")
            print(f"文件夹内容: {os.listdir(self.audio_folder)}")
            return
        
        # 确保输出文件夹存在
        if not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder)
            print(f"创建输出文件夹: {self.output_folder}")
        else:
            print(f"输出文件夹已存在: {self.output_folder}")
        
        # 创建单一的主临时文件夹在输出目录内
        temp_root = os.path.join(self.output_folder, "autodraw_temp")
        os.makedirs(temp_root, exist_ok=True)
        print(f"创建主临时工作目录: {temp_root}")
        
        try:
            # 处理每个视频文件
            for i, video_file in enumerate(video_files):
                try:
                    video_path = os.path.join(self.video_folder, video_file)
                    print(f"开始处理视频 {i+1}/{len(video_files)}: {video_file}")
                    
                    # 为当前视频创建单独的临时文件夹
                    video_temp_dir = os.path.join(temp_root, f"video_{i}")
                    os.makedirs(video_temp_dir, exist_ok=True)
                    print(f"创建视频临时文件夹: {video_temp_dir}")
                    
                    # 3. 检测视频高潮片段
                    print(f"开始检测视频高潮片段...")
                    highlight_segments = self.detect_highlights(video_path)
                    print(f"高潮片段检测完成，找到 {len(highlight_segments)} 个片段")
                    
                    # 4. 为每个高潮片段生成手绘动画
                    for j, (start_time, end_time) in enumerate(highlight_segments):
                        print(f"处理高潮片段 {j+1}/{len(highlight_segments)}, 时间范围: {start_time:.2f}s - {end_time:.2f}s")
                        
                        # 为当前片段创建单独的临时文件夹
                        segment_temp_dir = os.path.join(video_temp_dir, f"segment_{j}")
                        os.makedirs(segment_temp_dir, exist_ok=True)
                        print(f"创建片段临时文件夹: {segment_temp_dir}")
                        
                        # 随机选择一个音频文件
                        random_audio = os.path.join(self.audio_folder, random.choice(audio_files))
                        print(f"选择的音频文件: {os.path.basename(random_audio)}")
                        
                        # 提取高潮片段 - 保存到临时文件夹
                        highlight_clip_path = os.path.join(segment_temp_dir, f"highlight.mp4")
                        print(f"提取视频片段到: {highlight_clip_path}")
                        if not self.extract_video_segment(video_path, highlight_clip_path, start_time, end_time):
                            print(f"错误: 视频片段提取失败，跳过此片段")
                            continue
                        
                        if not os.path.exists(highlight_clip_path):
                            print(f"错误: 视频片段提取后文件不存在: {highlight_clip_path}")
                            continue
                            
                        print(f"视频片段提取成功，开始生成手绘动画...")
                        
                        # 生成手绘动画 - 保存到临时文件夹
                        drawing_video_path = os.path.join(segment_temp_dir, f"drawing.mp4")
                        print(f"生成手绘动画到: {drawing_video_path}")
                        try:
                            self.generate_drawing_animation(highlight_clip_path, drawing_video_path)
                            
                            if not os.path.exists(drawing_video_path):
                                print(f"错误: 手绘动画生成失败，文件不存在: {drawing_video_path}")
                                continue
                                
                            print(f"手绘动画生成成功，开始合成最终视频...")
                            
                            # 拼接视频 - 先保存到临时目录
                            temp_final_path = os.path.join(segment_temp_dir, f"final.mp4")
                            print(f"合成临时最终视频到: {temp_final_path}")
                            
                            # 使用原始完整视频路径进行上下拼接，确保使用与当前片段匹配的原始视频
                            original_video_path = video_path  # 使用当前正在处理的原始视频
                            self.combine_videos(highlight_clip_path, drawing_video_path, random_audio, 
                                              temp_final_path, self.audio_volume, original_video_path)
                            
                            # 检查临时最终视频是否存在
                            if os.path.exists(temp_final_path):
                                print(f"临时最终视频生成成功: {temp_final_path}")
                                
                                # 创建以视频文件夹名命名的子文件夹
                                import shutil
                                video_dir_name = os.path.basename(os.path.dirname(video_path))
                                video_name = os.path.splitext(os.path.basename(video_path))[0]
                                
                                # 创建子文件夹
                                output_subfolder = os.path.join(self.output_folder, video_dir_name)
                                os.makedirs(output_subfolder, exist_ok=True)
                                print(f"创建输出子文件夹: {output_subfolder}")
                                
                                # 以原视频文件名命名输出文件
                                final_output_path = os.path.join(output_subfolder, f"{video_name}.mp4")
                                
                                # 检查是否需要模板合并
                                if self.use_template_merge and self.template_folder:
                                    print(f"执行模板合并操作...")
                                    temp_merged_path = os.path.join(segment_temp_dir, f"merged_with_template.mp4")
                                    self.merge_with_template(temp_final_path, temp_merged_path)
                                    if os.path.exists(temp_merged_path):
                                        shutil.copy2(temp_merged_path, final_output_path)
                                        print(f"带模板合并的最终视频已复制到子文件夹: {final_output_path}")
                                    else:
                                        shutil.copy2(temp_final_path, final_output_path)
                                        print(f"模板合并失败，使用原始视频: {final_output_path}")
                                else:
                                    shutil.copy2(temp_final_path, final_output_path)
                                    print(f"最终视频已复制到子文件夹: {final_output_path}")
                            else:
                                print(f"警告: 临时最终视频文件不存在: {temp_final_path}")
                                
                        except Exception as e:
                            print(f"生成手绘动画或合成视频时发生错误: {str(e)}")
                            import traceback
                            traceback.print_exc()
                            continue
                        
                        # 更新进度
                        progress = int(((i * len(highlight_segments) + j + 1) / 
                                      (len(video_files) * len(highlight_segments))) * 100)
                        print(f"当前处理进度: {progress}%")
                        self.progress_update.emit(progress)
                    
                    # 每个视频处理完成后，删除对应的临时文件夹
                    try:
                        import shutil
                        print(f"清理视频临时文件夹: {video_temp_dir}")
                        shutil.rmtree(video_temp_dir)
                        print(f"视频 {video_file} 的临时文件清理完成")
                    except Exception as e:
                        print(f"清理视频临时文件夹时出错: {str(e)}")
                        
                except Exception as e:
                    print(f"处理视频 {video_file} 时发生错误: {str(e)}")
                    import traceback
                    traceback.print_exc()
            
            # 处理完所有视频后，删除主临时文件夹
            try:
                import shutil
                print(f"清理主临时工作目录: {temp_root}")
                shutil.rmtree(temp_root)
                print("所有临时文件清理完成")
            except Exception as e:
                print(f"清理主临时工作目录时出错: {str(e)}")
        except Exception as e:
            print(f"处理视频文件时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
        
        # 如果用户选择了删除原始视频，则在处理完成后删除
        if self.delete_original_videos:
            try:
                deleted_count = 0
                print(f"用户选择了删除原始视频，开始删除视频A文件夹中的视频文件...")
                for video_file in video_files:
                    video_path = os.path.join(self.video_folder, video_file)
                    try:
                        os.remove(video_path)
                        print(f"已删除原始视频: {video_path}")
                        deleted_count += 1
                    except Exception as e:
                        print(f"删除视频文件时出错: {video_path}, 错误: {str(e)}")
                
                if deleted_count > 0:
                    print(f"成功删除 {deleted_count}/{len(video_files)} 个原始视频文件")
                    self.processing_complete.emit(f"处理完成！已删除 {deleted_count} 个原始视频。输出文件保存在：{self.output_folder}")
                else:
                    print("没有原始视频被删除")
                    self.processing_complete.emit("处理完成！输出文件保存在：" + self.output_folder)
            except Exception as e:
                print(f"删除原始视频时发生错误: {str(e)}")
                self.processing_complete.emit("处理完成！删除原始视频时出错。输出文件保存在：" + self.output_folder)
        else:
            print("所有视频处理完成!")
            self.processing_complete.emit("处理完成！输出文件保存在：" + self.output_folder)
    
    def detect_highlights(self, video_path):
        """检测视频高潮片段 - 使用MediaPipe分析人脸和姿态"""
        print(f"检测视频高潮片段: {video_path}")
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                print(f"错误: 无法打开视频文件: {video_path}")
                return [(0, 30)]  # 返回默认片段
                
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps
            
            print(f"视频信息: FPS={fps}, 总帧数={frame_count}, 时长={duration:.2f}秒")
            
            # 每隔几秒采样一个关键帧
            sample_interval = 2  # 每2秒采样一帧
            frame_interval = int(fps * sample_interval)
            
            # 随机偏移设置，增加内容多样性
            max_offset = int(fps * 1.0)  # 最大随机偏移量为1秒
            print(f"启用随机帧偏移，最大偏移量: ±{max_offset}帧 (±{max_offset/fps:.2f}秒)")
            
            # 存储每个采样帧的分数
            frame_scores = []
            current_frame = 0
            
            # 限制分析的帧数，防止处理时间过长
            max_frames = min(30, frame_count // frame_interval)  # 最多分析30帧
            print(f"将分析 {max_frames} 个关键帧")
            
            frame_counter = 0
            while current_frame < frame_count and frame_counter < max_frames:
                # 添加随机偏移，增加内容多样性
                if max_offset > 0:
                    random_offset = random.randint(-max_offset, max_offset)
                    frame_pos = max(0, min(frame_count - 1, current_frame + random_offset))
                    print(f"分析帧 {frame_counter+1}/{max_frames}, 原始位置: {current_frame}, 随机偏移: {random_offset}, 最终位置: {frame_pos}")
                else:
                    frame_pos = current_frame
                    print(f"分析帧 {frame_counter+1}/{max_frames}, 帧位置: {frame_pos}")
                
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
                ret, frame = cap.read()
                if not ret:
                    print(f"警告: 无法读取帧 {frame_pos}")
                    break
                    
                # 分析图像内容
                try:
                    score = self.analyze_frame_content(frame)
                    frame_scores.append((current_frame / fps, score))
                    print(f"帧 {frame_counter+1} 得分: {score:.4f}")
                except Exception as e:
                    print(f"分析帧 {current_frame} 时出错: {str(e)}")
                
                current_frame += frame_interval
                frame_counter += 1
            
            cap.release()
            
            # 如果没有有效得分，返回默认片段
            if not frame_scores:
                print("警告: 未获取到有效的帧得分，使用默认片段")
                return [(0, min(30, duration))]
            
            # 过滤出得分较高的片段，确保包含角色
            # 设置最低得分阈值，低于此值的帧被认为不包含有效角色
            min_score_threshold = 0.3
            valid_frame_scores = [fs for fs in frame_scores if fs[1] >= min_score_threshold]
            
            if not valid_frame_scores:
                print(f"警告: 没有找到得分高于阈值({min_score_threshold})的帧，使用原始得分")
                valid_frame_scores = frame_scores
            
            # 找出得分最高的几个片段
            valid_frame_scores.sort(key=lambda x: x[1], reverse=True)
            top_segments = valid_frame_scores[:min(3, len(valid_frame_scores))]
            print(f"得分最高的片段: {top_segments}")
            
            # 从时间点扩展为时间段
            highlights = []
            for time_point, score in top_segments:
                start_time = max(0, time_point - 5)  # 从高潮点前5秒开始
                end_time = min(duration, time_point + 15)  # 到高潮点后15秒结束
                highlights.append((start_time, end_time))
                print(f"高潮片段: {start_time:.2f}s - {end_time:.2f}s, 得分: {score:.4f} {'(包含角色)' if score >= min_score_threshold else '(可能无角色)'}")
            
            # 合并重叠的片段
            highlights.sort(key=lambda x: x[0])
            merged_highlights = []
            for current in highlights:
                if not merged_highlights:
                    merged_highlights.append(current)
                else:
                    prev = merged_highlights[-1]
                    # 如果当前片段与上一个片段重叠，则合并
                    if current[0] <= prev[1]:
                        merged_highlights[-1] = (prev[0], max(prev[1], current[1]))
                    else:
                        merged_highlights.append(current)
            
            print(f"合并后的高潮片段: {merged_highlights}")
            
            # 如果没有检测到任何片段，返回默认片段
            if not merged_highlights:
                print("未检测到高潮片段，使用默认片段")
                return [(0, min(30, duration))]
                
            print(f"检测到 {len(merged_highlights)} 个高潮片段")
            return merged_highlights
            
        except Exception as e:
            print(f"检测视频高潮片段时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            # 返回默认片段
            return [(0, 30)]
    
    def analyze_frame_content(self, frame):
        """分析图像内容 - 检测人物和动画角色，评估视觉效果"""
        score = 0
        has_character = False  # 标记是否包含人物或动画角色
        
        try:
            # 转RGB格式给MediaPipe使用
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # 1. 人脸检测 - 检测人脸数量和大小
            try:
                face_results = self.face_detector.process(rgb_frame)
                face_score = 0
                
                if face_results.detections:
                    for detection in face_results.detections:
                        # 获取人脸框的相对坐标
                        box = detection.location_data.relative_bounding_box
                        # 根据人脸大小和置信度评分
                        face_score += box.width * box.height * detection.score[0]
                    print(f"检测到 {len(face_results.detections)} 个人脸，得分: {face_score:.4f}")
                    has_character = True  # 检测到人脸，标记为包含角色
                else:
                    print("未检测到人脸")
            except Exception as e:
                print(f"人脸检测出错: {str(e)}")
                face_score = 0
            
            # 2. 姿态检测 - 检测人体姿态的显著性
            try:
                pose_results = self.pose_detector.process(rgb_frame)
                pose_score = 0
                
                if pose_results.pose_landmarks:
                    # 计算姿态关键点的运动范围
                    landmarks = pose_results.pose_landmarks.landmark
                    
                    # 提取关键点坐标
                    points = [(lm.x, lm.y) for lm in landmarks if lm.visibility > 0.5]
                    
                    if points:
                        # 计算关键点分布范围
                        x_coords = [p[0] for p in points]
                        y_coords = [p[1] for p in points]
                        x_range = max(x_coords) - min(x_coords)
                        y_range = max(y_coords) - min(y_coords)
                        
                        # 姿态分布得分
                        pose_score = (x_range + y_range) / 2
                        
                        # 添加特定姿势的额外得分
                        # 例如检测是否有举手、跳跃等动作
                        if len(landmarks) > 15:  # 确保有足够的关键点
                            # 手臂举起的判断
                            if (landmarks[15].y < landmarks[11].y or  # 右手腕高于右肩
                                landmarks[16].y < landmarks[12].y):   # 左手腕高于左肩
                                pose_score += 0.3
                        
                        has_character = True  # 检测到人体姿态，标记为包含角色
                    print(f"检测到人体姿态，得分: {pose_score:.4f}")
                else:
                    print("未检测到人体姿态")
            except Exception as e:
                print(f"姿态检测出错: {str(e)}")
                pose_score = 0
                
            # 3. 额外的动画角色检测 - 使用颜色分布和边缘检测来识别动画角色
            try:
                # 转换为HSV颜色空间，更容易检测动画角色的鲜艳颜色
                hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
                
                # 检测鲜艳的颜色区域（常见于动画角色）
                # 熊大熊二等动画角色通常有鲜艳的颜色
                lower_bounds = [
                    np.array([0, 100, 100]),    # 红色范围1
                    np.array([160, 100, 100]),  # 红色范围2
                    np.array([20, 100, 100]),   # 黄色
                    np.array([90, 80, 80]),     # 绿色
                    np.array([100, 100, 100]),  # 蓝色
                    np.array([125, 80, 80])     # 紫色
                ]
                upper_bounds = [
                    np.array([10, 255, 255]),   # 红色范围1
                    np.array([179, 255, 255]),  # 红色范围2
                    np.array([40, 255, 255]),   # 黄色
                    np.array([100, 255, 255]),  # 绿色
                    np.array([120, 255, 255]),  # 蓝色
                    np.array([140, 255, 255])   # 紫色
                ]
                
                cartoon_score = 0
                total_pixels = frame.shape[0] * frame.shape[1]
                
                for lower, upper in zip(lower_bounds, upper_bounds):
                    mask = cv2.inRange(hsv, lower, upper)
                    colored_pixels = cv2.countNonZero(mask)
                    ratio = colored_pixels / total_pixels
                    cartoon_score += ratio
                
                # 归一化动画角色得分
                cartoon_score = min(1.0, cartoon_score * 5)  # 乘以5是为了放大效果，最大值为1.0
                
                # 检测边缘，动画角色通常有清晰的轮廓
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                edges = cv2.Canny(gray, 100, 200)
                edge_ratio = cv2.countNonZero(edges) / total_pixels
                
                # 结合颜色和边缘信息
                cartoon_character_score = cartoon_score * 0.7 + edge_ratio * 10  # 边缘比例通常很小，乘以10放大
                
                print(f"动画角色检测得分: {cartoon_character_score:.4f}")
                
                # 如果动画角色得分超过阈值，认为包含动画角色
                if cartoon_character_score > 0.15:  # 阈值可以根据实际情况调整
                    has_character = True
                    print("检测到可能的动画角色")
                    
            except Exception as e:
                print(f"动画角色检测出错: {str(e)}")
                cartoon_character_score = 0
            
            # 4. 图像质量评估
            try:
                # 计算图像的复杂度（边缘检测）
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                laplacian = cv2.Laplacian(gray, cv2.CV_64F)
                complexity = np.var(laplacian) / 10000  # 归一化
                
                # 亮度和对比度
                brightness = np.mean(gray) / 255
                contrast = np.std(gray) / 128
                
                # 颜色丰富度
                hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
                saturation = np.mean(hsv[:,:,1]) / 255
                color_variety = np.std(hsv[:,:,0]) / 180
                
                print(f"图像质量: 复杂度={complexity:.4f}, 亮度={brightness:.4f}, 对比度={contrast:.4f}, 饱和度={saturation:.4f}, 色彩多样性={color_variety:.4f}")
            except Exception as e:
                print(f"图像质量评估出错: {str(e)}")
                complexity = brightness = contrast = saturation = color_variety = 0.5  # 默认中等值
            
            # 使用图像分类器进行分类（如果可用）
            classifier_score = 0
            if self.has_classifier:
                try:
                    pil_image = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                    inputs = self.feature_extractor(pil_image, return_tensors="pt")
                    with torch.no_grad():
                        outputs = self.image_classifier(**inputs)
                    logits = outputs.logits
                    predicted_class_idx = logits.argmax(-1).item()
                    confidence = torch.softmax(logits, dim=1)[0, predicted_class_idx].item()
                    classifier_score = confidence
                    print(f"图像分类器得分: {classifier_score:.4f}")
                except Exception as e:
                    print(f"图像分类器评估出错: {str(e)}")
                    classifier_score = 0
            
            # 综合得分计算
            image_quality_score = 0.3 * complexity + 0.2 * brightness + 0.2 * contrast + 0.1 * saturation + 0.2 * color_variety
            
            # 最终得分计算 - 如果没有检测到角色，大幅降低得分
            if has_character:
                # 包含人物或动画角色时，使用正常得分计算
                character_multiplier = 1.0
                print("图像中包含人物或动画角色，保持正常得分")
            else:
                # 没有检测到角色时，大幅降低得分
                character_multiplier = 0.2
                print("警告: 图像中未检测到人物或动画角色，得分降低")
            
            # 最终得分 - 权重可以根据需要调整
            base_score = 0.25 * face_score + 0.25 * pose_score + 0.25 * image_quality_score + 0.25 * classifier_score
            score = base_score * character_multiplier
            
            print(f"帧分析最终得分: {score:.4f} (基础得分: {base_score:.4f}, 角色乘数: {character_multiplier})")
            
            return score
            
        except Exception as e:
            print(f"帧分析过程出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return 0.5  # 返回默认中等分数
    
    def generate_drawing_animation(self, video_path, output_path):
        """生成模拟手绘动画 - 使用AI模型生成手绘效果并添加笔画动画过程"""
        print(f"生成大师级手绘动画: {video_path} -> {output_path}")
        
        # 使用输出目录的autodraw_temp作为临时文件的基础目录
        temp_base_dir = os.path.join(self.output_folder, "autodraw_temp", os.path.basename(os.path.dirname(output_path)))
        os.makedirs(temp_base_dir, exist_ok=True)
        print(f"使用统一临时工作目录: {temp_base_dir}")
        
        # 检查模型是否已初始化
        if self.use_controlnet:
            if not hasattr(self, 'controlnet') or self.controlnet is None:
                print("错误: ControlNet模型未初始化，无法生成手绘效果")
                self.processing_complete.emit("错误: ControlNet模型未初始化")
                return
                
            if not hasattr(self, 'drawing_generator'):
                print("错误: drawing_generator未初始化")
                self.processing_complete.emit("错误: 绘图生成器未初始化")
                return
        else:
            if not hasattr(self, 'drawing_generator') or self.drawing_generator is None:
                print("错误: Stable Diffusion模型未初始化，无法生成手绘效果")
                self.processing_complete.emit("错误: Stable Diffusion模型未初始化")
                return
        
        # 初始化关键变量，防止提前返回造成未定义错误
        frames = []
        frame_paths = []
        drawing_frames = []
        all_stroke_paths = []
        
        # 检查输入视频文件是否存在
        if not os.path.exists(video_path):
            print(f"错误: 输入视频文件不存在: {video_path}")
            self.processing_complete.emit(f"错误: 输入视频文件不存在: {video_path}")
            return
            
        # 检查输出文件夹是否存在，不存在则创建
        output_dir = os.path.dirname(output_path)
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
                print(f"已创建输出目录: {output_dir}")
            except Exception as e:
                print(f"创建输出目录失败: {str(e)}")
                self.processing_complete.emit(f"错误: 无法创建输出目录: {str(e)}")
                return
        
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                print(f"错误: 无法打开视频文件: {video_path}")
                self.processing_complete.emit(f"错误: 无法打开视频文件")
                return
                
        except Exception as e:
            print(f"打开视频文件时发生错误: {str(e)}")
            self.processing_complete.emit(f"错误: 打开视频文件时发生错误: {str(e)}")
            return
            
        # 获取视频信息
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # 计算视频原始宽高比并保存，用于后续绘画生成
        original_aspect_ratio = width / height
        print(f"视频信息 - 宽度: {width}, 高度: {height}, FPS: {fps}, 总帧数: {frame_count}, 宽高比: {original_aspect_ratio:.3f}")
        
        # 创建临时文件夹结构 - 使用输出路径所在目录
        # 所有临时文件都放在同一个文件夹中，以便于后续清理
        temp_frames_dir = os.path.join(temp_base_dir, "frames")
        temp_drawings_dir = os.path.join(temp_base_dir, "drawings")
        
        # 创建所有临时目录
        for temp_dir in [temp_frames_dir, temp_drawings_dir]:
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
                print(f"创建临时目录: {temp_dir}")
        
        # 每隔几帧处理一次，以减少计算量
        frame_step = max(1, frame_count // 8)  # 最多处理8帧，CPU友好
        print(f"图像处理步长: {frame_step}，将处理约 {min(8, frame_count)} 帧")
        
        # 添加随机偏移，增加内容多样性
        max_offset = int(frame_step * 0.5)  # 最大随机偏移量为步长的一半
        print(f"启用随机帧偏移，最大偏移量: ±{max_offset}帧")
        
        # 提取关键帧
        current_frame = 0
        max_attempts = 3  # 每个位置最多尝试几次查找包含角色的帧
        
        while current_frame < frame_count:
            found_character_frame = False
            attempts = 0
            best_frame = None
            best_score = -1
            best_frame_pos = -1
            
            # 尝试多次查找包含角色的帧
            while attempts < max_attempts and not found_character_frame:
                # 添加随机偏移，在高潮片段中随机取帧
                if max_offset > 0:
                    random_offset = random.randint(-max_offset, max_offset)
                    frame_pos = max(0, min(frame_count - 1, current_frame + random_offset))
                    print(f"尝试 {attempts+1}/{max_attempts}: 原始帧位置: {current_frame}, 随机偏移: {random_offset}, 最终位置: {frame_pos}")
                else:
                    frame_pos = current_frame
                    print(f"尝试 {attempts+1}/{max_attempts}: 帧位置: {frame_pos}")
                
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
                ret, frame = cap.read()
                if not ret:
                    print(f"警告: 读取视频帧 {frame_pos} 失败")
                    attempts += 1
                    continue
                
                # 分析帧内容，检查是否包含角色
                try:
                    frame_score = self.analyze_frame_content(frame)
                    print(f"帧 {frame_pos} 分析得分: {frame_score:.4f}")
                    
                    # 如果得分高于阈值，认为包含角色
                    if frame_score >= 0.3:  # 与前面的min_score_threshold保持一致
                        found_character_frame = True
                        best_frame = frame.copy()
                        best_score = frame_score
                        best_frame_pos = frame_pos
                        print(f"在位置 {frame_pos} 找到包含角色的帧，得分: {frame_score:.4f}")
                        break
                    elif frame_score > best_score:
                        # 保存目前最好的帧
                        best_frame = frame.copy()
                        best_score = frame_score
                        best_frame_pos = frame_pos
                except Exception as e:
                    print(f"分析帧 {frame_pos} 时出错: {str(e)}")
                
                attempts += 1
            
            # 如果尝试多次后仍未找到包含角色的帧，使用得分最高的帧
            if not found_character_frame and best_frame is not None:
                print(f"未找到包含角色的帧，使用得分最高的帧: 位置 {best_frame_pos}, 得分: {best_score:.4f}")
                frame = best_frame
                frame_pos = best_frame_pos
            elif not found_character_frame:
                print(f"警告: 未找到任何有效帧，跳过此位置")
                current_frame += frame_step
                continue
            
            frames.append(frame)
            # 保存关键帧
            frame_path = os.path.join(temp_frames_dir, f"frame_{len(frames)}.jpg")
            success = cv2.imwrite(frame_path, frame)
            if not success:
                print(f"警告: 无法保存帧到 {frame_path}")
            else:
                print(f"帧 {len(frames)} 已保存到 {frame_path}")
                frame_paths.append(frame_path)
            
            current_frame += frame_step
        
        print(f"成功提取并保存了 {len(frames)} 帧")
        cap.release()
        
        # 检查是否成功提取任何帧
        if not frame_paths:
            print("错误: 没有成功提取任何帧，无法继续处理")
            return
        
        # 预先检查是否使用自定义背景
        using_custom_background = self.background_folder and os.path.exists(self.background_folder)
        if using_custom_background:
            bg_files = [f for f in os.listdir(self.background_folder) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp'))]
            if bg_files:
                # 为整个视频随机选择一个背景图片，确保整个视频使用相同背景
                selected_bg_file = random.choice(bg_files)
                selected_bg_path = os.path.join(self.background_folder, selected_bg_file)
                print(f"为整个视频选择的自定义背景: {selected_bg_file}")
                
                # 保存背景信息到临时文件，供后续动画生成时使用
                bg_info_path = os.path.join(temp_base_dir, "background_info.txt")
                with open(bg_info_path, 'w') as f:
                    f.write(selected_bg_path)
            else:
                using_custom_background = False
                print("自定义背景文件夹中没有找到有效图片，将使用默认纸质纹理")
        
        # 为每个关键帧生成手绘效果
        for i, frame_path in enumerate(frame_paths):
            # 使用AI模型生成手绘效果
            print(f"使用大师级绘画风格处理帧 {i+1}/{len(frame_paths)}")
            
            # 检查文件是否存在
            if not os.path.exists(frame_path):
                print(f"错误: 帧文件不存在: {frame_path}")
                continue
                
            # 判断是否需要收集笔画路径（用于Grease Pencil）
            collect_paths = self.drawing_method == "grease_pencil" and self.blender_gp
            
            # 传递原始视频宽高比给绘图函数，确保生成图像维持相同比例
            drawing = self.generate_ai_drawing(frame_path, aspect_ratio=original_aspect_ratio)
            
            if drawing is None:
                print(f"错误: 生成的绘画图像为空，跳过帧 {i+1}")
                continue
            
            # 保存一个调试副本
            debug_path = os.path.join(temp_drawings_dir, f"debug_drawing_{i}.png")
            cv2.imwrite(debug_path, drawing)
            print(f"调试: 已保存原始绘画到 {debug_path}")
            
            # 提取笔画路径
            if collect_paths and hasattr(self, 'astar_planner'):
                # 提取边缘和路径
                gray = cv2.cvtColor(drawing, cv2.COLOR_BGR2GRAY)
                edges = cv2.Canny(gray, 50, 150)
                edges_dilated = cv2.dilate(edges, np.ones((3, 3), np.uint8), iterations=1)
                
                # 找到所有连通区域
                num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(edges_dilated, connectivity=8)
                
                # 存储当前帧的所有路径
                current_frame_paths = []  # 重命名避免与外部frame_paths冲突
                
                # 对每个连通区域规划路径
                print(f"开始处理连通区域，总数: {num_labels-1}")
                for j in range(1, min(num_labels, 50)):  # 跳过背景(0)，最多处理50个区域防止卡死
                    # 提取当前连通区域
                    component_mask = np.zeros_like(edges_dilated)
                    component_mask[labels == j] = 255
                    
                    # 只处理足够大的区域
                    if stats[j, cv2.CC_STAT_AREA] < 10:
                        continue
                        
                    # 为该连通区域规划路径
                    start_point = (int(centroids[j][0]), int(centroids[j][1]))
                    print(f"规划区域 {j}/{num_labels-1} 的路径...")
                    path = self.astar_planner.plan_stroke_path(component_mask, start_point)
                    
                    if path and len(path) > 1:
                        current_frame_paths.append(path)
                
                # 将当前帧的路径添加到总路径列表
                all_stroke_paths.extend(current_frame_paths)
                print(f"提取了 {len(current_frame_paths)} 条路径")
            
            # 调整大小以匹配原始视频，确保宽高比保持一致
            h, w = drawing.shape[:2]
            current_ratio = w / h
            
            # 如果现有比例与目标比例不一致，需要调整
            if abs(current_ratio - original_aspect_ratio) > 0.01:
                print(f"调整绘画图像比例: 当前比例 {current_ratio:.3f}, 目标比例 {original_aspect_ratio:.3f}")
                # 确保输出与原始视频完全相同的宽高比
                if width / height > w / h:  # 原视频比例更宽
                    new_w = w
                    new_h = int(w / original_aspect_ratio)
                else:  # 原视频比例更高
                    new_h = h
                    new_w = int(h * original_aspect_ratio)
                
                # 创建白色背景的画布
                canvas = np.ones((new_h, new_w, 3), dtype=np.uint8) * 255
                
                # 计算居中位置
                x_offset = (new_w - w) // 2
                y_offset = (new_h - h) // 2
                
                # 将绘画放在新画布的中心
                if x_offset >= 0 and y_offset >= 0:
                    canvas[y_offset:y_offset+h, x_offset:x_offset+w] = drawing
                else:
                    # 如果绘画比画布大，则需要裁剪
                    src_x = max(0, -x_offset)
                    src_y = max(0, -y_offset)
                    dst_x = max(0, x_offset)
                    dst_y = max(0, y_offset)
                    
                    src_w = min(w - src_x, new_w - dst_x)
                    src_h = min(h - src_y, new_h - dst_y)
                    
                    canvas[dst_y:dst_y+src_h, dst_x:dst_x+src_w] = drawing[src_y:src_y+src_h, src_x:src_x+src_w]
                
                drawing = canvas
            
            # 最后调整到原始视频的精确尺寸
            drawing = cv2.resize(drawing, (width, height))
            
            # 保存生成的手绘帧
            drawing_path = os.path.join(temp_drawings_dir, f"drawing_{i}.png")
            if cv2.imwrite(drawing_path, drawing):
                print(f"成功保存手绘帧到 {drawing_path}")
            else:
                print(f"无法保存手绘帧到 {drawing_path}")
            
            drawing_frames.append(drawing)
            print(f"已生成手绘帧 {i+1}/{len(frame_paths)}")
        
        # 创建输出视频
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        # 使用原始帧率，不再减半
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        # 为Blender创建专用临时目录在输出文件夹的统一临时目录下
        temp_blender_dir = os.path.join(self.output_folder, "autodraw_temp", "blender")
        if self.drawing_method == "grease_pencil" and not os.path.exists(temp_blender_dir):
            try:
                os.makedirs(temp_blender_dir)
                print(f"创建Blender临时目录: {temp_blender_dir}")
            except Exception as e:
                print(f"创建Blender临时目录失败: {str(e)}")
        
        # 始终使用标准绘画方式
        self._generate_standard_animation(out, drawing_frames, width, height, total_frames=frame_count)
        
        out.release()
        
        # 注意：不在这里清理临时文件，由调用者负责清理整个临时目录结构
        print(f"手绘动画生成完成: {output_path}")
    
    # 移除 _generate_grease_pencil_animation 和 _generate_astar_animation 方法
    
    def _generate_standard_animation(self, video_writer, drawing_frames, width, height, total_frames):
        """生成手绘动画效果，通过逐步显示线稿来模拟绘画过程"""
        drawing_frames_count = len(drawing_frames)
        print(f"总共有 {drawing_frames_count} 帧线稿图像")
        
        # 计算每帧线稿需要的总帧数 - 确保至少有足够的帧数
        frames_per_drawing = max(total_frames // drawing_frames_count, 1)
        print(f"每帧线稿分配 {frames_per_drawing} 帧视频")
        
        # 计算实际会生成的总帧数
        estimated_frames = 0
        
        # 检查是否有保存的背景图片信息
        # 首先尝试在特定视频的临时目录中查找
        # 假设文件名格式为output_path的目录名部分
        temp_base_dir = None
        if hasattr(self, 'output_folder') and self.output_folder:
            # 从文件名中获取视频标识符
            video_identifier = ""
            # 迭代查找所有可能的背景信息文件
            for root, dirs, files in os.walk(os.path.join(self.output_folder, "autodraw_temp")):
                for dir_name in dirs:
                    test_path = os.path.join(self.output_folder, "autodraw_temp", dir_name, "background_info.txt")
                    if os.path.exists(test_path):
                        print(f"找到背景信息文件: {test_path}")
                        temp_base_dir = os.path.join(self.output_folder, "autodraw_temp", dir_name)
                        break
                if temp_base_dir:
                    break
        
        # 如果找到了临时目录，使用特定的背景信息文件
        if temp_base_dir:
            bg_info_path = os.path.join(temp_base_dir, "background_info.txt")
        else:
            # 否则使用默认路径
            bg_info_path = os.path.join(self.output_folder, "autodraw_temp", "background_info.txt")
            
        custom_bg = None
        
        if os.path.exists(bg_info_path):
            try:
                with open(bg_info_path, 'r') as f:
                    bg_path = f.read().strip()
                    print(f"检测到保存的背景图片路径: {bg_path}")
                    if os.path.exists(bg_path):
                        # 读取背景图片
                        bg_img = cv2.imdecode(np.fromfile(bg_path, dtype=np.uint8), cv2.IMREAD_COLOR)
                        if bg_img is not None:
                            # 调整背景图片大小以匹配视频尺寸
                            custom_bg = cv2.resize(bg_img, (width, height))
                            print(f"成功加载自定义背景图片，尺寸: {custom_bg.shape}")
                        else:
                            print(f"无法读取背景图片: {bg_path}")
                    else:
                        print(f"背景图片不存在: {bg_path}")
            except Exception as e:
                print(f"加载背景图片时出错: {str(e)}")
                import traceback
                traceback.print_exc()
        
        # 处理每个线稿帧
        for frame_idx, drawing in enumerate(drawing_frames):
            print(f"处理线稿帧 {frame_idx+1}/{drawing_frames_count}")
            
            # 保存原始线稿用于调试到统一的临时目录
            debug_path = os.path.join(self.output_folder, "autodraw_temp", f"debug_original_{frame_idx}.png")
            cv2.imwrite(debug_path, drawing)
            
            # 创建线稿的二值版本（黑白）
            gray = cv2.cvtColor(drawing, cv2.COLOR_BGR2GRAY)
            _, binary = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY_INV)  # 反转颜色，线条为白色
            
            # 分割线稿为多个部分
            num_segments = min(9, frames_per_drawing // 5)  # 最多9个部分，但确保每个部分至少有5帧
            num_segments = max(num_segments, 1)  # 确保至少有1个部分
            print(f"将线稿分割为 {num_segments} 个部分")
            
            # 创建分割后的线稿
            segments = []
            
            # 使用连通组件分析将线稿分割为不同区域
            num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary, connectivity=8)
            
            # 如果连通组件太少，使用其他方法分割
            if num_labels - 1 < num_segments:
                print(f"连通组件数量不足 ({num_labels-1}), 使用网格分割")
                
                # 使用网格分割
                grid_size = int(np.sqrt(num_segments))
                cell_width = width // grid_size
                cell_height = height // grid_size
                
                for i in range(grid_size):
                    for j in range(grid_size):
                        if len(segments) < num_segments:
                            # 创建掩码
                            mask = np.zeros_like(binary)
                            # 设置当前网格单元的区域
                            x1, y1 = j * cell_width, i * cell_height
                            x2, y2 = min(width, (j+1) * cell_width), min(height, (i+1) * cell_height)
                            mask[y1:y2, x1:x2] = binary[y1:y2, x1:x2]
                            segments.append(mask)
            else:
                # 根据连通组件面积排序
                component_sizes = [(i, stats[i, cv2.CC_STAT_AREA]) for i in range(1, num_labels)]
                component_sizes.sort(key=lambda x: x[1], reverse=True)
                
                # 选择最大的几个组件
                selected_components = component_sizes[:num_segments]
                
                # 为每个选定的组件创建掩码
                for i, (label_idx, _) in enumerate(selected_components):
                    mask = np.zeros_like(binary)
                    mask[labels == label_idx] = 255
                    segments.append(mask)
                
                # 如果组件数量不足，添加随机组合的掩码
                while len(segments) < num_segments:
                    # 随机选择两个已有的掩码并合并
                    if len(segments) >= 2:
                        idx1, idx2 = random.sample(range(len(segments)), 2)
                        combined_mask = cv2.bitwise_or(segments[idx1], segments[idx2])
                        segments.append(combined_mask)
                    else:
                        # 如果只有一个掩码，创建其部分副本
                        mask = segments[0].copy()
                        h, w = mask.shape
                        # 只保留一半区域
                        mask[h//2:, :] = 0
                        segments.append(mask)
            
            # 确保有足够的段落
            print(f"创建了 {len(segments)} 个线稿段落")
            
            # 随机打乱段落顺序
            random.shuffle(segments)
            
            # 创建背景画布 - 使用自定义背景或纸质纹理背景
            if custom_bg is not None:
                canvas = custom_bg.copy()
                print("使用自定义背景图片作为画布")
            else:
                # 使用纸质纹理背景而不是纯白色
                print("使用默认纸质纹理作为画布")
                
                # 基础淡灰色背景 (220,220,220) - 更明显的灰色调
                canvas = np.ones((height, width, 3), dtype=np.uint8) * 220
                
                # 添加更强的随机噪声以模拟纸质纹理
                noise = np.random.randint(-15, 15, (height, width, 3), dtype=np.int16)
                
                # 创建纸质纹理的基本图案
                texture = np.zeros((height, width), dtype=np.uint8)
                
                # 添加细微纹理图案
                for y in range(0, height, 4):
                    for x in range(0, width, 4):
                        texture[y:min(y+4, height), x:min(x+4, width)] = np.random.randint(0, 8) 
                
                # 为纹理添加一些低频变化
                for i in range(30):  # 增加随机纹理图案数量
                    x1, y1 = np.random.randint(0, width), np.random.randint(0, height)
                    x2, y2 = np.random.randint(0, width), np.random.randint(0, height)
                    radius = np.random.randint(100, max(200, min(height, width)//2))
                    color_var = np.random.randint(-12, 8)  # 增加颜色变化范围
                    
                    # 在两点之间绘制平滑渐变
                    cv2.line(canvas, (x1, y1), (x2, y2), (220+color_var, 220+color_var, 220+color_var), thickness=radius, lineType=cv2.LINE_AA)
                
                # 应用噪声
                for ch in range(3):
                    canvas[:,:,ch] = np.clip(canvas[:,:,ch] + noise[:,:,ch], 0, 255).astype(np.uint8)
                
                # 保存一个背景样本以便调试
                if hasattr(self, 'output_folder') and self.output_folder:
                    sample_path = os.path.join(self.output_folder, "autodraw_temp", "bg_texture_sample.png")
                    cv2.imwrite(sample_path, canvas)
                    print(f"已保存纹理背景样本到: {sample_path}")
            
            # 先添加几帧空白
            for _ in range(3):
                video_writer.write(canvas.copy())
                estimated_frames += 1
            
            # 计算每个段落显示的帧数
            frames_per_segment = max(frames_per_drawing // len(segments), 1)
            
            # 逐步添加段落
            accumulated_mask = np.zeros_like(binary)
            
            for i, segment in enumerate(segments):
                # 将当前段落添加到累积掩码
                accumulated_mask = cv2.bitwise_or(accumulated_mask, segment)
                
                # 创建当前状态的线稿
                current_drawing = canvas.copy()
                # 将累积掩码应用到线稿
                current_drawing[accumulated_mask > 0] = (0, 0, 0)  # 黑色线条
                
                # 添加轻微的动画过渡
                transition_frames = min(5, frames_per_segment // 2)
                transition_frames = max(transition_frames, 1)  # 至少1帧
                
                # 前几帧逐渐显示当前段落
                for j in range(transition_frames):
                    alpha = j / transition_frames
                    transition_drawing = canvas.copy()
                    # 已有的线条保持不变
                    if i > 0:
                        prev_mask = np.zeros_like(binary)
                        for k in range(i):
                            prev_mask = cv2.bitwise_or(prev_mask, segments[k])
                        transition_drawing[prev_mask > 0] = (0, 0, 0)
                    
                    # 当前段落逐渐显示
                    segment_copy = segment.copy()
                    # 随机选择部分像素显示
                    random_mask = np.random.random(segment.shape) < alpha
                    segment_copy = segment_copy * random_mask.astype(np.uint8)
                    transition_drawing[segment_copy > 0] = (0, 0, 0)
                    
                    video_writer.write(transition_drawing)
                    estimated_frames += 1
                
                # 显示当前完整状态一段时间
                remaining_frames = max(frames_per_segment - transition_frames, 1)  # 至少1帧
                for _ in range(remaining_frames):
                    video_writer.write(current_drawing)
                    estimated_frames += 1
            
            # 确保最后显示完整线稿
            final_drawing = canvas.copy()
            final_drawing[binary > 0] = (0, 0, 0)  # 显示完整线稿
            
            # 添加最后几帧完整线稿 - 确保每个完整线稿停留2-3秒
            # 计算需要添加的帧数以达到2-3秒
            fps = 30  # 估计的帧率
            # 2.5秒，刚好处于2-3秒之间的中间值
            final_frames = int(fps * 2.5)  
            print(f"增加完整线稿显示时间: 添加{final_frames}帧 (约2.5秒)")
            for _ in range(final_frames):
                video_writer.write(final_drawing)
                estimated_frames += 1
        
        # 如果有多个线稿帧，处理帧间过渡
        if drawing_frames_count > 1:
            # 计算每个过渡需要的帧数
            transition_frames = max(total_frames // (drawing_frames_count * 2), 1)
            
            for i in range(drawing_frames_count - 1):
                base_frame = drawing_frames[i]
                next_frame = drawing_frames[i+1]
                
                # 为每个完整线稿帧增加2-3秒的显示时间
                fps = 30  # 估计的帧率
                pause_frames = int(fps * 2.5)  # 2.5秒，刚好处于2-3秒之间
                print(f"线稿帧 {i+1}/{drawing_frames_count} 停留 {pause_frames}帧 (约2.5秒)")
                for _ in range(pause_frames):
                    video_writer.write(base_frame)
                    estimated_frames += 1
                
                # 创建过渡帧
                for j in range(transition_frames):
                    alpha = j / transition_frames
                    blended = cv2.addWeighted(base_frame, 1-alpha, next_frame, alpha, 0)
                    video_writer.write(blended)
                    estimated_frames += 1
                    
            # 确保最后一帧也有足够的显示时间
            pause_frames = int(fps * 2.5)  # 同样是2.5秒
            print(f"最后一帧线稿 {drawing_frames_count}/{drawing_frames_count} 停留 {pause_frames}帧 (约2.5秒)")
            for _ in range(pause_frames):
                video_writer.write(drawing_frames[-1])
                estimated_frames += 1
        
        # 检查是否生成了足够的帧，如果不够，添加额外的帧以达到目标帧数
        remaining_frames = total_frames - estimated_frames
        if remaining_frames > 0:
            print(f"需要添加 {remaining_frames} 额外帧以匹配原始视频时长")
            # 使用最后一帧作为填充
            last_frame = drawing_frames[-1] if drawing_frames else np.ones((height, width, 3), dtype=np.uint8) * 255
            for _ in range(remaining_frames):
                video_writer.write(last_frame)
        
        print(f"标准动画生成完成，总共生成 {estimated_frames + max(0, remaining_frames)} 帧")
    
    def generate_ai_drawing(self, image_path, aspect_ratio=None):
        """使用AI模型生成大师级手绘效果"""
        print(f"开始生成手绘图像，源图像路径: {image_path}")
        # 检查文件是否存在
        if not os.path.exists(image_path):
            print(f"错误: 图像文件不存在: {image_path}")
            # 创建一个简单的替代图像作为应急措施
            fallback_image = np.ones((512, 512, 3), dtype=np.uint8) * 255
            cv2.putText(fallback_image, "Image Loading Error", (100, 256), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
            return fallback_image
            
        try:
            # 加载图像
            image = Image.open(image_path).convert("RGB")
            orig_width, orig_height = image.size
            print(f"图像加载成功，原始尺寸: {orig_width}x{orig_height}")
            
            # 保存原始尺寸信息，用于后续恢复
            original_size = (orig_width, orig_height)
            
            # 获取/计算宽高比
            if aspect_ratio is not None:
                # 如果提供了目标宽高比，记录但仍使用原图尺寸
                orig_aspect_ratio = aspect_ratio
                print(f"记录指定宽高比: {orig_aspect_ratio:.3f}，但保持原始图像尺寸")
            else:
                orig_aspect_ratio = orig_width / orig_height
                print(f"使用原图宽高比: {orig_aspect_ratio:.3f}")
            
            # 对于AI模型处理，可能需要调整尺寸以提高性能
            # 但我们会确保保持宽高比并在输出时恢复原始尺寸
            max_size = max(orig_width, orig_height)
            
            # 如果图像尺寸过大，可能导致内存问题，进行适当缩放处理
            if max_size > 1920:
                # 缩放但严格保持原始宽高比
                scale_factor = 1920 / max_size
                new_width = int(orig_width * scale_factor)
                new_height = int(orig_height * scale_factor)
                
                # 确保宽高为8的倍数(SD模型要求)
                new_width = (new_width // 8) * 8
                new_height = (new_height // 8) * 8
                
                # 临时缩放图像以进行处理
                image = image.resize((new_width, new_height), Image.LANCZOS)
                print(f"图像过大，临时缩放为: {new_width}x{new_height}，但最终输出将恢复原始尺寸")
            else:
                # 确保原始尺寸为8的倍数
                new_width = (orig_width // 8) * 8
                new_height = (orig_height // 8) * 8
                
                if new_width != orig_width or new_height != orig_height:
                    image = image.resize((new_width, new_height), Image.LANCZOS)
                    print(f"微调尺寸为8的倍数: {new_width}x{new_height}")
            
            # 使用ControlNet处理图像
            print(f"使用ControlNet生成手绘效果, 目标输出尺寸: {original_size}")
            result = self._generate_with_controlnet(image, target_aspect_ratio=orig_aspect_ratio, original_size=original_size)
            
            # 确保结果图像尺寸与原始图像完全一致
            if isinstance(result, np.ndarray):
                if result.shape[1] != orig_width or result.shape[0] != orig_height:
                    print(f"调整最终输出尺寸以匹配原始图像: {orig_width}x{orig_height}")
                    result = cv2.resize(result, (orig_width, orig_height), interpolation=cv2.INTER_LANCZOS4)
            
            return result
                
        except Exception as e:
            print(f"图像处理过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            # 创建错误提示图像
            error_image = np.ones((512, 512, 3), dtype=np.uint8) * 255
            cv2.putText(error_image, f"Error: {str(e)[:30]}", (50, 256), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
            return error_image
    
    # 添加ControlNet生成方法
    def _generate_with_controlnet(self, image, target_aspect_ratio=None, original_size=None):
        """使用ControlNet模型生成图像"""
        print("进入ControlNet生成函数...")
        try:
            # 检查controlnet是否已正确初始化
            if not hasattr(self, 'controlnet') or self.controlnet is None:
                print("错误: controlnet未被初始化!")
                error_image = np.ones((512, 512, 3), dtype=np.uint8) * 255
                cv2.putText(error_image, "ControlNet Model Not Initialized", (50, 256), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
                return error_image
            
            # 检查drawing_generator
            if not hasattr(self, 'drawing_generator'):
                print("错误: drawing_generator未被初始化!")
                # 尝试创建一个简单的drawing_generator
                try:
                    from diffusers import StableDiffusionControlNetPipeline, AutoencoderKL, UNet2DConditionModel
                    
                    # 创建基本组件
                    print("尝试从ControlNet模型创建Pipeline...")
                    
                    # 使用预训练的VAE和UNet
                    vae = AutoencoderKL.from_pretrained(
                        "runwayml/stable-diffusion-v1-5", 
                        subfolder="vae",
                        torch_dtype=torch.float32 if self.device == "cpu" else torch.float16
                    )
                    
                    unet = UNet2DConditionModel.from_pretrained(
                        "runwayml/stable-diffusion-v1-5", 
                        subfolder="unet",
                        torch_dtype=torch.float32 if self.device == "cpu" else torch.float16
                    )
                    
                    # 创建完整的pipeline
                    self.drawing_generator = StableDiffusionControlNetPipeline.from_pretrained(
                        "runwayml/stable-diffusion-v1-5",
                        controlnet=self.controlnet,
                        vae=vae,
                        unet=unet,
                        safety_checker=None,
                        feature_extractor=None,
                        torch_dtype=torch.float32 if self.device == "cpu" else torch.float16
                    )
                    
                    # 移动到设备
                    self.drawing_generator.to(self.device)
                    print("成功创建StableDiffusionControlNetPipeline")
                    
                    # 优化内存使用
                    if self.device != "cpu":
                        self.drawing_generator.enable_attention_slicing()
                except Exception as init_error:
                    print(f"无法创建ControlNet Pipeline: {str(init_error)}")
                    error_image = np.ones((512, 512, 3), dtype=np.uint8) * 255
                    cv2.putText(error_image, "Failed to create ControlNet Pipeline", (30, 256), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                    return error_image
                
            # 使用用户选择的绘画风格
            if hasattr(self, 'selected_styles') and self.selected_styles:
                # 随机选择一个用户选定的风格
                selected_prompt = random.choice(self.selected_styles)
            else:
                # 铅笔线稿风格提示词 - 更加强调细线条和干净的人物轮廓
                pencil_lineart_prompts = [
                    "clear character sketch, continuous thin lines, distinct character outlines, professional illustration, white background, recognizable character features",
                    "precise character line drawing, continuous clean lines, clear figure outlines, professional illustration, white background, distinct facial features",
                    "detailed character sketch, continuous thin lines, clear human figure, professional illustration, white background, recognizable character",
                    "clean character line art, continuous thin lines, clear figure drawing, professional illustration, white background, distinct character features"
                ]
                
                # 使用铅笔风格提示词
                selected_prompt = random.choice(pencil_lineart_prompts)
            
            # 打印当前使用的风格提示词
            print(f"使用绘画风格: {selected_prompt[:50]}...")
            
            # 记录模型调用开始
            print(f"开始调用ControlNet模型, 推理步数: {self.inference_steps}, 设备: {self.device}")
            
            # 将PIL图像转为numpy数组
            img_np = np.array(image)
            
            # 确定输出尺寸
            # 优先使用原始尺寸，如果有提供
            if original_size:
                output_width, output_height = original_size
                print(f"使用原始图像尺寸作为输出: {output_width}x{output_height}")
            else:
                # 回退到基于宽高比的尺寸计算
                if target_aspect_ratio is not None:
                    aspect_ratio = target_aspect_ratio
                else:
                    original_width, original_height = image.size
                    aspect_ratio = original_width / original_height
                
                # 确定生成图像的目标尺寸，保持宽高比
                base_size = 768  # 基础尺寸
                
                # 根据宽高比确定输出尺寸
                if aspect_ratio > 1:  # 横向图像
                    output_width = base_size
                    output_height = int(base_size / aspect_ratio)
                else:  # 纵向图像
                    output_height = base_size
                    output_width = int(base_size * aspect_ratio)
            
            # 确保尺寸为8的倍数(SD模型要求)
            output_width = (output_width // 8) * 8
            output_height = (output_height // 8) * 8
                
            print(f"ControlNet生成尺寸: {output_width}x{output_height}")
            
            # 增强预处理图像，提取突出人物/角色的线稿边缘
            print("增强预处理图像，提取突出人物/角色的线稿...")
            img_np = np.array(image)
            
            # 转为灰度图
            gray = cv2.cvtColor(img_np, cv2.COLOR_RGB2GRAY)
            
            # 应用CLAHE增强对比度，使人物特征更明显
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(gray)
            
            # 对图像进行双边滤波，保留边缘同时平滑区域
            bilateral = cv2.bilateralFilter(enhanced, 9, 75, 75)
            
            # 使用多尺度Canny边缘检测，捕获不同级别的细节
            edges1 = cv2.Canny(bilateral, 15, 70)   # 捕获更多细节边缘
            edges2 = cv2.Canny(enhanced, 50, 150)   # 主要轮廓
            
            # 合并边缘
            combined_edges = cv2.bitwise_or(edges1, edges2)
            
            # 使用自适应阈值二值化提取更多结构细节，特别是人物轮廓
            binary_adapt = cv2.adaptiveThreshold(enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                        cv2.THRESH_BINARY_INV, 11, 2)
            
            # 移除小的噪点
            kernel_small = np.ones((2, 2), np.uint8)
            binary_clean = cv2.morphologyEx(binary_adapt, cv2.MORPH_OPEN, kernel_small)
            
            # 对二值图像进行面积过滤，保留较大的连通区域（可能是人物）
            # 标记连通区域
            num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary_clean, connectivity=8)
            
            # 创建掩码，只保留较大的区域（可能是人物/角色）
            min_area = 100  # 调整最小区域面积阈值，平衡细节和噪点
            filtered_binary = np.zeros_like(binary_clean)
            for i in range(1, num_labels):  # 跳过背景（标签0）
                if stats[i, cv2.CC_STAT_AREA] > min_area:
                    filtered_binary[labels == i] = 255
            
            # 与边缘检测结果合并，确保保留重要线条
            combined = cv2.bitwise_or(combined_edges, filtered_binary)
            
            # 使用形态学操作连接断线
            kernel_connect = np.ones((2, 2), np.uint8)
            connected_lines = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel_connect)
            
            # 细化线条 - 使用形态学操作
            kernel_thin = np.ones((2, 2), np.uint8)
            thinned = cv2.erode(connected_lines, kernel_thin, iterations=1)
            
            # 创建白色背景
            white_bg = np.ones_like(thinned) * 255
            
            # 反转颜色：白色背景，黑色线条
            lineart = white_bg - thinned
            
            # 转回RGB格式
            lineart_rgb = cv2.cvtColor(lineart, cv2.COLOR_GRAY2RGB)
            
            # 转为PIL图像
            edges_pil = Image.fromarray(lineart_rgb)
            lineart_pil = edges_pil
            
            # 保存调试图像到输出目录的临时文件夹
            debug_edges_path = os.path.join(self.output_folder, "autodraw_temp", "debug_edges.png")
            cv2.imwrite(debug_edges_path, lineart)
            print(f"已保存边缘检测调试图像到: {debug_edges_path}")
            
            # 调用ControlNet模型生成图像
            try:
                # 增强的提示词和参数，突出人物/角色的线稿，减少背景
                enhanced_prompt = (
                    selected_prompt + 
                    ", clear character line drawing, focus on character only, continuous lines, " +
                    "distinct character outlines, recognizable human figure, " + 
                    "white background, professional character art, clear facial features" 
                )
                
                enhanced_negative = (
                    "messy drawing, broken lines, background details, dark areas, bold lines, " +
                    "thick outlines, noise, low quality, blurry, worst quality, color, " +
                    "harsh contrast, rough sketching, background, black blocks, scenery, discontinuous lines"
                )
                
                print(f"使用增强提示词: {enhanced_prompt[:100]}...")
                
                # 调用模型，优化参数突出人物/角色
                result = self.drawing_generator(
                    prompt=enhanced_prompt,
                    negative_prompt=enhanced_negative,
                    image=edges_pil,  # 使用增强的边缘检测图像
                    num_inference_steps=self.inference_steps,
                    guidance_scale=10.0,  # 提高指导比例以获得更清晰的结果
                    controlnet_conditioning_scale=0.8,  # 调整控制网络影响，确保清晰的人物线条
                    height=output_height,
                    width=output_width
                )
                
                # 检查结果是否有images属性
                if hasattr(result, 'images') and len(result.images) > 0:
                    result_image = result.images[0]
                    print("ControlNet模型调用完成，生成图像尺寸:", result_image.size)
                    
                    # 后处理：增强线条连续性并应用卡通风格
                    try:
                        # 转换为NumPy数组进行处理
                        result_np = np.array(result_image)
                        if len(result_np.shape) == 3 and result_np.shape[2] == 3:
                            # 转灰度
                            gray = cv2.cvtColor(result_np, cv2.COLOR_RGB2GRAY)
                            
                            # 使用自适应阈值提取人物线条，保留更多细节
                            binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                                        cv2.THRESH_BINARY_INV, 11, 3)
                            
                            # 对二值图像进行面积过滤，移除小噪点
                            num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary, connectivity=8)
                            filtered_binary = np.zeros_like(binary)
                            for i in range(1, num_labels):
                                if stats[i, cv2.CC_STAT_AREA] > 10:  # 降低阈值保留更多细节
                                    filtered_binary[labels == i] = 255
                            
                            # 连接断开的线条
                            kernel_close = np.ones((2, 2), np.uint8)
                            connected_lines = cv2.morphologyEx(filtered_binary, cv2.MORPH_CLOSE, kernel_close)
                            
                            # 细化线条
                            kernel = np.ones((2, 2), np.uint8)
                            thinned_lines = cv2.erode(connected_lines, kernel, iterations=1)
                            
                            # 转回RGB，使用黑色线条突出人物
                            white_bg = np.ones_like(result_np) * 255
                            # 使用纯黑色线条，使人物轮廓更加突出
                            white_bg[thinned_lines > 0] = [0, 0, 0]  # 黑色线条
                            result_np = white_bg
                            
                            # 转回PIL
                            result_image = Image.fromarray(result_np.astype('uint8'))
                            print("已应用线条增强处理，提高线条连续性")
                    except Exception as post_error:
                        print(f"线条增强后处理失败: {str(post_error)}")
                    
                    # 如果需要，将结果调整为原始图像尺寸
                    if original_size and (result_image.size[0] != original_size[0] or result_image.size[1] != original_size[1]):
                        result_image = result_image.resize(original_size, Image.LANCZOS)
                        print(f"将结果调整为原始尺寸: {original_size}")
                else:
                    # 如果没有images属性，可能是使用了DummyGenerator
                    print("警告: 结果对象没有images属性，使用线稿图像作为结果")
                    result_image = lineart_pil
                    
                    # 如果需要，将结果调整为原始图像尺寸
                    if original_size and (result_image.size[0] != original_size[0] or result_image.size[1] != original_size[1]):
                        result_image = result_image.resize(original_size, Image.LANCZOS)
                        print(f"将线稿调整为原始尺寸: {original_size}")
            except Exception as call_error:
                print(f"调用ControlNet模型出错: {str(call_error)}")
                # 如果调用失败，直接使用线稿图像
                result_image = lineart_pil
                print("使用线稿图像作为备用结果")
                
                # 如果需要，将结果调整为原始图像尺寸
                if original_size and (result_image.size[0] != original_size[0] or result_image.size[1] != original_size[1]):
                    result_image = result_image.resize(original_size, Image.LANCZOS)
                    print(f"将备用线稿调整为原始尺寸: {original_size}")
            
            # 将PIL图像转换为numpy数组/OpenCV格式
            drawing = np.array(result_image)
            if len(drawing.shape) == 2:  # 如果是灰度图
                drawing = cv2.cvtColor(drawing, cv2.COLOR_GRAY2BGR)
            else:
                drawing = cv2.cvtColor(drawing, cv2.COLOR_RGB2BGR)
                
            # 为线稿添加纸质纹理淡灰背景
            drawing = self._add_paper_texture(drawing)
            
            # 调试: 保存生成图像到输出目录的临时文件夹
            debug_path = os.path.join(self.output_folder, "autodraw_temp", "debug_controlnet_output.png")
            cv2.imwrite(debug_path, drawing)
            print(f"已保存调试图像到: {debug_path}")
            
            # 如果使用A*算法进行绘画路径规划
            if self.drawing_method == "astar" and self.astar_planner:
                print("使用A*算法处理生成的图像")
                processed_image = self._process_with_astar(drawing)
                
                # 确保最终输出与原始尺寸一致
                if original_size:
                    h, w = processed_image.shape[:2]
                    if w != original_size[0] or h != original_size[1]:
                        processed_image = cv2.resize(processed_image, original_size, interpolation=cv2.INTER_LANCZOS4)
                        print(f"将A*处理后的图像调整为原始尺寸: {original_size}")
                
                return processed_image
                
            # 确保最终输出与原始尺寸一致
            if original_size:
                h, w = drawing.shape[:2]
                if w != original_size[0] or h != original_size[1]:
                    drawing = cv2.resize(drawing, original_size, interpolation=cv2.INTER_LANCZOS4)
                    print(f"将最终图像调整为原始尺寸: {original_size}")
                
            print(f"ControlNet图像生成和转换完成，返回大小:{drawing.shape}")
            return drawing
            
        except Exception as e:
            print(f"ControlNet生成过程出错: {str(e)}")
            import traceback
            traceback.print_exc()
            # 创建错误提示图像
            error_image = np.ones((512, 512, 3), dtype=np.uint8) * 255
            cv2.putText(error_image, f"ControlNet Error: {str(e)[:30]}", (50, 256), 
                     cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
            return error_image


    

    def extract_video_segment(self, input_path, output_path, start_time, end_time):
        """提取视频片段，添加随机偏移以增加内容多样性"""
        # 添加随机偏移，增加内容多样性
        max_offset = 1.5  # 最大随机偏移量为1.5秒
        
        # 获取视频总时长
        try:
            duration_cmd = [
                'ffprobe', '-v', 'error', '-select_streams', 'v:0',
                '-show_entries', 'format=duration', '-of', 'csv=p=0',
                input_path
            ]
            result = subprocess.run(duration_cmd, capture_output=True, text=True)
            total_duration = float(result.stdout.strip())
            
            # 计算随机偏移量，确保不超出视频范围
            random_offset = random.uniform(-max_offset, max_offset)
            
            # 确保偏移后的时间点仍在有效范围内
            new_start = max(0, min(total_duration - 1, start_time + random_offset))
            new_end = min(total_duration, end_time + random_offset)
            
            # 确保片段长度至少为5秒
            if new_end - new_start < 5 and new_end < total_duration:
                new_end = min(total_duration, new_start + 5)
            
            print(f"提取视频片段: {input_path} -> {output_path}")
            print(f"原始时间范围: {start_time:.2f}s - {end_time:.2f}s, 随机偏移: {random_offset:.2f}s")
            print(f"最终时间范围: {new_start:.2f}s - {new_end:.2f}s")
            
            duration = new_end - new_start
        except Exception as e:
            print(f"计算随机偏移时出错: {str(e)}，使用原始时间范围")
            print(f"提取视频片段: {input_path} -> {output_path}, 时间范围: {start_time:.2f}s - {end_time:.2f}s")
            new_start = start_time
            duration = end_time - start_time
        
        cmd = [
            'ffmpeg', '-i', input_path, 
            '-ss', str(new_start), '-t', str(duration),
            '-c:v', 'libx264', '-c:a', 'aac', 
            '-y',  # 覆盖已有文件
            output_path
        ]
        
        try:
            print(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, stderr=subprocess.PIPE, stdout=subprocess.PIPE, text=True)
            
            if result.returncode != 0:
                print(f"FFmpeg命令执行失败，返回码: {result.returncode}")
                print(f"错误输出: {result.stderr}")
                return False
                
            # 检查输出文件是否存在
            if not os.path.exists(output_path):
                print(f"错误: 输出文件不存在: {output_path}")
                return False
                
            # 检查输出文件大小
            file_size = os.path.getsize(output_path)
            if file_size == 0:
                print(f"错误: 输出文件大小为0: {output_path}")
                return False
                
            print(f"视频片段提取成功: {output_path}, 文件大小: {file_size/1024/1024:.2f} MB")
            return True
        except Exception as e:
            print(f"提取视频片段时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def merge_with_template(self, input_video_path, output_path):
        """将模板视频的前2帧与输入视频合并"""
        if not self.template_folder or not os.path.exists(self.template_folder):
            print("模板文件夹不存在，跳过模板合并")
            # 如果没有模板文件夹，直接复制输入视频到输出路径
            shutil.copy2(input_video_path, output_path)
            return output_path
            
        # 获取模板文件夹中的所有视频文件
        template_videos = []
        for ext in ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv']:
            template_videos.extend(glob.glob(os.path.join(self.template_folder, f'*{ext}')))
            
        if not template_videos:
            print("模板文件夹中没有视频文件，跳过模板合并")
            shutil.copy2(input_video_path, output_path)
            return output_path
            
        # 随机选择一个模板视频
        template_video_path = random.choice(template_videos)
        print(f"选择模板视频: {template_video_path}")
        
        # 提取模板视频的前2帧到临时文件
        temp_dir = os.path.join(self.output_folder, "temp")
        os.makedirs(temp_dir, exist_ok=True)
        temp_template_path = os.path.join(temp_dir, "template_frames.mp4")
        
        # 提取模板视频的前2帧
        try:
            command = [
                'ffmpeg', '-y',
                '-i', template_video_path,
                '-vframes', '2',   # 只提取2帧
                '-c:v', 'copy',    # 复制视频流
                '-an',             # 不包含音频
                temp_template_path
            ]
            print(f"提取模板视频前2帧: {' '.join(command)}")
            subprocess.run(command, check=True)
        except Exception as e:
            print(f"提取模板视频前2帧失败: {str(e)}")
            shutil.copy2(input_video_path, output_path)
            return output_path
            
        # 合并模板视频前1秒和输入视频（直接复制流）
        temp_list_file = os.path.join(temp_dir, "concat_list.txt")
        
        # 确保视频格式兼容，添加demuxer选项
        with open(temp_list_file, 'w', encoding='utf-8') as f:
            f.write(f"file '{os.path.abspath(temp_template_path)}'\n")
            f.write(f"file '{os.path.abspath(input_video_path)}'\n")
            
        try:
            # 检查模板视频是否有效
            check_cmd = [
                'ffprobe', '-v', 'error',
                '-select_streams', 'v:0',
                '-show_entries', 'stream=codec_name,width,height',
                '-of', 'csv=s=x:p=0',
                temp_template_path
            ]
            check_result = subprocess.run(check_cmd, capture_output=True, text=True)
            print(f"模板视频信息: {check_result.stdout.strip()}")
            
            # 检查输入视频信息
            check_input_cmd = [
                'ffprobe', '-v', 'error',
                '-select_streams', 'v:0',
                '-show_entries', 'stream=codec_name,width,height',
                '-of', 'csv=s=x:p=0',
                input_video_path
            ]
            check_input_result = subprocess.run(check_input_cmd, capture_output=True, text=True)
            print(f"输入视频信息: {check_input_result.stdout.strip()}")
            
            # 提取输入视频的音频
            temp_audio_path = os.path.join(temp_dir, "temp_audio.aac")
            extract_audio_cmd = [
                'ffmpeg', '-y',
                '-i', input_video_path,
                '-vn',  # 不包含视频
                '-acodec', 'copy',  # 复制音频流
                temp_audio_path
            ]
            print(f"提取音频: {' '.join(extract_audio_cmd)}")
            subprocess.run(extract_audio_cmd, check=True)
            
            # 合并视频并添加音频
            command = [
                'ffmpeg', '-y',
                '-f', 'concat',
                '-safe', '0',
                '-i', temp_list_file,  # 视频部分
                '-i', temp_audio_path, # 音频部分
                '-c:v', 'copy',        # 复制视频流
                '-c:a', 'copy',        # 复制音频流
                '-map', '0:v',         # 使用concat文件中的视频
                '-map', '1:a',         # 使用提取的音频
                '-shortest',           # 使用最短的流长度
                output_path
            ]
            print(f"执行合并命令: {' '.join(command)}")
            subprocess.run(command, check=True)
            
            # 清理临时音频文件
            try:
                os.remove(temp_audio_path)
            except:
                pass
        except Exception as e:
            print(f"合并视频失败: {str(e)}")
            shutil.copy2(input_video_path, output_path)
            return output_path
            
        # 清理临时文件
        try:
            if os.path.exists(temp_template_path):
                os.remove(temp_template_path)
            if os.path.exists(temp_list_file):
                os.remove(temp_list_file)
        except Exception as cleanup_error:
            print(f"清理临时文件时出错: {str(cleanup_error)}")
            
        return output_path
            
    def combine_videos(self, video_a_path, drawing_video_path, audio_path, output_path, audio_volume, original_video_path=None):
        """拼接视频并添加音频 - 使用提供的原始视频或片段视频路径"""
        # 获取输出目录
        output_dir = os.path.dirname(os.path.abspath(output_path))
        
        # 如果提供了原始视频路径，使用它
        if original_video_path and os.path.exists(original_video_path):
            video_a_path = original_video_path
            print(f"使用指定的原始视频: {original_video_path}")
        else:
            # 保留传入的视频路径，确保使用与当前处理相匹配的视频
            print(f"使用传入的视频路径: {video_a_path}")
            
        print(f"确认使用视频: {video_a_path}")
        
        # 获取视频A的信息
        probe_cmd = [
            'ffprobe', '-v', 'error', '-select_streams', 'v:0',
            '-show_entries', 'stream=width,height,display_aspect_ratio',
            '-of', 'csv=s=x:p=0', video_a_path
        ]
        result = subprocess.run(probe_cmd, capture_output=True, text=True)
        video_a_info = result.stdout.strip().split('x')
        video_a_width = int(video_a_info[0])
        video_a_height = int(video_a_info[1])
        
        # 获取视频A的时长
        duration_cmd = [
            'ffprobe', '-v', 'error', '-select_streams', 'v:0',
            '-show_entries', 'format=duration', '-of', 'csv=p=0',
            video_a_path
        ]
        result_duration = subprocess.run(duration_cmd, capture_output=True, text=True)
        try:
            video_a_duration = float(result_duration.stdout.strip())
            print(f"原始视频的时长: {video_a_duration:.2f}秒")
        except (ValueError, IndexError):
            video_a_duration = None
            print("无法获取原始视频的时长，将不进行时长匹配")
        
        # 获取绘画视频信息
        probe_cmd_drawing = [
            'ffprobe', '-v', 'error', '-select_streams', 'v:0',
            '-show_entries', 'stream=width,height',
            '-of', 'csv=s=x:p=0', drawing_video_path
        ]
        result_drawing = subprocess.run(probe_cmd_drawing, capture_output=True, text=True)
        drawing_info = result_drawing.stdout.strip().split('x')
        drawing_width = int(drawing_info[0])
        drawing_height = int(drawing_info[1])
        
        # 获取绘画视频的时长
        duration_cmd_drawing = [
            'ffprobe', '-v', 'error', '-select_streams', 'v:0',
            '-show_entries', 'format=duration', '-of', 'csv=p=0',
            drawing_video_path
        ]
        result_duration_drawing = subprocess.run(duration_cmd_drawing, capture_output=True, text=True)
        try:
            drawing_duration = float(result_duration_drawing.stdout.strip())
            print(f"绘画视频的时长: {drawing_duration:.2f}秒")
        except (ValueError, IndexError):
            drawing_duration = None
            print("无法获取绘画视频的时长")
        
        print(f"原视频尺寸: {video_a_width}x{video_a_height}, 绘画视频尺寸: {drawing_width}x{drawing_height}")
        
        # 判断视频A是横屏还是竖屏
        is_landscape = video_a_width > video_a_height
        is_vertical_a = video_a_height > video_a_width
        print(f"原始视频是{'竖屏' if is_vertical_a else '横屏'}")
        
        # 强制设置为9:16的竖屏分辨率，适合快手和抖音平台
        # 竖屏视频使用1080x1920
        target_width = 1080
        target_height = 1920
        
        # 使用9:16的竖屏分辨率，上下拼接
        # 计算每个视频应占据的高度
        half_height = target_height // 2
        
        # 对于竖屏视频，保持原始宽高比
        new_width_a = target_width
        new_height_a = half_height
        
        if is_vertical_a:
            # 竖屏视频，保持宽高比，限制宽度在红线范围内
            # 假设红线范围是总宽度的60-70%
            vertical_width_ratio = 0.65  # 竖屏视频宽度占总宽度的比例
            vertical_video_width = int(target_width * vertical_width_ratio)
            
            # 计算保持宽高比的高度
            video_ratio = video_a_height / video_a_width
            vertical_video_height = int(vertical_video_width * video_ratio)
            
            # 如果计算出的高度超过了half_height，则以height为基准重新计算宽度
            if vertical_video_height > half_height:
                vertical_video_height = half_height
                vertical_video_width = int(half_height / video_ratio)
            
            # 更新缩放尺寸
            new_width_a = vertical_video_width
            new_height_a = vertical_video_height
            
            print(f"竖屏视频将保持宽高比，调整大小为: {new_width_a}x{new_height_a}")
        else:
            print(f"原始视频是横屏，将进行适当拉伸以填满屏幕宽度")
        
        # 对于绘画视频 - 完全强制拉伸以填满屏幕底部
        new_width_drawing = target_width  # 强制使用完整宽度
        new_height_drawing = half_height  # 强制使用半屏高度
        
        print(f"设置原视频尺寸为: {new_width_a}x{new_height_a}, 线稿视频尺寸为: {new_width_drawing}x{new_height_drawing}")
        print(f"特别注意: 线稿视频将完全拉伸以填充整个底部区域，不保留原始比例")
        
        # 根据视频类型选择缩放命令
        if is_vertical_a:
            # 竖屏视频保持宽高比
            scale_a = f"scale={new_width_a}:{new_height_a}:force_original_aspect_ratio=decrease"
            # 计算居中所需的偏移量
            x_offset = (target_width - new_width_a) // 2
            # 添加黑边使视频居中
            pad_a = f"pad={target_width}:{new_height_a}:{x_offset}:0:color=black"
        else:
            # 横屏视频强制拉伸
            scale_a = f"scale={new_width_a}:{new_height_a}:force_original_aspect_ratio=disable"
            pad_a = "null"  # 直接传递
        
        # 线稿视频仍然强制拉伸填充底部
        scale_drawing = f"scale={new_width_drawing}:{new_height_drawing}:force_original_aspect_ratio=disable"
        pad_drawing = "null"  # 直接传递
        
        # 创建渐变过渡效果区域
        # 减小渐变区域的高度至2%，让拼接更不明显
        blend_height = int(target_height * 0.02)
        print(f"渐变区域高度: {blend_height}像素")
        
        # 首先计算重叠区域的垂直位置
        overlap_position = new_height_a - blend_height
        
        # 检查是否有自定义背景
        bg_texture_color = "white"  # 默认背景色
        
        # 检查是否有背景信息文件
        bg_info_path = None
        
        # 搜索可能的背景信息文件位置
        if hasattr(self, 'output_folder') and self.output_folder:
            # 迭代查找所有可能的背景信息文件
            for root, dirs, files in os.walk(os.path.join(self.output_folder, "autodraw_temp")):
                for dir_name in dirs:
                    test_path = os.path.join(self.output_folder, "autodraw_temp", dir_name, "background_info.txt")
                    if os.path.exists(test_path):
                        print(f"视频拼接：找到背景信息文件: {test_path}")
                        bg_info_path = test_path
                        break
                if bg_info_path:
                    break
                    
            # 如果未找到，尝试使用默认路径
            if not bg_info_path:
                default_path = os.path.join(self.output_folder, "autodraw_temp", "background_info.txt")
                if os.path.exists(default_path):
                    bg_info_path = default_path
                    print(f"视频拼接：使用默认背景信息文件: {default_path}")
        
        # 如果找到背景信息文件，读取背景图片并提取平均颜色
        if bg_info_path and os.path.exists(bg_info_path):
            try:
                with open(bg_info_path, 'r') as f:
                    bg_path = f.read().strip()
                    print(f"视频拼接：检测到背景图片路径: {bg_path}")
                    if os.path.exists(bg_path):
                        # 读取背景图片获取主色调
                        bg_img = cv2.imdecode(np.fromfile(bg_path, dtype=np.uint8), cv2.IMREAD_COLOR)
                        if bg_img is not None:
                            # 计算背景图片的平均颜色
                            avg_color = np.mean(bg_img, axis=(0, 1))
                            # 将BGR转为RGB并格式化为十六进制颜色代码
                            r, g, b = int(avg_color[2]), int(avg_color[1]), int(avg_color[0])
                            bg_texture_color = f"0x{r:02x}{g:02x}{b:02x}"
                            print(f"视频拼接：使用背景图片平均颜色: {bg_texture_color}")
                            
                            # 保存一个颜色样本图片用于调试
                            color_sample = np.ones((100, 100, 3), dtype=np.uint8)
                            color_sample[:,:,0] = b
                            color_sample[:,:,1] = g
                            color_sample[:,:,2] = r
                            sample_path = os.path.join(self.output_folder, "bg_color_sample.png")
                            cv2.imwrite(sample_path, color_sample)
                            print(f"视频拼接：已保存背景颜色样本到: {sample_path}")
            except Exception as e:
                print(f"视频拼接：获取背景颜色时出错: {str(e)}")
                import traceback
                traceback.print_exc()
        
        # 构建修改后的滤镜链，根据视频类型选择不同的处理方式
        if is_vertical_a:
            filter_complex = (
                # 竖屏视频先按比例缩放，然后居中添加黑边
                f"[0:v]{scale_a},{pad_a}[v0scaled];" \
                # 线稿视频强制拉伸填充底部
                f"[1:v]scale={new_width_drawing}:{new_height_drawing + 20}:force_original_aspect_ratio=disable,setsar=1[v1scaled];" \
                # 创建纯黑色背景并将原视频添加到上部
                f"[v0scaled]pad=iw:ih+{new_height_drawing}:0:0:color=black[background];" \
                # 线稿视频顶部添加平滑渐变效果
                f"[v1scaled]format=rgba,geq=r='r(X,Y)':g='g(X,Y)':b='b(X,Y)':a='if(lt(Y,{blend_height}),sin(PI*Y/(2*{blend_height}))*255,255)'[overlay_with_fade];" \
                # 将带透明渐变的线稿覆盖到底部
                f"[background][overlay_with_fade]overlay=0:{overlap_position}:shortest=0[v]"
            )
        else:
            filter_complex = (
                # 原视频缩放
                f"[0:v]scale={new_width_a}:{new_height_a}:force_original_aspect_ratio=disable,setsar=1[v0scaled];" \
                # 线稿视频强制拉伸
                f"[1:v]scale={new_width_drawing}:{new_height_drawing + 20}:force_original_aspect_ratio=disable,setsar=1[v1scaled];" \
                # 创建纯黑色背景并将原视频添加到上部
                f"[v0scaled]pad=iw:ih+{new_height_drawing}:0:0:color=black[background];" \
                # 线稿视频顶部添加平滑渐变效果
                f"[v1scaled]format=rgba,geq=r='r(X,Y)':g='g(X,Y)':b='b(X,Y)':a='if(lt(Y,{blend_height}),sin(PI*Y/(2*{blend_height}))*255,255)'[overlay_with_fade];" \
                # 将带透明渐变的线稿覆盖到底部
                f"[background][overlay_with_fade]overlay=0:{overlap_position}:shortest=0[v]"
            )
        
        # 打印详细的调试信息
        print(f"视频拼接详情:")
        print(f"原视频: {video_a_width}x{video_a_height}, 绘画视频: {drawing_width}x{drawing_height}")
        print(f"目标尺寸: {target_width}x{target_height}")
        print(f"缩放后尺寸 - 原视频: {new_width_a}x{new_height_a}, 绘画视频: {new_width_drawing}x{new_height_drawing}")
        
        # 执行全方位深度去重处理 - 在合并前对原视频和线稿视频进行处理(包括画面和声音)
        print("执行全方位深度去重处理(视频+音频) - 用于抖音快手发布...")
        temp_dedup_dir = os.path.join(output_dir, "dedup_temp")
        os.makedirs(temp_dedup_dir, exist_ok=True)
        
        # 必须使用原始完整视频进行深度去重处理
        if original_video_path and os.path.exists(original_video_path):
            print(f"使用完整原始视频进行深度去重处理: {original_video_path}")
            # 对原视频进行深度去重处理
            temp_video_a_dedup = os.path.join(temp_dedup_dir, "video_a_dedup.mp4")
            self.deep_video_deduplication_qsv(original_video_path, temp_video_a_dedup)
            if os.path.exists(temp_video_a_dedup):
                video_a_path = temp_video_a_dedup
                print(f"使用去重后的原视频: {video_a_path}")
            else:
                print(f"深度去重处理失败，使用原始视频: {video_a_path}")
        else:
            print(f"未找到完整原始视频，跳过深度去重处理")
            
        # 不处理线稿视频，保持原样
        print(f"线稿视频保持原样，不进行去重处理: {drawing_video_path}")
        
        # 计算缩放比例（而非拉伸率）
        width_ratio_a = new_width_a / video_a_width
        height_ratio_a = new_height_a / video_a_height
        width_ratio_drawing = new_width_drawing / drawing_width
        height_ratio_drawing = new_height_drawing / drawing_height
        
        print(f"缩放比例 - 原视频: 宽度 {width_ratio_a:.2f}倍, 高度 {height_ratio_a:.2f}倍")
        print(f"缩放比例 - 线稿视频: 宽度 {width_ratio_drawing:.2f}倍, 高度 {height_ratio_drawing:.2f}倍")
        print(f"完整的filter_complex: {filter_complex}")
        
        # 音频混合 - 添加深度去重处理，优化快手抖音平台发布
        audio_mix = (
            # 原始视频音频进行深度去重处理
            f"[0:a]volume=1," \
            # 应用多层音频变换以避免内容识别
            f"highpass=f=30,lowpass=f=14000," \
            # 添加变声效果并保持音画同步
            f"aresample=async=1," \
            # 添加轻微动态范围压缩
            f"compand=attacks=0.02:decays=0.2:points=-70/-70|-40/-25|-10/-10|0/-5:soft-knee=6," \
            # 添加轻微混响效果，增加深度差异化
            f"aecho=0.8:0.7:40:0.5[a1];"
            
            # 副音频单独应用音量控制参数
            f"[2:a]volume={audio_volume}[a2];"
            
            # 然后混合两个音频流，确保最终音频质量
            f"[a1][a2]amix=inputs=2:duration=first:dropout_transition=0," \
            # 最终音频标准化处理，提高音频质量
            f"loudnorm=I=-16:TP=-1.5:LRA=11[a]"
        )
        
        # 处理文件路径，确保路径中的空格和特殊字符被正确处理
        # 将反斜杠转换为正斜杠，避免Windows路径问题
        video_a_path = video_a_path.replace('\\', '/')
        drawing_video_path = drawing_video_path.replace('\\', '/')
        audio_path = audio_path.replace('\\', '/')
        output_path = output_path.replace('\\', '/')
        
        # 使用临时文件存储复杂的滤镜图，避免命令行参数解析问题
        # 创建临时滤镜文件
        filter_file_path = os.path.join(os.path.dirname(output_path), "filter_complex.txt")
        
        # 组合完整的滤镜表达式并写入文件
        full_filter = f"{filter_complex};{audio_mix}"
        with open(filter_file_path, "w") as f:
            f.write(full_filter)
        
        # 加上引号保护路径
        video_a_path_safe = f'"{video_a_path}"'
        drawing_video_path_safe = f'"{drawing_video_path}"'
        audio_path_safe = f'"{audio_path}"'
        output_path_safe = f'"{output_path}"'
        filter_file_path_safe = f'"{filter_file_path}"'
        
        # 构建不使用复杂滤镜表达式的FFmpeg命令
        ffmpeg_cmd = f'ffmpeg -y -i {video_a_path_safe} -stream_loop -1 -i {drawing_video_path_safe} -i {audio_path_safe}'
        
        # 如果能获取到视频A的时长，添加时长参数确保输出视频与原视频时长一致
        if video_a_duration:
            # 使用滤镜文件而不是直接在命令行指定滤镜，设置60帧率
            ffmpeg_cmd += f" -filter_complex_script {filter_file_path_safe} -map \"[v]\" -map \"[a]\" -c:v libx264 -r 60 -c:a aac -t {video_a_duration}"
            print(f"设置输出视频时长为: {video_a_duration:.2f}秒")
            print(f"已启用绘画视频循环填充，确保匹配原视频时长")
        else:
            # 如果无法获取原视频时长，使用标准命令，设置60帧率
            ffmpeg_cmd += f" -filter_complex_script {filter_file_path_safe} -map \"[v]\" -map \"[a]\" -c:v libx264 -r 60 -c:a aac"
            print("无法获取原视频时长，但仍启用绘画视频循环填充")
        
        # 添加输出路径
        ffmpeg_cmd += f' {output_path_safe}'
        
        print(f"执行FFmpeg命令: {ffmpeg_cmd}")
        print(f"滤镜文件内容: {full_filter}")
        
        try:
            # 使用shell=True但不使用text=True，避免编码问题
            result = subprocess.run(ffmpeg_cmd, shell=True, stderr=subprocess.PIPE, stdout=subprocess.PIPE, text=False)
            
            if result.returncode != 0:
                print(f"FFmpeg执行失败，错误码: {result.returncode}")
                # 安全地解码错误信息，忽略不可解码的字符
                try:
                    stderr_text = result.stderr.decode('utf-8', errors='ignore')
                except:
                    stderr_text = "无法解码错误信息"
                print(f"错误信息: {stderr_text[:200]}..." if len(stderr_text) > 200 else f"错误信息: {stderr_text}")
            else:
                print(f"FFmpeg执行成功，输出文件: {output_path}")
                
            # 检查输出文件是否存在
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"输出文件大小: {file_size/1024/1024:.2f} MB")
                
                # 注意：视频已经在合并前进行过深度去重处理，不需要再次处理
                print(f"视频已在合并前进行过深度去重处理，最终文件准备完成")
                
            else:
                print(f"警告: 输出文件不存在: {output_path}")
                
        except Exception as e:
            print(f"执行FFmpeg命令时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            
        # 清理临时滤镜文件
        try:
            if os.path.exists(filter_file_path):
                os.remove(filter_file_path)
                print(f"已清理临时滤镜文件: {filter_file_path}")
        except:
            pass

    def deep_video_deduplication_qsv(self, input_video_path, output_video_path):
        """全方位深度去重（视频+音频），专为快手、抖音等平台优化，通过修改视频和音频特征避开内容检测算法，不影响正常观看体验"""
        print(f"开始对视频进行全方位深度去重处理(视频+音频): {input_video_path}")
        
        # 创建临时文件夹
        temp_dir = os.path.join(os.path.dirname(output_video_path), "dedup_temp_qsv")
        os.makedirs(temp_dir, exist_ok=True)
        
        # 临时文件路径
        temp_output = os.path.join(temp_dir, "dedup_video_qsv.mp4")
        
        # 构建深度去重滤镜链 - 优化高清质量，添加随机色块和RGB抖动
        dedup_filters = (
            # 使用轻微缩放，减少变形
            "scale=iw*1.01:ih*1.01:flags=lanczos," 
            # 轻微裁剪，保持画面完整性
            "crop=iw*0.99:ih*0.99," 
            # 非常轻微的随机旋转，避免明显变形
            f"rotate={random.uniform(-0.5, 0.5)*3.14159/180}," 
            # 轻微调整亮度、对比度和饱和度
            f"eq=brightness={random.uniform(0.02, 0.05)}:contrast={random.uniform(1.02, 1.05)}:saturation={random.uniform(1.02, 1.05)}:gamma={random.uniform(0.95, 1.05)}," 
            # 添加轻微锐化
            "unsharp=3:3:0.8:3:3:0.0," 
            # 添加几乎不可见的水印
            f"drawbox=x=iw/2+{random.randint(-100, 100)}:y=ih/2+{random.randint(-100, 100)}:w=10:h=10:t=1:c=white@0.02," 
            # 轻微调整色彩
            f"hue=h={random.uniform(-5, 5)}:s={random.uniform(1.0, 1.1)},"
            # 添加RGB轻微抖动效果
            f"colorchannelmixer=rr=1:rb={random.uniform(-0.05, 0.05)}:gr={random.uniform(-0.05, 0.05)}:gg=1:bb=1,"
            # 添加几个随机色块（简化版本，固定数量）
            f"drawbox=x={random.randint(0, 500)}:y={random.randint(0, 500)}:w=5:h=5:color=0xff0000@0.2:t=fill,"
            f"drawbox=x={random.randint(0, 500)}:y={random.randint(0, 500)}:w=6:h=6:color=0x00ff00@0.2:t=fill,"
            f"drawbox=x={random.randint(0, 500)}:y={random.randint(0, 500)}:w=4:h=4:color=0x0000ff@0.2:t=fill,"
            # RGB轻微抖动效果（替代像素化效果，更简单可靠）
            f"eq=saturation={random.uniform(1.0, 1.05)}:gamma={random.uniform(0.97, 1.03)}:contrast={random.uniform(1.0, 1.05)},"
            # 添加非常轻微的噪点
            "noise=c0s=3:allf=t"
        )
        
        # 生成随机的关键帧间隔，在1-4秒之间
        key_frame_min = 1
        key_frame_max = 4
        
        # 创建随机的关键帧时间点字符串，生成3-5个随机时间点（减少数量以提高兼容性）
        key_frame_count = random.randint(3, 5)
        key_frame_times = []
        for i in range(key_frame_count):
            key_frame_times.append(str(random.uniform(key_frame_min, key_frame_max*i)))
        key_frame_expr = "expr:gte(t," + "+".join(key_frame_times) + ")"
        
                 # 构建使用软件编码的FFmpeg命令，仅处理视频，不处理音频
        cmd = [
            'ffmpeg', '-y',
            '-i', input_video_path,
            '-vf', dedup_filters,
            # 使用软件编码
            '-c:v', 'libx264',
            # 使用较快的预设，平衡速度和质量
            '-preset', 'medium',
            # 设置较高的视频质量
            '-crf', '16',
            # 设置较高的视频码率，保证高清质量
            '-b:v', '5M', '-maxrate', '6M', '-bufsize', '12M',
            # 随机设置GOP大小，在30-50之间
            '-g', str(random.randint(30, 50)),
            # 强制在随机时间点插入关键帧
            '-force_key_frames', key_frame_expr,
            # 随机场景分割，在0.25-0.35之间设置阈值
            '-sc_threshold', str(random.uniform(0.25, 0.35)),
            # 直接复制音频流，不做处理
            '-c:a', 'copy',
            # 保持视频帧率
            '-fps_mode', 'passthrough',
            temp_output
        ]
        
        try:
            print(f"执行视频去重命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, stderr=subprocess.PIPE, stdout=subprocess.PIPE, text=True)
            
            if result.returncode != 0:
                print(f"主要视频去重处理失败: {result.stderr}")
                print("尝试使用备用编码方式...")
                
                # 使用更快的编码预设，保持相同的滤镜效果
                cmd_soft = [
                    'ffmpeg', '-y',
                    '-i', input_video_path,
                    '-vf', dedup_filters,
                    # 使用软件编码
                    '-c:v', 'libx264', '-preset', 'ultrafast', '-crf', '16',
                    # 设置较高的视频码率，保证高清质量
                    '-b:v', '5M', '-maxrate', '6M', '-bufsize', '12M',
                    # 随机设置GOP大小，在30-50之间
                    '-g', str(random.randint(30, 50)),
                    # 强制在随机时间点插入关键帧
                    '-force_key_frames', key_frame_expr,
                    # 随机场景分割，在0.25-0.35之间设置阈值
                    '-sc_threshold', str(random.uniform(0.25, 0.35)),
                    # 直接复制音频流，不做处理
                    '-c:a', 'copy',
                    # 保持视频帧率
                    '-fps_mode', 'passthrough',
                    temp_output
                ]
                
                print(f"执行备用编码去重命令: {' '.join(cmd_soft)}")
                result = subprocess.run(cmd_soft, stderr=subprocess.PIPE, stdout=subprocess.PIPE, text=True)
                
                if result.returncode != 0:
                    print(f"备用编码方式也失败: {result.stderr}")
                    # 如果仍然失败，直接复制原视频
                    import shutil
                    shutil.copy2(input_video_path, output_video_path)
                    print(f"由于去重失败，直接使用原视频: {input_video_path} -> {output_video_path}")
                    return False
            
            # 检查输出文件是否存在
            if not os.path.exists(temp_output):
                print(f"去重后的临时文件不存在: {temp_output}")
                # 直接复制原视频
                import shutil
                shutil.copy2(input_video_path, output_video_path)
                print(f"直接使用原视频: {input_video_path} -> {output_video_path}")
                return False
            
            # 复制到最终输出路径
            import shutil
            shutil.copy2(temp_output, output_video_path)
            print(f"视频去重处理完成: {output_video_path}")
            
            # 清理临时文件
            try:
                shutil.rmtree(temp_dir)
                print(f"清理临时文件夹: {temp_dir}")
            except:
                pass
                
            return True
            
        except Exception as e:
            print(f"视频去重处理时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            # 出错时直接复制原视频
            try:
                import shutil
                shutil.copy2(input_video_path, output_video_path)
                print(f"由于错误，直接使用原视频: {input_video_path} -> {output_video_path}")
            except:
                pass
            return False

    def _add_paper_texture(self, image):
        """为线稿添加纸质纹理或自定义背景"""
        print("添加背景...")
        
        # 创建图像副本
        result = image.copy()
        
        # 提取线稿（黑色像素）
        # 创建掩码，黑色线条区域为1，其他为0
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        _, mask = cv2.threshold(gray, 240, 255, cv2.THRESH_BINARY_INV)
        
        # 获取图像尺寸
        h, w = image.shape[:2]
        
        # 检查是否有自定义背景文件夹
        if self.background_folder and os.path.exists(self.background_folder):
            # 获取背景文件夹中的所有图片文件
            bg_files = [f for f in os.listdir(self.background_folder) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp'))]
            if bg_files:
                # 随机选择一个背景图片
                bg_file = random.choice(bg_files)
                bg_path = os.path.join(self.background_folder, bg_file)
                print(f"使用自定义背景图片: {bg_file}")
                
                try:
                    # 确保路径是绝对路径
                    bg_path = os.path.abspath(bg_path)
                    print(f"背景图片完整路径: {bg_path}")
                    
                    # 读取背景图片
                    paper_bg = cv2.imdecode(np.fromfile(bg_path, dtype=np.uint8), cv2.IMREAD_COLOR)
                    if paper_bg is None:
                        print(f"警告: OpenCV无法读取图片 {bg_path}，尝试使用PIL读取")
                        # 尝试使用PIL作为备选方案
                        from PIL import Image
                        pil_img = Image.open(bg_path)
                        paper_bg = np.array(pil_img.convert('RGB'))
                        # 转换颜色通道顺序从RGB到BGR（OpenCV使用BGR）
                        paper_bg = paper_bg[:, :, ::-1].copy()
                        
                    # 验证图片是否成功加载
                    if paper_bg is None or paper_bg.size == 0:
                        raise Exception(f"无法读取图片: {bg_path}")
                    
                    # 调整背景图片大小以匹配原图
                    paper_bg = cv2.resize(paper_bg, (w, h))
                    
                    # 将线稿(前景)与背景图片合并
                    # 对掩码进行扩展，避免边缘出现白边
                    kernel = np.ones((3, 3), np.uint8)
                    mask_dilated = cv2.dilate(mask, kernel, iterations=1)
                    
                    # 将背景图片应用到非线稿区域
                    result[mask_dilated == 0] = paper_bg[mask_dilated == 0]
                    
                    print("自定义背景应用完成")
                    return result
                except Exception as e:
                    print(f"应用自定义背景出错: {str(e)}，使用默认纸质纹理")
                    import traceback
                    traceback.print_exc()  # 打印详细的错误信息
            else:
                print(f"警告: 背景文件夹 {self.background_folder} 中没有找到有效图片，使用默认纸质纹理")
        else:
            if self.background_folder:
                print(f"警告: 背景文件夹 {self.background_folder} 不存在，使用默认纸质纹理")
        
        # 如果没有自定义背景或应用自定义背景失败，使用默认纸质纹理
        print("使用默认纸质纹理背景...")
        
        # 基础淡灰色背景 (220,220,220) - 更明显的灰色调
        paper_bg = np.ones((h, w, 3), dtype=np.uint8) * 220
        
        # 添加更强的随机噪声以模拟纸质纹理
        noise = np.random.randint(-15, 15, (h, w, 3), dtype=np.int16)
        
        # 创建纸质纹理的基本图案
        texture = np.zeros((h, w), dtype=np.uint8)
        
        # 添加细微纹理图案
        for y in range(0, h, 4):
            for x in range(0, w, 4):
                texture[y:min(y+4, height), x:min(x+4, width)] = np.random.randint(0, 8) 
        
        # 为纹理添加一些低频变化
        for i in range(30):  # 增加随机纹理图案数量
            x1, y1 = np.random.randint(0, width), np.random.randint(0, height)
            x2, y2 = np.random.randint(0, width), np.random.randint(0, height)
            radius = np.random.randint(100, max(200, min(h, w)//2))
            color_var = np.random.randint(-12, 8)  # 增加颜色变化范围
            
            # 在两点之间绘制平滑渐变
            cv2.line(paper_bg, (x1, y1), (x2, y2), (220+color_var, 220+color_var, 220+color_var), thickness=radius, lineType=cv2.LINE_AA)
        
        # 应用噪声和纹理
        for c in range(3):
            paper_bg[:,:,c] = np.clip(paper_bg[:,:,c].astype(np.int16) + noise[:,:,c] - texture//2, 200, 235).astype(np.uint8)
        
        # 应用轻微的模糊使纹理更自然
        paper_bg = cv2.GaussianBlur(paper_bg, (3, 3), 0)
        
        # 将线稿(前景)与纸质背景合并
        # 对掩码进行扩展，避免边缘出现白边
        kernel = np.ones((3, 3), np.uint8)
        mask_dilated = cv2.dilate(mask, kernel, iterations=1)
        
        # 将纸质背景应用到非线稿区域
        result[mask_dilated == 0] = paper_bg[mask_dilated == 0]
        
        print("纸质纹理背景应用完成")
        return result

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("AI自动绘画视频生成器 - 增强版")
        self.setGeometry(100, 100, 800, 700)  # 增加窗口高度以容纳新选项
        
        self.video_folder = ""
        self.audio_folder = ""
        self.output_folder = ""
        self.background_folder = ""  # 添加背景图片文件夹变量
        self.template_folder = ""  # 添加模板文件夹变量
        self.audio_volume = 0.5
        self.inference_steps = 25  # 默认推理步数
        self.use_controlnet = False   # 是否使用ControlNet
        self.controlnet_model_path = ""   # ControlNet模型路径
        self.drawing_method = "standard"  # 绘画方法：standard/astar/grease_pencil/controlnet
        self.delete_original_videos = False  # 是否删除原始视频
        
        # AI模型路径
        self.model_paths = {
            'sentiment_model': "",   # 图像分类模型
            'sd_model': ""          # Stable Diffusion模型
        }
        
        self.init_ui()
    
    def init_ui(self):
        # 创建主布局
        main_layout = QVBoxLayout()
        
        # 视频文件夹选择
        video_layout = QHBoxLayout()
        self.video_label = QLabel("视频A文件夹: 未选择")
        video_btn = QPushButton("选择视频文件夹")
        video_btn.clicked.connect(self.select_video_folder)
        video_layout.addWidget(self.video_label)
        video_layout.addWidget(video_btn)
        main_layout.addLayout(video_layout)
        
        # 音频文件夹选择
        audio_layout = QHBoxLayout()
        self.audio_label = QLabel("音频文件夹: 未选择")
        audio_btn = QPushButton("选择音频文件夹")
        audio_btn.clicked.connect(self.select_audio_folder)
        audio_layout.addWidget(self.audio_label)
        audio_layout.addWidget(audio_btn)
        main_layout.addLayout(audio_layout)
        
        # 输出文件夹选择
        output_layout = QHBoxLayout()
        self.output_label = QLabel("输出文件夹: 未选择")
        output_btn = QPushButton("选择输出文件夹")
        output_btn.clicked.connect(self.select_output_folder)
        output_layout.addWidget(self.output_label)
        output_layout.addWidget(output_btn)
        main_layout.addLayout(output_layout)
        
        # 背景图片文件夹选择
        background_layout = QHBoxLayout()
        self.background_label = QLabel("背景图片文件夹: 未选择 (默认使用生成纹理)")
        background_btn = QPushButton("选择背景图片文件夹")
        background_btn.clicked.connect(self.select_background_folder)
        background_layout.addWidget(self.background_label)
        background_layout.addWidget(background_btn)
        main_layout.addLayout(background_layout)
        
        # 模板文件夹选择
        template_folder_layout = QHBoxLayout()
        template_folder_label = QLabel("模板文件夹:")
        self.template_folder_path = QLineEdit()
        self.template_folder_path.setReadOnly(True)
        template_folder_btn = QPushButton("选择")
        template_folder_btn.clicked.connect(self.select_template_folder)
        template_folder_layout.addWidget(template_folder_label)
        template_folder_layout.addWidget(self.template_folder_path)
        template_folder_layout.addWidget(template_folder_btn)
        main_layout.addLayout(template_folder_layout)
        
        # AI模型选择组
        model_group = QGroupBox("AI模型选择")
        model_layout = QVBoxLayout()
        
        # 默认使用ControlNet
        self.use_controlnet = True
        
        # 设置默认绘画方法为标准方式
        self.drawing_method = "standard"
        
        # AI模型路径设置
        model_path_group = QGroupBox("AI模型路径设置")
        model_path_layout = QVBoxLayout()
        
        # 图像分类模型路径
        sentiment_layout = QHBoxLayout()
        sentiment_layout.addWidget(QLabel("图像分类模型路径 (可选):"))
        self.sentiment_path_edit = QLineEdit()
        self.sentiment_path_edit.setPlaceholderText("输入本地模型路径（可以留空）")
        sentiment_btn = QPushButton("浏览...")
        sentiment_btn.clicked.connect(lambda: self.select_model_path('sentiment_model'))
        sentiment_layout.addWidget(self.sentiment_path_edit)
        sentiment_layout.addWidget(sentiment_btn)
        model_path_layout.addLayout(sentiment_layout)
        
        # 添加ControlNet模型路径设置
        controlnet_layout = QHBoxLayout()
        controlnet_layout.addWidget(QLabel("ControlNet模型路径:"))
        self.controlnet_path_edit = QLineEdit()
        self.controlnet_path_edit.setPlaceholderText("输入ControlNet模型路径（如D:\\AIjieshuo\\models）")
        self.controlnet_path_edit.setEnabled(True)  # 启用ControlNet模型路径输入
        controlnet_btn = QPushButton("浏览...")
        controlnet_btn.clicked.connect(lambda: self.select_model_path('controlnet_model'))
        controlnet_layout.addWidget(self.controlnet_path_edit)
        controlnet_layout.addWidget(controlnet_btn)
        model_path_layout.addLayout(controlnet_layout)
        
        model_path_group.setLayout(model_path_layout)
        model_layout.addWidget(model_path_group)
        
        model_group.setLayout(model_layout)
        main_layout.addWidget(model_group)
        
        # 添加绘画风格选择组
        style_group = QGroupBox("英文绘画风格")
        style_layout = QVBoxLayout()
        
        # 添加绘画风格选项
        self.style_options = {
            "中国水墨": "masterful traditional Chinese ink painting, stroke by stroke, inspired by Zhang Daqian, delicate brush technique, professional artist, fine line work, meticulous detail",
            "钢笔细描": "professional pen and ink illustration, fine hatching technique, inspired by Albrecht Dürer, master artist, detailed crosshatching, precise linework, deliberate strokes",
            "炭笔素描": "exquisite charcoal sketch, chiaroscuro technique, inspired by Leonardo da Vinci, master artist, dramatic shading, rich texture, precise details, expressive lines",
            "日式浮世绘": "traditional brush painting, precise brushwork, inspired by Hokusai, flowing lines, rhythmic strokes, expert technique, artistic mastery, detailed ink work",
            "油画肖像": "professional portrait painting, refined brushwork, inspired by John Singer Sargent, masterful technique, artistic excellence, detailed rendering, expressive strokes",
            "铅笔写实": "hyperrealistic pencil drawing, professional technique, inspired by Paul Cadden, photorealistic detail, master artist, meticulous shading, layered graphite work"
        }
        
        # 添加风格复选框
        self.style_checkboxes = []
        
        for style_name in self.style_options.keys():
            checkbox = QCheckBox(style_name)
            checkbox.setChecked(True)  # 默认全选
            self.style_checkboxes.append(checkbox)
            style_layout.addWidget(checkbox)
        
        style_group.setLayout(style_layout)
        main_layout.addWidget(style_group)
        
        # 添加说明标签
        note_label = QLabel("注意: 使用ControlNet线稿控制，需英文提示词")
        note_label.setStyleSheet("color: red;")
        main_layout.addWidget(note_label)
        
        # 添加模板合并勾选框
        template_merge_layout = QHBoxLayout()
        self.use_template_merge = QCheckBox("模板合并 (将模板视频前2帧与处理后视频合并)")
        self.use_template_merge.setChecked(False)
        template_merge_layout.addWidget(self.use_template_merge)
        main_layout.addLayout(template_merge_layout)
        
        # 添加删除原始视频选项
        delete_videos_layout = QHBoxLayout()
        self.delete_videos_checkbox = QCheckBox("处理完成后删除视频A文件夹中的原始视频")
        self.delete_videos_checkbox.setChecked(False)
        self.delete_videos_checkbox.stateChanged.connect(self.toggle_delete_videos)
        delete_videos_layout.addWidget(self.delete_videos_checkbox)
        main_layout.addLayout(delete_videos_layout)
        
        # 音量控制
        volume_layout = QHBoxLayout()
        volume_label = QLabel("副音频音量:")
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setMinimum(0)
        self.volume_slider.setMaximum(100)
        self.volume_slider.setValue(50)
        self.volume_slider.valueChanged.connect(self.update_volume)
        self.volume_value_label = QLabel("50%")
        volume_layout.addWidget(volume_label)
        volume_layout.addWidget(self.volume_slider)
        volume_layout.addWidget(self.volume_value_label)
        main_layout.addLayout(volume_layout)
        
        # 添加 AI模型步数控制
        steps_layout = QHBoxLayout()
        steps_label = QLabel("AI绘画步数:")
        self.steps_slider = QSlider(Qt.Horizontal)
        self.steps_slider.setMinimum(10)
        self.steps_slider.setMaximum(50)
        self.steps_slider.setValue(25)  # 默认值为25步
        self.steps_slider.valueChanged.connect(self.update_steps)
        self.steps_value_label = QLabel("25步 (平衡质量和速度)")
        steps_layout.addWidget(steps_label)
        steps_layout.addWidget(self.steps_slider)
        steps_layout.addWidget(self.steps_value_label)
        main_layout.addLayout(steps_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        main_layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        main_layout.addWidget(self.status_label)
        
        # 开始处理按钮
        self.process_btn = QPushButton("开始处理")
        self.process_btn.clicked.connect(self.start_processing)
        self.process_btn.setEnabled(False)
        main_layout.addWidget(self.process_btn)
        
        # 设置主窗口部件
        central_widget = QWidget()
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)
    
    # 移除on_model_selected和on_drawing_method_selected方法
    
    def select_model_path(self, model_type):
        """选择模型路径"""
        if model_type == 'sentiment_model':
            title = "选择图像分类模型路径"
            edit_field = self.sentiment_path_edit
        elif model_type == 'controlnet_model':
            title = "选择ControlNet模型路径"
            edit_field = self.controlnet_path_edit
            
        folder = QFileDialog.getExistingDirectory(self, title)
        if folder:
            if model_type == 'controlnet_model':
                self.controlnet_model_path = folder
                edit_field.setText(folder)
            else:
                self.model_paths[model_type] = folder
                edit_field.setText(folder)
            self.check_ready()
    
    def select_video_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择视频A文件夹")
        if folder:
            self.video_folder = folder
            self.video_label.setText(f"视频A文件夹: {os.path.basename(folder)}")
            
            # 自动设置同名输出文件夹
            self.output_folder = os.path.join(os.path.dirname(folder), os.path.basename(folder) + "_output")
            self.output_label.setText(f"输出文件夹: {os.path.basename(self.output_folder)}")
            
            self.check_ready()
    
    def select_audio_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择音频文件夹")
        if folder:
            self.audio_folder = folder
            self.audio_label.setText(f"音频文件夹: {os.path.basename(folder)}")
            self.check_ready()
    
    def select_output_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择输出文件夹")
        if folder:
            self.output_folder = folder
            self.output_label.setText(f"输出文件夹: {os.path.basename(self.output_folder)}")
            self.check_ready()
    
    def select_background_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择背景图片文件夹")
        if folder:
            # 检查文件夹中是否有图片文件
            image_files = [f for f in os.listdir(folder) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp'))]
            if not image_files:
                QMessageBox.warning(self, "警告", "选择的文件夹中没有找到图片文件(支持.png, .jpg, .jpeg, .bmp)")
                return
                
            self.background_folder = folder
            self.background_label.setText(f"背景图片文件夹: {os.path.basename(folder)} ({len(image_files)}张图片)")
            self.check_ready()
    
    def select_template_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择模板文件夹")
        if folder:
            self.template_folder = folder
            self.template_folder_path.setText(folder)
            self.check_ready()
    
    def update_volume(self, value):
        self.audio_volume = value / 100.0
        self.volume_value_label.setText(f"{value}%")
    
    def update_steps(self, value):
        description = ""
        if value <= 15:
            description = "（速度优先，质量较低）"
        elif value <= 25:
            description = "（平衡质量和速度）"
        elif value <= 35:
            description = "（质量优先，速度较慢）"
        else:
            description = "（最高质量，非常慢）"
            
        self.steps_value_label.setText(f"{value}步 {description}")
        self.inference_steps = value  # 存储步数值
        
    def toggle_delete_videos(self, state):
        """处理是否删除原始视频的勾选框状态变化"""
        self.delete_original_videos = state == Qt.Checked
        if self.delete_original_videos:
            # 显示警告对话框
            reply = QMessageBox.warning(
                self,
                "警告",
                "启用此选项将在处理完成后删除视频A文件夹中的原始视频文件，此操作不可恢复！\n\n确定要启用此选项吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                # 用户选择取消，恢复勾选框状态
                self.delete_videos_checkbox.setChecked(False)
                self.delete_original_videos = False
    
    def check_ready(self):
        """检查是否所有必要的设置都已完成"""
        if self.video_folder and self.audio_folder and self.output_folder:
            # 检查模型路径
            models_ready = bool(self.controlnet_model_path)
            
            # 检查模板文件夹（如果勾选了模板合并选项）
            template_ready = True
            if hasattr(self, 'use_template_merge') and self.use_template_merge.isChecked():
                template_ready = bool(self.template_folder and os.path.exists(self.template_folder))
                if not template_ready:
                    self.status_label.setText("错误：启用模板合并功能时必须选择有效的模板文件夹")
                
            self.process_btn.setEnabled(models_ready and template_ready)
        else:
            self.process_btn.setEnabled(False)
    
    def start_processing(self):
        if not os.path.exists(self.video_folder):
            self.status_label.setText("错误：视频文件夹不存在")
            return
            
        if not os.path.exists(self.audio_folder):
            self.status_label.setText("错误：音频文件夹不存在")
            return
            
        # 检查模板文件夹（如果启用了模板合并）
        if self.use_template_merge.isChecked() and (not self.template_folder or not os.path.exists(self.template_folder)):
            self.status_label.setText("错误：启用模板合并功能时必须选择有效的模板文件夹")
            return
        
        # 检查ControlNet模型路径是否有效
        if not self.controlnet_model_path:
            self.status_label.setText("错误：必须设置ControlNet模型路径")
            return
        if not os.path.exists(self.controlnet_model_path):
            self.status_label.setText("错误：ControlNet模型路径无效")
            return
        
        # 检查可选模型路径
        sentiment_path = self.model_paths.get('sentiment_model', '')
        if sentiment_path and not os.path.exists(sentiment_path):
            self.status_label.setText("警告：图像分类模型路径无效，将使用基本方法")
            self.model_paths['sentiment_model'] = ""
        
        # 获取选中的绘画风格提示词
        selected_styles = []
        for i, checkbox in enumerate(self.style_checkboxes):
            if checkbox.isChecked():
                style_name = list(self.style_options.keys())[i]
                selected_styles.append(self.style_options[style_name])
        if not selected_styles:
            self.status_label.setText("警告：未选择任何绘画风格，将使用所有风格")
            selected_styles = list(self.style_options.values())
        
        # 禁用按钮，防止重复点击
        self.process_btn.setEnabled(False)
        self.status_label.setText("正在处理...")
        
        # 创建并启动处理线程
        self.thread = VideoProcessingThread(
            self.video_folder, 
            self.audio_folder, 
            self.output_folder,
            self.audio_volume,
            self.model_paths,
            selected_styles,
            self.inference_steps,
            self.use_controlnet,
            self.controlnet_model_path,
            self.drawing_method,
            self.background_folder,
            self.delete_original_videos,
            self.template_folder,
            self.use_template_merge.isChecked()
        )
        self.thread.progress_update.connect(self.update_progress)
        self.thread.processing_complete.connect(self.processing_finished)
        self.thread.start()
    
    def update_progress(self, value):
        self.progress_bar.setValue(value)
    
    def processing_finished(self, message):
        self.status_label.setText(message)
        self.process_btn.setEnabled(True)

if __name__ == "__main__":
    # 检查是否安装了必要的依赖
    try:
        result = subprocess.run(['ffmpeg', '-version'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        if result.returncode != 0:
            print("错误：未安装FFmpeg或FFmpeg未添加到系统路径")
            print("请安装FFmpeg: https://ffmpeg.org/download.html")
            sys.exit(1)
    except FileNotFoundError:
        print("错误：未安装FFmpeg或FFmpeg未添加到系统路径")
        print("请安装FFmpeg: https://ffmpeg.org/download.html")
        sys.exit(1)
    
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())