import os
import torch
import argparse
from diffusers import ControlNetModel
from huggingface_hub import hf_hub_download
import shutil
from pathlib import Path

def convert_controlnet_pth_to_diffusers(pth_path, output_dir, model_name=None):
    """
    将ControlNet的.pth模型文件转换为diffusers格式
    
    参数:
        pth_path: .pth文件路径
        output_dir: 输出目录
        model_name: 模型名称，如果为None则从文件名提取
    """
    print(f"开始转换ControlNet模型: {pth_path}")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 如果未提供模型名称，从文件名提取
    if model_name is None:
        model_name = os.path.basename(pth_path).replace(".pth", "")
    
    # 创建模型特定的输出目录
    model_output_dir = os.path.join(output_dir, f"temp_{model_name}")
    os.makedirs(model_output_dir, exist_ok=True)
    
    print(f"模型名称: {model_name}")
    print(f"输出目录: {model_output_dir}")
    
    try:
        # 下载yaml配置文件
        yaml_path = pth_path.replace(".pth", ".yaml")
        if not os.path.exists(yaml_path):
            print(f"警告: 未找到对应的yaml配置文件: {yaml_path}")
            print("尝试在同目录查找其他yaml文件...")
            
            # 查找同目录下的yaml文件
            dir_path = os.path.dirname(pth_path)
            yaml_files = [f for f in os.listdir(dir_path) if f.endswith('.yaml')]
            
            if yaml_files:
                yaml_path = os.path.join(dir_path, yaml_files[0])
                print(f"使用找到的yaml文件: {yaml_path}")
            else:
                print("未找到任何yaml文件，将使用默认配置")
        
        # 使用ControlNetModel.from_single_file进行转换
        print("开始模型转换过程...")
        controlnet = ControlNetModel.from_single_file(
            pth_path,
            torch_dtype=torch.float16
        )
        
        # 保存转换后的模型
        print(f"保存转换后的模型到: {model_output_dir}")
        controlnet.save_pretrained(model_output_dir)
        
        print(f"ControlNet模型转换完成: {model_name}")
        print(f"转换后的模型文件保存在: {model_output_dir}")
        
        # 检查转换后的文件
        converted_files = os.listdir(model_output_dir)
        print(f"转换后的文件列表: {converted_files}")
        
        return model_output_dir
    
    except Exception as e:
        print(f"转换过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    parser = argparse.ArgumentParser(description="ControlNet模型转换工具 - 将.pth文件转换为diffusers格式")
    parser.add_argument("--input", type=str, required=True, help="输入的.pth模型文件路径")
    parser.add_argument("--output", type=str, default="D:/AIjieshuo/mox", help="输出目录路径")
    parser.add_argument("--name", type=str, help="模型名称(可选，默认从文件名提取)")
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        return
    
    # 检查输入文件是否为.pth文件
    if not args.input.endswith(".pth"):
        print(f"错误: 输入文件不是.pth格式: {args.input}")
        return
    
    # 转换模型
    output_dir = convert_controlnet_pth_to_diffusers(args.input, args.output, args.name)
    
    if output_dir:
        print("\n转换成功！")
        print(f"转换后的模型位于: {output_dir}")
        print("\n您现在可以在自动绘画程序中使用这个转换后的模型了。")
    else:
        print("\n转换失败。请检查错误信息并重试。")

if __name__ == "__main__":
    print("ControlNet模型转换工具 - 将.pth文件转换为diffusers格式")
    print("=" * 60)
    main() 