import bpy
import math
import os

# 清空场景
bpy.ops.wm.read_factory_settings(use_empty=True)
scene = bpy.context.scene

# 设置分辨率为1920x1080
scene.render.engine = 'BLENDER_WORKBENCH'
scene.render.resolution_x = 1920
scene.render.resolution_y = 1080
scene.render.resolution_percentage = 100
scene.display.shading.light = 'FLAT'
scene.display.shading.color_type = 'MATERIAL'
scene.render.image_settings.color_mode = 'RGB'
scene.render.image_settings.color_depth = '8'
scene.render.film_transparent = False

# 修正：确保scene.world存在
if not scene.world:
    scene.world = bpy.data.worlds.new("World")
scene.world.use_nodes = False
scene.world.color = (1, 1, 1)

# 添加纸张纹理背景平面
bpy.ops.mesh.primitive_plane_add(size=2, location=(0, 0, 0))
plane = bpy.context.object
plane.scale[0] = 1920/1080  # 适配横屏比例

# 创建纸张材质
mat_bg = bpy.data.materials.new("PaperBG")
mat_bg.use_nodes = True
nodes = mat_bg.node_tree.nodes
links = mat_bg.node_tree.links
for n in nodes:
    nodes.remove(n)
tex_image = nodes.new(type='ShaderNodeTexImage')
img_path = "D:/wo/paper_texture.jpg"
if os.path.exists(img_path):
    tex_image.image = bpy.data.images.load(img_path)
else:
    print("未找到纸张纹理图片，将使用默认白色")
    tex_image.image = None
bsdf = nodes.new(type='ShaderNodeBsdfDiffuse')
output = nodes.new(type='ShaderNodeOutputMaterial')
links.new(tex_image.outputs['Color'], bsdf.inputs['Color'])
links.new(bsdf.outputs['BSDF'], output.inputs['Surface'])
plane.data.materials.append(mat_bg)

# 创建相机，正对平面
bpy.ops.object.camera_add(location=(0, 0, 2))
scene.camera = bpy.context.object
scene.camera.data.type = 'ORTHO'
scene.camera.data.ortho_scale = 1.0  # 横屏比例下正好全画面

# 创建Grease Pencil对象
bpy.ops.object.gpencil_add(location=(0, 0, 0.01))
gp_obj = bpy.context.object
gp_data = gp_obj.data

# 创建Grease Pencil材质
mat = bpy.data.materials.new("GP_Material")
bpy.data.materials.create_gpencil_data(mat)  # 关键：为材质添加Grease Pencil数据
mat.grease_pencil.color = (0, 0, 0, 1)  # 完全不透明黑色
gp_data.materials.append(mat)

# 创建图层
layer = gp_data.layers.new("Lines", set_active=True)
layer.lock = False
layer.hide = False
frame = layer.frames.new(1)

# 创建正方形
stroke = frame.strokes.new()
stroke.display_mode = '3DSPACE'
stroke.material_index = 0
stroke.line_width = 30
points = [(-0.3, -0.15, 0), (0.3, -0.15, 0), (0.3, 0.15, 0), (-0.3, 0.15, 0), (-0.3, -0.15, 0)]
stroke.points.add(count=len(points))
for i, co in enumerate(points):
    stroke.points[i].co = co

# 创建圆
stroke2 = frame.strokes.new()
stroke2.display_mode = '3DSPACE'
stroke2.material_index = 0
stroke2.line_width = 15
segments = 48
stroke2.points.add(count=segments)
radius_x = 0.22
radius_y = 0.12
for i in range(segments):
    angle = 2.0 * math.pi * i / segments
    x = radius_x * math.cos(angle)
    y = radius_y * math.sin(angle)
    stroke2.points[i].co = (x, y, 0)

# 渲染
scene.render.filepath = "D:/wo/test_output/frame_3.6.png"
bpy.ops.render.render(write_still=True) 